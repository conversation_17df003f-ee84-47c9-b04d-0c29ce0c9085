"use client"
import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { LoaderModal } from '@/components/core';
import { useCompanies } from '../../instant-web/misc/api';
import { assignCompanyId } from '../../analytics/misc/api';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';

const SelectAnalyticsCompany = () => {
  const { data: salesUserDetails } = useDefaultCompanyBranch()

  console.log(salesUserDetails, "sales details")
  const { data: companies } = useCompanies();
  const router = useRouter()

  useEffect(() => {
    if (companies) {
      if (companies.length > 0) {
        const firstCompany = companies[0];
        assignCompanyId(firstCompany.id);
        return router.replace(`/subscription?companyId=${salesUserDetails?.data.company_id || firstCompany.id}&companyName=${salesUserDetails?.data.company || firstCompany.company_name}`);
      }
      router.replace(`/subscription`);
    }
  }, [companies, router])


  return <LoaderModal isOpen />
};

export default SelectAnalyticsCompany;
