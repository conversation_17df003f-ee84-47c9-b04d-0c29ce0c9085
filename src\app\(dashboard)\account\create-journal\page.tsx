'use client'

import React from 'react'
// import { DropIcon } from '../misc/components/icons'
import { Button, ErrorModal, FormError, Input, LinkButton, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SuccessModal, Textarea } from '@/components/core'
import { UsePostDraftJournal, UsePostJournal } from '../misc/api/postJournal'
import { useForm, Controller, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { formatAxiosErrorMessage } from '@/utils/errors'
import { AxiosError } from 'axios'
import { useBooleanStateControl, useErrorModalState } from '@/hooks'
import AddRowDetails from '../misc/components/AddDetails/AddRowDetails'
import moment from 'moment'
import { useCompaniesList } from '../../instant-web/misc/api'
import { format } from 'date-fns'
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch'
import { useSearchParams } from 'next/navigation'
import { Spinner } from '@/icons/core'
// import { UseGetAccount } from '../misc/api/getAccountCharts'



const journalEntrySchema = z.object({
    journal: z.object({
        lines: z.array(
            z.object({
                account: z
                    .string({ required_error: 'Please select account' })
                    .trim()
                    .min(1, { message: 'Please select account' }),
                description: z
                    .string({ required_error: 'Please enter description.' })
                    .trim()
                    .min(1, { message: 'Please enter description.' }),
                customer: z
                    .string({ required_error: 'Please enter customer.' })
                    .trim()
                    .min(1, { message: 'Please enter description.' }),
                debit_amount: z.number().optional(),
                credit_amount: z.number().optional(),
                bal_before: z.number().optional(),
                bal_after: z.number().optional(),
                tax: z.number().optional(),
            })
        ),
        date: z.date(),
        // journal_number: z.string().min(1, "Journal number is required"),
        journal_type: z.string().default("CASH"),
        references: z.string().optional(),
        transaction_type: z.enum(["Debit", "Credit"]),
        currency: z.string().default("NGN"),
        notes: z.string().optional(),
        company: z.string().min(1, "enter a company")
    })
});

export type JournalEntryFormData = z.infer<typeof journalEntrySchema>;

const JournalEntryPage = () => {


    const {
        state: isSuccessModalOpen,
        setState: setSuccessModalState,
    } = useBooleanStateControl();

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();


    const { data: allCompanies, isLoading: isCompanyLoading } = useCompaniesList()


    const get_all_company_list = allCompanies?.results?.map(({ id: company_id, company_name }: { id: string; company_name: string }) => ({
        company_id,
        company_name,
    }))

    const { data: salesUserDetails, isLoading } = useDefaultCompanyBranch();

    const params = useSearchParams();

    const companyname = params.get('company') || salesUserDetails?.data.company_id;

    const { mutate: createJournal } = UsePostJournal()

    const { mutate: draftJournal } = UsePostDraftJournal()

    const { control, handleSubmit, register, setValue, formState: { errors }, getValues } = useForm<JournalEntryFormData>({
        resolver: zodResolver(journalEntrySchema),
        defaultValues: {
            journal: {
                company: `${companyname}`,
                journal_type: "CASH",
                currency: "NGN",
                date: new Date(),
                references: '',
                transaction_type: "Debit",
                notes: '',
                lines: [
                    {
                        account: '',
                        description: '',
                        debit_amount: 0,
                        credit_amount: 0,
                        bal_after: 0,
                        bal_before: 0,
                        customer: '',
                    },
                ],
            },
        }
    });

    const handleSaveAsDraft = () => {
        const dataToSubmit = {
            ...getValues().journal,
            date: format(getValues().journal.date, 'yyyy-MM-dd'),
            is_draft: true,
        }

        draftJournal(
            { journal: dataToSubmit },
            {
                onSuccess: () => {
                    setSuccessModalState(true);
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    openErrorModalWithMessage((error as any).response.data[0] as string || errorMessage);
                },
            });


    }
    React.useEffect(() => {
        if (params.get('company')) {
            setValue('journal.company', params.get('company') as string)
        }
        else if (salesUserDetails?.data.company_id) {
            setValue('journal.company', salesUserDetails?.data.company_id)
        }

    }, [isLoading, salesUserDetails, companyname, params, setValue])

    const {
        fields: journalsFields,
        append: appendjournalsField,
        remove: removejournalsField,
    } = useFieldArray({
        name: 'journal.lines',
        control: control,
    });

    const onSubmit = (data: JournalEntryFormData) => {

        const dataToSubmit = {
            ...data.journal,
            date: format(data.journal.date, 'yyyy-MM-dd'),
        }

        createJournal(
            { journal: dataToSubmit },
            {
                onSuccess: () => {
                    setSuccessModalState(true);
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    openErrorModalWithMessage((error as any).response.data[0] as string || errorMessage);
                },
            });
    };

    if (isCompanyLoading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <Spinner /> Loading...
            </div>
        )
    }


    return (
        <div className='mt-7 pb-20'>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className='lg:flex justify-between px-5 md:px-10'>
                    <div className='flex items-center gap-3pb-5'>
                        <h1 className='text-2xl font-medium text-[#242424] font-sans'>New Journal Entry</h1>
                    </div>
                    {/* <div className=' hidden lg:block'>
                        <div className='flex gap-2 mt-3 lg:mt-0'>
                            <Button
                                className='py-2 border-[#7E92D0] border-[.0187rem] bg-transparent text-[#667085] whitespace-nowrap'>Save as draft</Button>
                            <Button
                                className=' flex items-center gap-1.5 text-sm whitespace-nowrap'>Save and Publish<DropIcon />
                            </Button>
                        </div>
                    </div> */}
                </div>
                <div className='px-5 md:px-11 mt-4'>
                    <p className='text-[#242424] font-medium'>Journal Details</p>
                    <article className='grid grid-cols-5 gap-4'>
                        <div className={`relative mt-3`}>
                            <label className="mb-1 text-xs">Date</label>
                            <Controller
                                control={control}
                                name="journal.date"
                                render={({ field: { onChange, value } }) => (
                                    <Input
                                        className="h-12 rounded-lg border-[.2992px] border-[#E2E8F0]"
                                        id="date"
                                        type="date"
                                        value={value ? moment(value).format('YYYY-MM-DD') : ''}
                                        onChange={(e) => {
                                            const dateString = e.target.value;
                                            const date = dateString ? moment(dateString, 'YYYY-MM-DD').toDate() : undefined;
                                            onChange(date);
                                        }}
                                    />
                                )}
                            />

                            {errors.journal && errors?.journal.date && <FormError errorMessage={errors?.journal.date.message} />}
                        </div>

                        <div className="relative mt-4">
                            <label className="mb-1 text-xs">Company</label>
                            <Controller
                                control={control}
                                name="journal.company"
                                render={({ field: { onChange, value, ref } }) => (
                                    <Select defaultValue={companyname} value={value} onValueChange={onChange}>
                                        <SelectTrigger
                                            className='h-12 rounded-lg border-[.2992px] border-[#E2E8F0]'
                                            iconClassName="fill-black"
                                            id="company"
                                            ref={ref}
                                        >
                                            <SelectValue className='text-black' placeholder="Select Company" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {get_all_company_list?.map(({ company_id, company_name }) => (
                                                <SelectItem key={company_id} value={company_id}>
                                                    {company_name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                )}
                            />
                            {errors.journal && errors?.journal.company && <FormError errorMessage={errors?.journal.company.message} />}
                        </div>

                        <div className={`relative mt-3`}>
                            <label className="mb-1 text-xs">Journal Type</label>
                            <Controller
                                control={control}
                                name="journal.journal_type"
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        className='h-12 rounded-lg border-[.2992px] border-[#E2E8F0]'
                                        defaultValue={'CASH'}
                                        placeholder='Journal type'
                                        type="text"
                                        disabled
                                    />
                                )}
                            />
                            {errors.journal && errors?.journal.journal_type && <FormError errorMessage={errors?.journal.journal_type.message} />}
                        </div>

                        <div className={`relative mt-3`}>
                            <label className="mb-1 text-xs">Reference</label>
                            <Controller
                                control={control}
                                name="journal.references"
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        className='h-12 rounded-lg border-[.2992px] border-[#E2E8F0]'
                                        placeholder='Reference'
                                        type="text"
                                    />
                                )}
                            />
                            {errors.journal && errors.journal.references && <FormError errorMessage={errors.journal.references.message} />}
                        </div>
                        <div className="relative mt-3">
                            <label className="mb-1 text-xs">Transaction Type</label>
                            <Controller
                                control={control}
                                name="journal.transaction_type"
                                render={({ field: { onChange, value, ref } }) => (
                                    <Select value={value} onValueChange={onChange}>
                                        <SelectTrigger
                                            className='h-12 rounded-lg border-[.2992px] border-[#E2E8F0]'
                                            iconClassName="fill-black"
                                            id="transaction_type"
                                            ref={ref}
                                        >
                                            <SelectValue className='text-black' placeholder="Select Statement Type" />
                                        </SelectTrigger>
                                        <SelectContent className='text-black'>
                                            {['Debit', 'Credit'].map(type => (
                                                <SelectItem key={type} value={type}>
                                                    {type}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                )}
                            />
                            {errors.journal && errors?.journal.transaction_type && <FormError errorMessage={errors?.journal.transaction_type.message} />}
                        </div>

                        <div className={`relative mt-3`}>
                            <label className="mb-1 text-xs">Currency</label>
                            <Controller
                                control={control}
                                name="journal.currency"
                                render={({ field }) => (
                                    <Input
                                        {...field}
                                        className='h-12 rounded-lg border-[.2992px] border-[#E2E8F0]'
                                        defaultValue={'NGN'}
                                        placeholder='Currency'
                                        type="text"
                                        disabled
                                    />
                                )}
                            />
                            {errors.journal && errors?.journal.currency && <FormError errorMessage={errors?.journal.currency.message} />}
                        </div>
                    </article>

                    <section className='border border-[#E9E9E9] rounded-lg px-5 py-6 mt-4'>
                        <div className='rounded-lg border-[.2992px] border-[#E2E8F0]'>
                            <AddRowDetails
                                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                //@ts-ignore
                                addRow={appendjournalsField}
                                control={control}
                                deleteRow={removejournalsField}
                                errors={errors}
                                journalsFields={journalsFields}
                                register={register}
                                setValue={setValue}
                            />
                        </div>
                    </section>

                    <div className={`relative mt-3`}>
                        <label className="mb-1 text-xs">Add a note</label>
                        <Controller
                            control={control}
                            name={'journal.notes'}
                            render={({ field }) => (
                                <Textarea
                                    {...field}
                                    className='rounded-lg border-[.2992px] border-[#E2E8F0] text-left resize-none'
                                    placeholder="Notes"
                                    rows={8}
                                    onChange={(e) => field.onChange(e.target.value)}
                                />
                            )}
                        />
                        {errors.journal && errors?.journal.notes && <FormError errorMessage={errors?.journal.notes.message} />}
                    </div>
                    <div className='flex gap-2 mt-3 lg:mt-5'>
                        <Button
                            className='flex items-center gap-1.5 text-sm whitespace-nowrap'
                            type="submit"
                        >
                            Save and Publish
                        </Button>
                        <Button
                            className='py-2 border-[#7E92D0] border-[.0187rem] bg-transparent text-[#667085] whitespace-nowrap'
                            type="button"
                            onClick={handleSaveAsDraft}
                        >
                            Save as draft
                        </Button>
                    </div>
                </div>

            </form>
            {isSuccessModalOpen && <SuccessModal
                heading="Journal Created Successfully"
                isSuccessModalOpen={isSuccessModalOpen}
                setSuccessModalState={setSuccessModalState}
                subheading="">
                <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
                    <LinkButton
                        className="grow text-base"
                        href="/account/journal-entry"
                        size="fullWidth"
                    >
                        Okay
                    </LinkButton>
                </div>
            </SuccessModal>}

            {isErrorModalOpen &&
                <ErrorModal
                    isErrorModalOpen={isErrorModalOpen}
                    setErrorModalState={setErrorModalState}
                    subheading={errorModalMessage || 'Please check your inputs and try again.'}
                >
                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                        <Button
                            className="grow bg-red-950 text-base"
                            size="lg"
                            onClick={closeErrorModal}
                        >
                            Okay
                        </Button>
                    </div>
                </ErrorModal>}

        </div>

    )
}

export default JournalEntryPage

