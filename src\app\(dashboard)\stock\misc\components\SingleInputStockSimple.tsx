'use client';

import { format, isBefore, startOfDay } from 'date-fns';
import React, { ChangeEvent, useState } from 'react';
import Image from 'next/image';
import {
  Control,
  Controller,
  FieldArrayWithId,
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';

import {
  FormError,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SingleDatePicker,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Button,
} from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils/classNames';

import { StockOutListsSimple } from '../types';
import { StockCategoryCombobox } from './StockCategoryCombobox';
import { StockSubCategoryCombobox } from './StockSubCategoryCombobox';
import { Label } from '@radix-ui/react-label';
import { StockOutSchema } from './InputStockSimpleTab';
import { ProductCombobox } from './ProductCombobox';
import { SupplierCombobox } from './SupplierCombobox';

type SingleInputStockProps = {
  control: Control<StockOutListsSimple>;
  setValue: UseFormSetValue<StockOutListsSimple>;
  index: number;
  companyId: string;
  register: UseFormRegister<StockOutListsSimple>;
  errors: FieldErrors<StockOutListsSimple>;
  stock: FieldArrayWithId<StockOutSchema, "stocks", "id">;
  removeRow: (id?: number) => void;
};
export const SingleInputStockSimple = ({
  control,
  setValue,
  errors,
  index,
  companyId,
  register,
  stock,
  removeRow,
}: SingleInputStockProps) => {
  const {
    state: isConfirmExpiredModalOpen,
    setState: setConfirmExpiredModalState,
    setTrue: openConfirmExpiredModal,
    setFalse: closeConfirmExpiredModal,
  } = useBooleanStateControl();

  const { category = '' } = useWatch({
    control,
    name: `stocks.${index}`,
  }) || {};

  const selectedDateExpired = (selectedDate: string | Date | undefined) => {
    if (!selectedDate) {
      return null;
    }
    const inputDate = new Date(selectedDate);
    const currentDate = new Date();

    return isBefore(startOfDay(inputDate), startOfDay(currentDate));
  };

  const [expiredDate, setExpiredDate] = useState('');
  const [continueWithSelectedDate, setContinueWithSelectedDate] = useState(false);

  const [excelFileBlob, setExcelFileBlob] = useState<File>();

  const handleImageChange = async function async(e: ChangeEvent<HTMLInputElement>) {
    e.preventDefault();
    setExcelFileBlob(undefined);
    const uploadedFile = e.target.files?.[0];
    if (e.target.files && uploadedFile) {
      setExcelFileBlob(uploadedFile);
      setValue(`stocks.${index}.image`, uploadedFile)
    }
    // Reset the file input value to allow re-uploading the same file
    if (e.target.files?.[0]) e.target.value = '';
  };

  return (
    <tr>
      <td>
        <Button
          className='p-0'
          size={'tiny'}
          variant={'unstyled'}
          onClick={() => removeRow(index)}
        >
          <svg fill="none" height="16" viewBox="0 0 16 16" width="16" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.6003 5.89609C11.5923 5.89609 11.5803 5.89609 11.5683 5.89609C9.45231 5.68409 7.34031 5.60409 5.24831 5.81609L4.43231 5.89609C4.26431 5.91209 4.11631 5.79209 4.10031 5.62409C4.08431 5.45609 4.20431 5.31209 4.36831 5.29609L5.18431 5.21609C7.31231 5.00009 9.46831 5.08409 11.6283 5.29609C11.7923 5.31209 11.9123 5.46009 11.8963 5.62409C11.8843 5.78009 11.7523 5.89609 11.6003 5.89609Z" fill="#FF0000" />
            <path d="M6.6001 5.49112C6.5841 5.49112 6.5681 5.49112 6.5481 5.48712C6.3881 5.45912 6.2761 5.30312 6.3041 5.14312L6.3921 4.61912C6.4561 4.23512 6.5441 3.70312 7.4761 3.70312H8.5241C9.4601 3.70312 9.5481 4.25512 9.6081 4.62312L9.6961 5.14312C9.7241 5.30712 9.6121 5.46312 9.4521 5.48712C9.2881 5.51512 9.1321 5.40312 9.1081 5.24312L9.0201 4.72312C8.9641 4.37512 8.9521 4.30712 8.5281 4.30712H7.4801C7.0561 4.30712 7.0481 4.36312 6.9881 4.71912L6.8961 5.23912C6.8721 5.38712 6.7441 5.49112 6.6001 5.49112Z" fill="#FF0000" />
            <path d="M9.28425 12.303H6.71625C5.32025 12.303 5.26425 11.531 5.22025 10.907L4.96025 6.87896C4.94825 6.71496 5.07625 6.57096 5.24025 6.55896C5.40825 6.55096 5.54825 6.67496 5.56025 6.83896L5.82025 10.867C5.86425 11.475 5.88025 11.703 6.71625 11.703H9.28425C10.1243 11.703 10.1403 11.475 10.1803 10.867L10.4403 6.83896C10.4523 6.67496 10.5963 6.55096 10.7603 6.55896C10.9243 6.57096 11.0523 6.71096 11.0403 6.87896L10.7803 10.907C10.7363 11.531 10.6803 12.303 9.28425 12.303Z" fill="#FF0000" />
            <path d="M8.66423 10.1039H7.33223C7.16823 10.1039 7.03223 9.96791 7.03223 9.80391C7.03223 9.63991 7.16823 9.50391 7.33223 9.50391H8.66423C8.82823 9.50391 8.96423 9.63991 8.96423 9.80391C8.96423 9.96791 8.82823 10.1039 8.66423 10.1039Z" fill="#FF0000" />
            <path d="M9.00019 8.50234H7.0002C6.8362 8.50234 6.7002 8.36634 6.7002 8.20234C6.7002 8.03834 6.8362 7.90234 7.0002 7.90234H9.00019C9.16419 7.90234 9.30019 8.03834 9.30019 8.20234C9.30019 8.36634 9.16419 8.50234 9.00019 8.50234Z" fill="#FF0000" />
            <rect fill="white" fillOpacity="0.1" height="15.8" rx="3.9" stroke="#CFCFCF" strokeWidth="0.2" width="15.8" x="0.1" y="0.1" />
          </svg>
        </Button>
      </td>
      <td>
        {index + 1}
      </td>
      <td>
        <Label
          className="flex w-full cursor-pointer items-center justify-center gap-2"
          htmlFor={`product-${index}`}
        >
          <div>
            {(excelFileBlob || stock.image) ?
              <Image
                alt={`stock-${index}`}
                height={36}
                src={excelFileBlob ? URL.createObjectURL(excelFileBlob as Blob) : stock.image as string}
                width={36}
              />
              :
              <svg fill="none" height="36" viewBox="0 0 36 36" width="36" xmlns="http://www.w3.org/2000/svg">
                <rect height="35.6" rx="3.8" stroke="#032282" strokeDasharray="2 2" strokeWidth="0.4" transform="matrix(-1 0 0 1 35.6 0)" width="35.6" x="-0.2" y="0.2" />
                <path d="M14 18H22C22.205 18 22.375 17.83 22.375 17.625C22.375 17.42 22.205 17.25 22 17.25H14C13.795 17.25 13.625 17.42 13.625 17.625C13.625 17.83 13.795 18 14 18Z" fill="#032282" />
                <path d="M18 14V22C18 22.205 17.83 22.375 17.625 22.375C17.42 22.375 17.25 22.205 17.25 22V14C17.25 13.795 17.42 13.625 17.625 13.625C17.83 13.625 18 13.795 18 14Z" fill="#032282" />
              </svg>
            }
          </div>
          <input
            accept="image/*"
            className="hidden"
            id={`product-${index}`}
            type="file"
            onChange={handleImageChange}
          />
        </Label>
        {errors?.stocks?.[index]?.image && (
          <FormError errorMessage={errors.stocks[index].image.message} />
        )}
      </td>
      <td>
        <Controller
          control={control}
          defaultValue={stock.category}
          name={`stocks.${index}.category`}
          render={({ field: { onChange, value } }) => (
            <StockCategoryCombobox
              companyId={companyId}
              defaultName={stock.category}
              id="category"
              placeholder="Category"
              value={value || stock.category}
              onChange={onChange}
            />
          )}
        />
        {errors?.stocks?.[index]?.category && (
          <FormError errorMessage={errors.stocks[index].category.message} />
        )}
      </td>
      <td>
        <Controller
          control={control}
          defaultValue={stock.supplied_by}
          name={`stocks.${index}.supplied_by`}
          render={({ field: { onChange, value } }) => (
            <SupplierCombobox
              companyId={companyId}
              defaultName={stock.supplied_by}
              id="supplied_by"
              placeholder="Supplier"
              value={value || stock.supplied_by}
              onChange={onChange}
            />
          )}
        />
        {errors?.stocks?.[index]?.supplied_by && (
          <FormError errorMessage={errors.stocks[index].supplied_by.message} />
        )}
      </td>
      <td>
        <Controller
          control={control}
          name={`stocks.${index}.item`}
          render={({ field: { onChange, value } }) => (
            <ProductCombobox
              categoryId={category}
              companyId={companyId}
              id="item"
              placeholder="Select Product"
              value={value}
              onChange={(e) => {
                onChange(e);
              }}
              onProductSelect={(selectedProduct) => {
                if (selectedProduct) {
                  setValue(`stocks.${index}.stock_price`, selectedProduct.product_price || 0, {
                    shouldValidate: true
                  });
                  setValue(`stocks.${index}.selling_price`, selectedProduct.selling_price || 0, {
                    shouldValidate: true
                  });
                  setValue(`stocks.${index}.vat`, selectedProduct.vat, {
                    shouldValidate: true
                  });
                }
              }}
            />
          )}
        />
        {errors?.stocks?.[index]?.item && (
          <FormError errorMessage={errors.stocks[index].item.message} />
        )}
      </td>
      <td>
        <Controller
          control={control}
          defaultValue={stock.subcategory}
          name={`stocks.${index}.subcategory`}
          render={({ field: { onChange, value } }) => (
            <StockSubCategoryCombobox
              categoryId={category}
              companyId={companyId}
              defaultName={stock.subcategory}
              id="subcategory"
              placeholder="Sub category"
              value={value || stock.subcategory}
              onChange={onChange}
            />
          )}
        />
        {errors?.stocks?.[index]?.subcategory && (
          <FormError errorMessage={errors.stocks[index].subcategory.message} />
        )}
      </td>
      <td>
        <Input className='px-1 text-center' defaultValue={stock.name} type="text" {...register(`stocks.${index}.name`)} />
        {errors?.stocks?.[index]?.name && (
          <FormError errorMessage={errors.stocks[index].name.message} />
        )}
      </td>
      <td>
        <Input className='px-1 text-center' defaultValue={stock.quantity} type="number" {...register(`stocks.${index}.quantity`)} />
        {errors?.stocks?.[index]?.quantity && (
          <FormError errorMessage={errors.stocks[index].quantity.message} />
        )}
      </td>
      <td>
        <Controller
          control={control}
          name={`stocks.${index}.expiry_date`}
          render={({ field: { /*onChange,*/ value } }) => (
            <SingleDatePicker
              className="border bg-white py-2.5 w-max"
              continueWithSelectedDate={continueWithSelectedDate || Boolean(stock.expiry_date)}
              defaultDate={stock.expiry_date ? new Date(stock.expiry_date) : undefined}
              id="expiry_date"
              placeholder="Set date"
              value={value}
              onChange={e => {
                const selectedDate = format(new Date(e), 'yyyy-MM-dd');
                setExpiredDate(selectedDate);
                const isExpired = selectedDateExpired(selectedDate);
                if (isExpired) {
                  openConfirmExpiredModal();
                } else {
                  setContinueWithSelectedDate(true);
                  setValue(`stocks.${index}.expiry_date`, selectedDate);
                }
              }}
            />
          )}
        />
        {errors?.stocks?.[index]?.expiry_date && (
          <FormError errorMessage={errors.stocks[index].expiry_date.message} />
        )}
        <Dialog
          open={isConfirmExpiredModalOpen}
          onOpenChange={setConfirmExpiredModalState}
        >
          <DialogTrigger className={cn('bg-transparent p-0')}></DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-base font-semibold">
                Expired Date
              </DialogTitle>
              <DialogClose className="ml-auto">Close</DialogClose>
            </DialogHeader>

            <DialogBody className="text-center">
              <div className="flex w-full flex-col items-center justify-center">
                <h1 className="text-base font-[800] leading-7 text-red-600 ">
                  Expired Date
                </h1>

                <p className="w-full text-sm text-[#6C727F]">
                  Based on your Date selection, your product has expired. Do
                  you want to continue?
                </p>
              </div>

              <div className="mt-4 flex w-full items-center justify-center gap-3">
                <DialogTrigger
                  className={cn('bg-transparent p-0 border-[0.3px] border-[#EF4444] px-14 py-[13px] text-smfont-semibold text-[#EF4444]')}
                  onClick={() => {
                    setContinueWithSelectedDate(false);
                    setExpiredDate('');
                  }}
                >
                  Cancel
                </DialogTrigger>
                <Button
                  className="px-14 py-[13px] text-sm font-semibold text-white"
                  onClick={() => {
                    setValue(`stocks.${index}.expiry_date`, expiredDate);
                    setExpiredDate('');
                    closeConfirmExpiredModal();
                    setContinueWithSelectedDate(true);
                  }}
                >
                  <span>Continue</span>
                </Button>
              </div>
            </DialogBody>
          </DialogContent>
        </Dialog>
      </td>
      <td>
        <Input className='px-1 text-center' defaultValue={stock.stock_alert} type="number" {...register(`stocks.${index}.stock_alert`)} />
        {errors?.stocks?.[index]?.stock_alert && (
          <FormError errorMessage={errors.stocks[index].stock_alert.message} />
        )}
      </td>
      <td>
        <Input className='px-1 text-center' defaultValue={stock.stock_price} type="number" disabled {...register(`stocks.${index}.stock_price`)} />
        {errors?.stocks?.[index]?.stock_price && (
          <FormError errorMessage={errors.stocks[index].stock_price.message} />
        )}
      </td>
      <td>
        <Input className='px-1 text-center' defaultValue={stock.selling_price} type="number" disabled {...register(`stocks.${index}.selling_price`)} />
        {errors?.stocks?.[index]?.selling_price && (
          <FormError errorMessage={errors.stocks[index].selling_price.message} />
        )}
      </td>
      <td>
        <Input
          className='px-1 text-center'
          defaultValue={stock.vat}
          id="vat"
          max={100}
          min={0}
          placeholder="0%"
          step="any"
          type="number"
          disabled
          onWheel={e => e.currentTarget.blur()}
          {...register(`stocks.${index}.vat`)}
        />
        {errors?.stocks?.[index]?.vat && (
          <FormError errorMessage={errors.stocks[index].vat.message} />
        )}
      </td>
      <td>
        <Controller
          control={control}
          defaultValue={stock.show_in_web_stores}
          name={`stocks.${index}.show_in_web_stores`}
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                id="show_in_web_stores"
                ref={ref}
              >
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {['Yes', 'No'].map(item => {
                  return (
                    <SelectItem key={item} value={item}>
                      {item}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.stocks?.[index]?.show_in_web_stores && (
          <FormError errorMessage={errors.stocks[index].show_in_web_stores.message} />
        )}
      </td>
      <td>
        <Input className='px-1 text-center' defaultValue={stock.description} type="text" {...register(`stocks.${index}.description`)} />
        {errors?.stocks?.[index]?.description && (
          <FormError errorMessage={errors.stocks[index].description.message} />
        )}
      </td>
      <style jsx>{`
        .table-container {
          overflow-x: auto;
          max-width: 100%;
          position: relative;
        }

        table {
          border-collapse: collapse;
          width: 100%;
          font-size: 12px;
        }

        thead th {
          position: sticky;
          top: 0;
          font-weight: 500;
        }

        tbody td,
        thead th {
          border: 0.8px solid #E2E8F0;
          padding: 16px 26px;
          background-color: #F9FAFB;
        }

        tbody tr:nth-child(even) {
          background-color: #ffffff;
        }

        tbody tr {
          background-color: #ffffff;
        }

        tbody tr:hover {
          background-color: #ffffff;
        }

        thead th:first-child,
        tbody td:first-child,
        thead th:nth-child(2),
        tbody td:nth-child(2),
        thead th:nth-child(3),
        tbody td:nth-child(3) {
          position: sticky;
          left: 0;
          background: #fff;
          border: 0.8px solid #E2E8F0;
          z-index: 1;
        }

        thead th:nth-child(2),
        tbody td:nth-child(2) {
          left: 70px;
        }

        thead th:nth-child(3),
        tbody td:nth-child(3) {
          left: 140px;
        }

        thead th:first-child,
        tbody td:first-child {
          z-index: 2;
        }
      `}</style>
    </tr>
  );
};

