"use client"
import { useParams } from 'next/navigation';
import React, { useState } from 'react'
import { transactionResult, useGetAccountDetails } from './api/fetchAccountDetails';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
// import { format as formatDate, parseISO } from 'date-fns';
import { addCommasToNumber } from '@/utils/numbers';
import { Button, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/core';
import WalletDetails from './components/WalletDetails';
// import {  WallertHistoryResult } from './api/fetchTransactionHistory';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import FilterIcon from '@/icons/core/FilterIcon';
import { cn } from '@/utils/classNames';
import { ExportWalletHistory } from './components/ExportWalletHistory';
import SuccessIcon from './components/icons/SuccessIcon';
import FaildIcon from './components/icons/FaildIcon';
import PendingIcon from './components/icons/PendingIcon';
import { useGetSingleCompanyDetails } from './api/fetchCompanyDetails';
import { DataTableExcelFormat } from '@/components/core/DataTableExcelFormat';
import moment from 'moment';

const Page = () => {
    const { id } = useParams();

    const [walletId, setWalletId] = useState<transactionResult>()
    const [fiterStatus, setFiterStatus] = useState("");

    const [showWalletDetails, setShowWalletDetails] = useState(false)
    const [{ pageIndex, pageSize }, setPagination] =
        React.useState<PaginationState>({
            pageIndex: 1,
            pageSize: 20,
        });
    const { data } = useGetSingleCompanyDetails(id as string)
    // eslint-disable-next-line no-console

    const fetchOptions = {
        pageIndex,
        pageSize,
        companyId: id as string,
        status: fiterStatus,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        account_type: data?.results[0]?.account_type ? data?.results[0]?.account_type : "SPEND_MGMT"

    };


    const checkStatus = (status: string) => {
        switch (status) {
            case "SUCCESSFUL":
                return <SuccessIcon />
            case "FAILED":
                return <FaildIcon />
            case "PENDING":
                return <PendingIcon />
            case "REVERSED":
                return <FaildIcon />
            case "IN_PROGRESS":
                return <PendingIcon />
            default:
                <PendingIcon />
                break;
        }
    }

    // const { data: acctDetails, isLoading, isFetching } = useGetWalletHistory(fetchOptions)

    const { data: acctDetails, isLoading, isFetching } = useGetAccountDetails(fetchOptions)
    const columns: ColumnDef<transactionResult>[] = [
        // {
        //     accessorKey: '',
        //     header: 'ID',
        //     cell: ({ row }) => row?.original?..id as string
        //     // return <span>{walletId}</span>;

        // },
        {
            accessorKey: 'transaction_type',
            header: 'Type',
            cell: ({ row }) => {
                const accountType = row.getValue('transaction_type') as string;
                return (<div className='flex items-center gap-x-3'>
                    <div className="">{checkStatus(row?.original?.status)}</div>
                    <span>{convertKebabAndSnakeToTitleCase(accountType)}</span></div>);
            },
        },
        {
            accessorKey: 'amount',
            header: 'Amount',
            cell: ({ row }) => {
                const amount = row.getValue('amount') as number;
                const formattedAmount = addCommasToNumber(amount);
                return <span>₦{formattedAmount}</span>;
            },
        },
        {
            accessorKey: 'balance_before',
            header: 'Balance before',
            cell: ({ row }) => {
                const amount = row.getValue('balance_before') as number;
                const formattedAmount = addCommasToNumber(amount);
                return <span>₦{formattedAmount}</span>;
            },
        },
        {
            accessorKey: 'balance_after',
            header: 'Balance After',
            cell: ({ row }) => {
                const amount = row.getValue('balance_after') as number;
                const formattedAmount = addCommasToNumber(amount);
                return <span>₦{formattedAmount}</span>;
            },
        },
        {
            accessorKey: 'commission',
            header: 'Commission',
            cell: ({ row }) => {
                const amount = row.getValue('commission') as number;
                const formattedAmount = addCommasToNumber(amount);
                return <span>₦{formattedAmount}</span>;
            },
        },
        {
            accessorKey: 'date_created',
            header: 'Date Created',
            cell: ({ row }) => {
                const date = row.getValue('date_created') as number;
                const formattedDate = moment(date)?.format("lll");
                return <span>{formattedDate ?? null}</span>;
            },
        },
        {
            accessorKey: 'status',
            header: 'Status',
            cell: ({ row }) => {
                const status = String(row.getValue('status'));
                const formatted = convertKebabAndSnakeToTitleCase(status);

                return (
                    <span
                        className={cn(
                            (status === 'PENDING' || status === 'IN_PROGRESS') &&
                            'text-[#FF9F2E]',
                            status === 'SUCCESSFUL' && 'text-[#00A37D]',
                            status === 'FAILED' && 'text-[#FF0000]',
                            status === 'REVERSED' && 'text-[#FF0000]',
                        )}
                    >
                        {formatted}
                    </span>
                );
            },
        },
        {
            accessorKey: 'beneficiary_account_name',
            header: 'Account Name',
            cell: ({ row }) => {
                const formattedData = row.getValue('beneficiary_account_name') as string;
                // const formattedDate = formatDate(parseISO(date), 'd/M/yyyy');
                return <span>{convertKebabAndSnakeToTitleCase(formattedData)}</span>;
            },
        },
        {
            id: 'actions',
            header: 'Action',
            cell: ({ row }) => (
                <Button
                    className="bg-[#F2F5FF] px-6 py-[10px] font-sans text-[#032282]"
                    onClick={() => {
                        setWalletId(row.original)
                        setShowWalletDetails(true)
                    }}
                >
                    View details
                </Button>
            ),
        },
    ];

    const { count, results } = acctDetails || {};

    const pageCount = React.useMemo(
        () => (count ? Math.ceil(count / pageSize) : -1),
        [count, pageSize]
    );


    return (
        <div className=' mx-10 my-3'>
            <div className="flex bg-white justify-between items-center w-full px-6  py-4">
                <div className="flex items-center gap-3 flex-wrap">
                    <p>Wallet history</p>
                    <div className="bg-[#F2F5FF] px-3 h-[1.8125rem] text-main-solid text-sm flex justify-center items-center rounded-md">{acctDetails?.count ?? 0}</div>
                </div>
                <div className="flex items-center gap-5">
                    <div className="">
                        <DropdownMenu>
                            <DropdownMenuTrigger>
                                <Button className="flex items-center outline-none bg-white border-[.05rem] text-[#556575] text-sm font-medium px-5 border-[#D6D6D6] gap-x-2">
                                    <FilterIcon /> Filter
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="min-w-[100px] px-4 bg-white rounded-md p-[5px] shadow-[0px_10px_38px_-10px_rgba(22,_23,_24,_0.35),_0px_10px_20px_-15px_rgba(22,_23,_24,_0.2)] will-change-[opacity,transform] data-[side=top]:animate-slideDownAndFade data-[side=right]:animate-slideLeftAndFade data-[side=bottom]:animate-slideUpAndFade data-[side=left]:animate-slideRightAndFade">
                                <DropdownMenuItem
                                    className="group text-[13px]  leading-none text-violet11 rounded-[3px] flex items-center h-[25px] relative cursor-pointer font-medium select-none outline-none data-[disabled]:text-mauve8 data-[disabled]:pointer-events-none data-[highlighted]:bg-violet9 data-[highlighted]:text-violet1"
                                    defaultValue={""}
                                    onClick={() => setFiterStatus("")}
                                >
                                    ALL
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    className="group text-[13px]  leading-none text-violet11 rounded-[3px] flex items-center h-[25px] relative cursor-pointer font-medium select-none outline-none data-[disabled]:text-mauve8 data-[disabled]:pointer-events-none data-[highlighted]:bg-violet9 data-[highlighted]:text-violet1"
                                    defaultValue={"Successful"}
                                    onClick={() => setFiterStatus("SUCCESSFUL")}



                                >
                                    SUCCESSFUL
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    className="group text-[13px] leading-none text-violet11 rounded-[3px] flex items-center h-[25px] relative cursor-pointer font-medium select-none outline-none data-[disabled]:text-mauve8 data-[disabled]:pointer-events-none data-[highlighted]:bg-violet9 data-[highlighted]:text-violet1"
                                    defaultValue={"FAILED"}
                                    onClick={() => setFiterStatus("FAILED")}
                                >
                                    FAILED
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    className="group text-[13px] leading-none text-violet11 rounded-[3px] flex items-center h-[25px] relative cursor-pointer font-medium select-none outline-none data-[disabled]:text-mauve8 data-[disabled]:pointer-events-none data-[highlighted]:bg-violet9 data-[highlighted]:text-violet1"
                                    defaultValue={"Failed"}
                                    onClick={() => setFiterStatus("IN_PROGRESS")}
                                >
                                    IN PROGRESS
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    className="group text-[13px] leading-none text-violet11 rounded-[3px] flex items-center h-[25px] relative cursor-pointer font-medium select-none outline-none data-[disabled]:text-mauve8 data-[disabled]:pointer-events-none data-[highlighted]:bg-violet9 data-[highlighted]:text-violet1"
                                    defaultValue={"Failed"}
                                    onClick={() => setFiterStatus("PENDING")}
                                >
                                    PENDING
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    className="group text-[13px] leading-none text-violet11 rounded-[3px] flex items-center h-[25px] relative cursor-pointer font-medium select-none outline-none data-[disabled]:text-mauve8 data-[disabled]:pointer-events-none data-[highlighted]:bg-violet9 data-[highlighted]:text-violet1"
                                    defaultValue={"Failed"}
                                    onClick={() => setFiterStatus("REVERSED")}
                                >
                                    REVERSED
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                    {results && results?.length > 0 && <div className="">
                        <ExportWalletHistory data={results} fileName='wallet-history' />
                    </div>}
                </div>
            </div>
            <div className="bg-white mt-3">
                <DataTableExcelFormat
                    columns={columns}
                    isFetching={isFetching}
                    isLoading={isLoading}
                    pageCount={pageCount}
                    pageIndex={pageIndex}
                    pageSize={pageSize}
                    rows={results}
                    setPagination={setPagination}
                />
            </div>

            {showWalletDetails && <WalletDetails
                isOpen={showWalletDetails}
                setIsOpen={setShowWalletDetails}
                walletId={walletId}
            />}
        </div>
    )
}

export default Page