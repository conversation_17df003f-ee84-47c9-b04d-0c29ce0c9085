'use client'


import { Button, DataTable, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, ErrorModal } from '@/components/core'
import React from 'react'
import { DeactivatedAccount, useGetDeactivatedAcc } from '../api/getDeactivatedAcc'
import { ColumnDef } from '@tanstack/react-table'
import ThreeDot from './icons/ThreeDot'
import toast from 'react-hot-toast'
import { formatAxiosErrorMessage } from '@/utils/errors'
import { AxiosError } from 'axios'
import { useActivateAccount } from '../api/activateAccount'
import { useErrorModalState } from '@/hooks'
import { useSearchParams } from 'next/navigation'
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch'
import { UseDeleteChart } from '../api/deleteAccountChart'

interface InactiveAccountsProps {
  globalFilter: string
}

const DeactivatedAccountsTable = ({ globalFilter }: InactiveAccountsProps) => {

  const {
    isErrorModalOpen,
    setErrorModalState,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { data: salesUserDetails } = useDefaultCompanyBranch();

  const params = useSearchParams();

  const companyId = params.get('company') || salesUserDetails?.data.company_id;

  const { data: deactivatedAcc, isLoading, isFetching, refetch } = useGetDeactivatedAcc(companyId as string)

  const { mutate: activateAccount } = useActivateAccount()

  const filteredAccounts = deactivatedAcc?.filter(account => {
    return (
      account.name?.toLowerCase().includes(globalFilter.toLowerCase() ?? '') ||
      account?.account_type_data.name?.toLowerCase().includes(globalFilter.toLowerCase() ?? '') ||
      account.account_code?.toLowerCase().includes(globalFilter.toLowerCase() ?? '') ||
      account.account_type?.toLowerCase().includes(globalFilter.toLowerCase() ?? '') ||
      account.statement_type?.toLowerCase().includes(globalFilter.toLowerCase() ?? '') ||
      account.statement_category?.toLowerCase().includes(globalFilter.toLowerCase() ?? '')
    );
  });

  const { mutate: deleteChartAccount } = UseDeleteChart()

  const handleDelete = async (id: string, name: string, description: string, account_type: string, company: string) => {
    deleteChartAccount(
      {
        id,
        data: {
          name,
          description,
          account_type,
          company
        }
      },
      {
        onSuccess: () => {
          toast.success('Chart Deleted successfully!');
          refetch()
        },
        onError: error => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          openErrorModalWithMessage((error as any).response.data[0] as string || errorMessage);
        },
      },
    )
  }

  const columns: ColumnDef<DeactivatedAccount>[] = [

    {
      accessorKey: 'account_code',
      header: 'Account Code',
    },
    {
      accessorKey: 'name',
      header: 'Account Name',
    },

    {
      accessorKey: 'account_type',
      header: 'Account Type',
      cell: ({ row }) => {
        return (
          <div>{row?.original?.account_type_data?.name}</div>
        )
      },
    },

    {
      accessorKey: 'statement_type',
      header: 'Statement Type',
    },

    {
      accessorKey: 'statement_category',
      header: 'Statement Category',
    },

    {
      accessorKey: 'account_type_data.standard_balance',
      header: 'Standard Balance',
    },

    {
      accessorKey: 'action',
      header: 'Action',
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="flex items-center outline-none bg-white text-[#556575] text-sm font-medium px-5 border-[#D6D6D6] gap-x-2">
                <ThreeDot />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="px-4 bg-white z-50 rounded-md p-[.3125rem] shadow-[0rem_.625rem_2.375rem_-0.625rem_rgba(22,_23,_24,_0.35),_0rem_.625rem_1.25rem_-0.9375rem_rgba(22,_23,_24,_0.2)]"
              sideOffset={5}
            >
              <DropdownMenuItem
                className="group text-[.8125rem] leading-none text-gray-700 rounded-[.1875rem] p-4 flex items-center h-[1.5625rem] cursor-pointer font-medium select-none outline-none hover:bg-gray-100"
                onClick={() => {
                  activateAccount(
                    { id: row.original.id },
                    {
                      onSuccess: () => {
                        toast.success('Account activated successfully!');
                      },
                      onError: (error) => {
                        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                        openErrorModalWithMessage(errorMessage as string);
                      },
                    }
                  );
                }}
              >
                Activate Account
              </DropdownMenuItem>
              <DropdownMenuItem
                className="group text-[.8125rem] leading-none text-gray-700 rounded-[.1875rem] p-4 flex items-center h-[1.5625rem] cursor-pointer font-medium select-none outline-none hover:bg-gray-100"
                onClick={() => {
                  const { name, description, account_type, company, id } = row.original;
                  handleDelete(id, name, description, account_type, company!);
                }}
              >
                Delete row
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      }
    }
  ]

  return (
    <>
      <div>
        <DataTable
          columns={columns}
          isFetching={isFetching}
          isLoading={isLoading}
          pageCount={10}
          pageIndex={1}
          pageSize={100}
          rows={filteredAccounts || []}
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          setPagination={() => { }}
        />
      </div>
      {
        <ErrorModal
          isErrorModalOpen={isErrorModalOpen}
          setErrorModalState={setErrorModalState}
          subheading={
            errorModalMessage || 'Please check your inputs and try again.'
          }
        ></ErrorModal>}
    </>
  )
}

export default DeactivatedAccountsTable