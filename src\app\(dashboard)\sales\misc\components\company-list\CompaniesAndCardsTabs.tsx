'use client';

import { <PERSON><PERSON>, LinkButton, LoaderModal } from '@/components/core';
import { addCommasToNumber } from '@/utils/numbers';
import { useGetAllCompaniesInfo } from '@/app/(dashboard)/stock/misc/api';
import { ICompanies, TokenBalanceInfo } from '@/app/(dashboard)/stock/misc/types';
import { ModuleSubscriptionModal } from '@/app/(dashboard)/payroll/misc/components/company-list/CompaniesAndCardsTabs';
import TokenBalanceCard from '@/app/(dashboard)/subscription/misc/components/TokenBalanceCard';
import TokenInsufficientModal from '../modals/TokenInsufficientModal';

import * as React from 'react';

import { useRouter } from 'next/navigation';

import { CompanyListItemDropdownMenu } from './CompanyListItemDropdownMenu';

// interface CompanyListItemProps {
//   id: string;
//   company_name: string;
//   branches: number;
//   stock_value: number;
//   total_stock: number;
//   is_subscription: boolean;
//   subscription_info?: SubscriptionInfo | null;
//   token_balance_info?: TokenBalanceInfo;
// }

function CompanyListItem({
  id,
  branches,
  company_name,
  total_stock,
  stock_value,
  is_subscription,
  is_free_trial,
  is_paid_subscription,
  active_subscriptions,
  not_started_subscriptions,
  token_balance_info
}: Omit<ICompanies, 'teams' | 'stock_on_hold'> & { token_balance_info?: TokenBalanceInfo }) {
  const [showModuleModal, setShowModuleModal] = React.useState(false);
  const [showTokenModal, setShowTokenModal] = React.useState(false);
  const [isHovered, setIsHovered] = React.useState(false);
  const router = useRouter();
  const hasNotStartedSubscriptions = not_started_subscriptions.length > 0;
  // const _hasActiveSubscriptions = active_subscriptions.length > 0;
  const hasSaleModule = active_subscriptions.some(sub =>
    sub.modules.some(module => module.module.code === 'SALES' && module.is_active)
  );

  // Check if user has sufficient tokens
  const hasTokens = token_balance_info && token_balance_info.current_balance > 0;

  const handleViewDetails = () => {
    // COMMENT FOR NOW
    // if (!hasSaleModule) {
    //   setShowModuleModal(true);
    //   return;
    // }

    // Check if user has tokens for token-based subscriptions
    if (!hasTokens) {
      setShowTokenModal(true);
      return;
    }

    router.push(`/sales/company-details/?company=${id}`);
  };
  const handleCreateBranch = () => {
    if (!hasSaleModule) {
      setShowModuleModal(true);
      return;
    }

    // Check if user has tokens for token-based subscriptions
    if (!hasTokens) {
      setShowTokenModal(true);
      return;
    }

    router.push(`/sales/create-branch?company=${id}`);
  };
  return (
    <li
      className="w-full rounded-10 border border-card-border p-3.5 shadow-card sm:p-5 xl:px-8 transition-all duration-300 hover:shadow-lg"
      key={id}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CompanyListItemDropdownMenu id={id} />

      <dl className="mb-4 grid grid-cols-3 gap-3">
        <div className="flex flex-col-reverse gap-1.5">
          <dt className="text-xxs text-label-text">Company</dt>
          <dd className="text-xs text-[#373737]">{company_name}</dd>
        </div>
        <div className="flex flex-col-reverse gap-1.5">
          <dt className="text-xxs text-label-text">Branches</dt>
          <dd className="text-xs text-[#373737]">{branches}</dd>
        </div>
        <div className="flex flex-col-reverse gap-1.5">
          <dt className="text-xxs text-label-text">Total stock</dt>
          <dd className="text-xs text-[#373737]">{total_stock ?? 0}</dd>
        </div>
      </dl>

      {/* Token Balance Section - Shows on Hover */}
      {token_balance_info && (
        <div
          className={`overflow-hidden transition-all duration-300 ease-in-out ${isHovered
            ? 'max-h-32 opacity-100 mb-4'
            : 'max-h-0 opacity-0 mb-0'
            }`}
        >
          <div className="pt-2">
            <TokenBalanceCard
              tokenBalanceInfo={token_balance_info}
              variant="compact"
            />
          </div>
        </div>
      )}

      <div className="flex flex-wrap gap-1.5 sm:grid sm:grid-cols-12 sm:gap-3">
        <div className="flex flex-col-reverse gap-1.5 col-span-4">
          <dt className="text-xxs text-label-text">Stock value</dt>
          <dd className="text-xs text-[#373737]">{addCommasToNumber(Number(stock_value ?? 0))}</dd>
        </div>
        <div className="col-span-8">
          {(is_subscription || is_free_trial || hasNotStartedSubscriptions) ?
            <div className="flex flex-wrap gap-1.5 sm:grid sm:grid-cols-2 sm:gap-3 ">
              <Button
                className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
                variant="light"
                onClick={handleViewDetails}
              >
                <span className="text-main-solid">View details</span>
              </Button>
              <Button
                className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
                variant="light"
                onClick={handleCreateBranch}
              >
                <span className="text-main-solid">Create Branch</span>
              </Button>

            </div> : <LinkButton
              className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
              href={`/subscription?companyId=${id}&companyName=${company_name}`}
              variant="light"
            >
              <span className="text-main-solid">Subscribe</span>
            </LinkButton>}
        </div>

      </div>
      <div className="flex flex-wrap gap-1.5 sm:grid sm:grid-cols-12 sm:gap-3 mt-2">
        <div className="text-xxs text-label-text col-span-4">Status</div>
        <div className="flex gap-1.5 col-span-8">
          <dd className="text-xs">
            {hasNotStartedSubscriptions ? (
              <span className="text-amber-600">Pending Payment</span>
            ) : is_free_trial ? (
              <span className="text-amber-600">Free Trial</span>
            ) : is_paid_subscription ? (
              <span className="text-green-600">Paid Subscription</span>
            ) : is_subscription ? (
              <span className="text-green-600">Subscribed</span>
            ) : (
              <span className="text-red-600">Not Subscribed</span>
            )}
          </dd>
        </div>
      </div>

      <ModuleSubscriptionModal
        company={{
          id,
          company_name,
          is_subscription,
          is_free_trial,
          is_paid_subscription,
          active_subscriptions,
          not_started_subscriptions
        }}
        isOpen={showModuleModal}
        ModuleName='SALES'
        onClose={() => setShowModuleModal(false)}
      />

      <TokenInsufficientModal
        companyId={id}
        companyName={company_name}
        currentBalance={token_balance_info?.current_balance || 0}
        isOpen={showTokenModal}
        module='Sales'
        onClose={() => setShowTokenModal(false)}
        onProceedAnyway={() => router.push(`/sales/company-details/?company=${id}`)}
      />

    </li>
  );
}

export function CompaniesList() {
  const fetchOptions = {
    pageIndex: 1,
  };
  const { data, isLoading } = useGetAllCompaniesInfo(fetchOptions);

  return (
    <div className="md:px-0" defaultValue="companies">
      <LoaderModal isOpen={isLoading} />
      <div className="mt-1 rounded-10 bg-white p-6 lg:py-8">
        <ul className="grid grid-cols-1 justify-start gap-4 [@media(min-width:1154px)]:grid-cols-2 [@media(min-width:1614px)]:grid-cols-3">
          {data?.companies?.map(
            ({
              id, branches,
              company_name,
              total_stock,
              stock_value, is_subscription, is_free_trial, is_paid_subscription, active_subscriptions, not_started_subscriptions, token_balance_info }) => (
              <CompanyListItem
                active_subscriptions={active_subscriptions}
                branches={branches}
                company_name={company_name}
                id={id}
                is_free_trial={is_free_trial}
                is_paid_subscription={is_paid_subscription}
                is_subscription={is_subscription}
                key={id}
                not_started_subscriptions={not_started_subscriptions}
                stock_value={stock_value}
                token_balance_info={token_balance_info}
                total_stock={total_stock}
              />
            )
          )}
        </ul>
      </div>
    </div>
  );
}
