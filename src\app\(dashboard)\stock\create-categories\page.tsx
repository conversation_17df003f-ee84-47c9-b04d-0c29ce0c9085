'use client'

import React from 'react';

import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { LoaderModal } from '@/components/core';
import { CreateCategoryModal } from '../misc/components/modals';

const Page = () => {
    const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();

    return (
        <>
            <LoaderModal isOpen={isCompanyLoading} />
            <CreateCategoryModal
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                companies={companies?.results || []} />
        </>
    );
};

export default Page;
