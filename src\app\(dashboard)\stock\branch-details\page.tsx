'use client'

import React, { useEffect } from 'react';
import Link from 'next/link';

import {
  LinkButton,
  LoaderModal,
  Tabs,
  TabsContent,
  <PERSON>bsList,
  TabsTrigger,
} from '@/components/core';
import { cn } from '@/utils/classNames';

import {
  BranchPriceListTable,
  BranchProductsTable,
  BranchStockHistoriesTable,
  StockCardsAndSummary,
  ViewBranchesDialog,
  UnlistedItemsTable,
} from '../misc/components';
import { StockIndexTable } from '../misc/tables';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCompaniesList } from '../../instant-web/misc/api';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';

export default function Stock() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const branch = searchParams.get('branch') || '';
  const company = searchParams.get('company') || '';

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  // if (salesUserDetails) {
  //   if (!company || (salesUserDetails?.data.company_id && (salesUserDetails?.data.company_id === company) && salesUserDetails?.data.branch_id)) {
  //     // return router.push(`/stock/stock-out?company=${salesUserDetails?.data.company_id}&companyName=${salesUserDetails?.data.company}&branch=${salesUserDetails?.data.branch_id}`);
  //   }
  // }



  useEffect(() => {
    if (!company && salesUserDetails?.data) {
      router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])


  return (
    <>
      <LoaderModal isOpen={isDefaultLoading || !company} />

      {branch ?
        <div className="md:px-7 md:py-3 lg:px-11">
          <div className="change__branch my-2 flex flex-wrap items-center ">
            <div className="flex flex-wrap items-center justify-between gap-3 p-4 lg:p-0">
              <LinkButton
                className={cn(
                  'rounded-lg bg-[#D6DFFE] px-4 py-2 text-xs font-normal text-[#032282] '
                )}
                href="#tables"
              >
                Scroll to Stocks
              </LinkButton>

              <Link
                className={cn(
                  'rounded-lg bg-[#D6DFFE] px-4 py-2 text-xs font-normal text-[#032282] '
                )}
                href={`/stock/create-branch?company=${company}`}
              >
                Create Branch
              </Link>
              <LinkButton
                className={cn(
                  'rounded-lg bg-[#D6DFFE] px-4 py-2 text-xs font-normal text-[#032282] '
                )}
                href={`/stock/create-categories?company=${company}`}
                variant={'light'}
              >
                Create Categories
              </LinkButton>

              <LinkButton
                className={cn(
                  'rounded-lg bg-[#D6DFFE] px-4 py-2 text-xs font-normal text-[#032282] '
                )}
                href={`/stock/add-product/multiple?company=${company}`}
                variant={'light'}
              >
                Add Products
              </LinkButton>
              <Link
                className={cn(
                  'rounded-lg bg-[#D6DFFE] px-4 py-2 text-xs font-normal text-[#032282] '
                )}
                href={`/stock/company-details?company=${company}`}
              >
                View all branches
              </Link>
            </div>
          </div>

          <StockCardsAndSummary />

          <div className="stocks__table" id="tables">
            <Tabs className="w-full" defaultValue="stocks">
              <TabsList className="my-4 grid grid-cols-1 gap-4 border-none bg-white md:grid-cols-2 lg:grid-cols-5">
                <TabsTrigger
                  className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                  value="stocks"
                >
                  <p>Stocks</p>
                </TabsTrigger>
                <TabsTrigger
                  className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                  value="items"
                >
                  <p>Items</p>
                </TabsTrigger>

                <TabsTrigger
                  className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                  value="price_list"
                >
                  <p>Price list</p>
                </TabsTrigger>

                <TabsTrigger
                  className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                  value="stock_history"
                >
                  <p>Stock History </p>
                </TabsTrigger>
                <TabsTrigger
                  className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                  value="unlisted_items"
                >
                  <p>Unlisted Items</p>
                </TabsTrigger>
              </TabsList>
              <div className="mt-[10px] w-full "></div>
              <TabsContent
                className="mt-2 h-screen w-full rounded-sm bg-white"
                value="stocks"
              >
                <StockIndexTable />
              </TabsContent>
              <TabsContent
                className="mt-2 h-screen w-full rounded-sm bg-white"
                value="items"
              >
                <BranchProductsTable />
              </TabsContent>

              <TabsContent
                className="mt-2 h-screen w-full rounded-sm bg-white"
                value="price_list"
              >
                <BranchPriceListTable />
              </TabsContent>

              <TabsContent
                className="mt-2 h-screen w-full rounded-sm bg-white"
                value="stock_history"
              >
                <BranchStockHistoriesTable />
              </TabsContent>

              <TabsContent
                className="mt-2 h-screen w-full rounded-sm bg-white"
                value="unlisted_items"
              >
                <UnlistedItemsTable />
              </TabsContent>
            </Tabs>

            <div id="tables" />
          </div>
        </div>
        :
        <>
          {!isCompanyLoading && <ViewBranchesDialog company={company}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            companyList={companies?.results} link={'/stock/branch-details'} />}
        </>
      }
    </>
  );
}
