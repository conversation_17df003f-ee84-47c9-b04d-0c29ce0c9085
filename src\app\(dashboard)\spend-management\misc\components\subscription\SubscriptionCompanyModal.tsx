'use client';

import { useRouter } from 'next/navigation';
import React from 'react';

import {
    Button,
    ClientOnly,
    Dialog,
    DialogBody,
    // DialogClose,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    Input,
    LinkButton,

} from '@/components/core';

import { CompanyListEntity } from '@/app/(dashboard)/payroll/misc/types';
import { SmallSpinner } from '@/icons/core';
// import CloseIcon from '@/app/(dashboard)/instant-web/misc/icons/CloseIcon';
import { cn } from '@/utils/classNames';

interface SelectCompanyModalProps {
    companyList: CompanyListEntity[];
    path?: string;
    url_params?: string;
    isLoadingCompanies: boolean;
    module: string;

}


export default function SubscriptionCompanyModal({
    companyList,
    path = "spend-management",
    isLoadingCompanies,
    module,
}: SelectCompanyModalProps) {
    const router = useRouter();

    // const urls = companyList.map(({ id: company_id, company_name }) => {
    //     return `${module}${path}/?companyId=${company_id}&companyName=${company_name}`;
    // });
    // const handleOnOpenChange = (open: boolean) => {
    //     if (!open) {
    //         router.back();
    //     }
    // };

    const [companiesToDisplay, setCompaniesToDisplay] = React.useState<CompanyListEntity[]>(companyList);
    const [serachText, setSearchText] = React.useState<string>('');

    const getCompanyUrl = (company: CompanyListEntity) => {
        // Check if the current path matches the payroll dashboard pattern
        const isPayrollDashboardPath = path.includes('/payroll-dash/') && path.includes('/payroll-dashboard/');

        if (isPayrollDashboardPath) {
            // For payroll dashboard path, use the format: /payroll/payroll-dash/{id}/payroll-dashboard/{name}
            return `${'/payroll'}/payroll-dash/${company.id}/payroll-dashboard/${encodeURIComponent(company.company_name)}`;
        } else {
            // For other paths, use the query parameter format
            return `${module}${path}/?companyId=${company.id}&companyName=${encodeURIComponent(company.company_name)}`;
        }
    };

    React.useEffect(() => {
        if (serachText) {
            const filteredCompanies = companyList.filter(({ company_name }) => company_name.toLowerCase().includes(serachText.toLowerCase()));
            setCompaniesToDisplay(filteredCompanies);
        } else {
            setCompaniesToDisplay(companyList);
        }
    }, [serachText, companyList]);

    return (
        <ClientOnly>
            {/* false for now */}
            <Dialog open={false} >
                <DialogContent className='!max-w-xl'>
                    <DialogHeader>
                        <DialogTitle className="text-base font-semibold">
                            Select company
                        </DialogTitle>
                        <Button
                            className="ml-auto bg-[#2D4696] px-6 py-2"
                            onClick={() => {
                                router.back();
                            }}
                        >
                            Close
                        </Button>
                    </DialogHeader>

                    <DialogBody>
                        <DialogDescription className="mb-4 text-light-text">
                            Select or search a company whose details you want to view
                            <Input
                                className="mt-4 w-full bg-[#F5F7F9] h-11"
                                placeholder="Search company"
                                value={serachText}
                                onChange={(e) => setSearchText(e.target.value)}
                            />
                        </DialogDescription>

                        <div className="flex flex-col gap-4">

                            {
                                isLoadingCompanies &&
                                <li className='flex items-center gap-2 justify-center w-full'>
                                    <SmallSpinner color="#030229" />
                                    <span className='text-sm text-main-solid'>Fetching companies</span>
                                </li>
                            }

                            <div className="overflow-y-auto max-h-[55vh]">

                                {
                                    companiesToDisplay?.map((company) => (
                                        <div className="flex justify-between py-[1.5625rem] px-5 items-center mb-3 border-[0.5px] border-[#D6D6D6] rounded-10" key={company?.id}>
                                            <div className="">
                                                <h2 className='text-xs font-medium text-black'>{company?.company_name}</h2>
                                                <p className='text-xxs text-[#9B9B9B] mt-[.3125rem]'>Company name</p>
                                            </div>
                                            <div className="">
                                                <div className={`${cn(company?.is_subscription ? "bg-[#30A3651A] " : "bg-[#FF3B3B1A]")}  bg-opacity-10 py-[6px] rounded-[10px] px-[8px]`}>
                                                    <h2 className={`${cn(company?.is_subscription ? " text-[#009444]" : "text-[#FF3B3B]")} text-xxs sm:text-xs`}>{company?.is_subscription ? "Subscribed" : "Not Subscribed"}</h2>

                                                </div>
                                                <p className='text-xxs text-[#9B9B9B] mt-[.3125rem]'>Subscription status</p>
                                            </div>
                                            <div className="">
                                                <LinkButton
                                                    className={`${cn(company?.is_subscription ? "bg-[#EFF7FF] text-[#032282]" : "text-white")} sm:text-xs text-[10px] font-medium rounded-[7px] px-3 py-[6px]`}
                                                    href={
                                                        company?.is_subscription ?
                                                            getCompanyUrl(company)
                                                            :
                                                            `/subscription?companyId=${company?.id}&companyName=${encodeURIComponent(company?.company_name)}`
                                                    }>{company?.is_subscription ? "View company" : "Subscribe now"}</LinkButton>
                                                <p className='text-xxs text-[#9B9B9B] mt-[.3125rem]'>Action</p>
                                            </div>
                                        </div>

                                    ))
                                }
                            </div>
                        </div>

                    </DialogBody>
                </DialogContent>
            </Dialog>
        </ClientOnly>
    );
}