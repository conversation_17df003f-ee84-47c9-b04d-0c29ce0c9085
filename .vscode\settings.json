{"css.customData": [".vscode/tailwind.json"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "dotenv.enableAutocloaking": false, "typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnPaste": true, "editor.formatOnType": true, "workbench.editor.labelFormat": "medium", "html.format.indentInnerHtml": true, "html.format.wrapLineLength": 100, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[css]": {"editor.defaultFormatter": "vscode.css-language-features"}, "cSpell.words": ["Subscriptionmodule"], "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}}