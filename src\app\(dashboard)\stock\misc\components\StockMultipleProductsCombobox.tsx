'use client';

import * as React from 'react';

import {
  Button,
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/core';
import { useDebounce } from '@/hooks';
import { cn } from '@/utils/classNames';

import {
  useGetProductsByCompanyAndCategory,
  //   useGetStockCategoriesByCompany,
} from '../api';
import { StockCategories } from '../types';

type ProductType = {
  id?: string;
  value?: string;
  label?: string;
  quantity?: number;
};

interface CategoriesComboboxProps {
  id?: string;
  companyId: string;
  categoryId: string;
  defaultCategoryId?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  initialCategoriesResponse?: StockCategories;
  products: ProductType[];
  handleAddOrRemoveProducts: (category: {
    value: string;
    label: string;
    quantity: number;
  }) => void;
}

export function StockMultipleProductsCombobox({
  id,
  placeholder,
  value: rhfValue,
  onChange,
  categoryId,
  //   initialCategoriesResponse,
  handleAddOrRemoveProducts,
  products,
  companyId,
}: CategoriesComboboxProps) {
  const [open, setOpen] = React.useState(false);

  const [, setSelectedCategory] = React.useState<{
    label: string;
    value: string;
  } | null>();

  const [value] = React.useState('');
  const [search, setSearch] = React.useState('');

  const debouncedSearch = useDebounce(search, 500);

  const fetchOptions = {
    companyId,
    categoryId: categoryId,
    pageIndex: 1,
    pageSize: 100,
    search: debouncedSearch,
  };

  const { data: allProducts, isFetching: areUsersFetching } =
    useGetProductsByCompanyAndCategory(fetchOptions);

  const productsList = allProducts?.data.products;

  const options = productsList?.map(({ name, id }) => {
    return {
      value: id,
      label: `${name || 'Name unavailable'}`,
      quantity: 0,
    };
  });

  const [checkboxes, setCheckboxes] = React.useState<Array<boolean>>([]);

  const handleCheckboxChange = (index: number) => {
    const newCheckboxes = [...checkboxes]; // Create a copy of the current array
    newCheckboxes[index] = !newCheckboxes[index]; // Toggle the value at the specified index
    setCheckboxes(newCheckboxes); // Update the state with the new array
  };

  function isSelected(value: string) {
    return products?.find((el: { value?: string }) => el?.value === value)
      ? true
      : false;
  }

  return (
    <>
      <Popover
        // defaultOpen={true}
        modal={true}
        open={open}
        onOpenChange={setOpen}
      >
        <PopoverTrigger asChild>
          <Button
            className={cn(
              'flex h-10 w-full items-center justify-between gap-2 rounded-md border border-[#E8E8E8]/70 bg-white px-3 py-2 text-left text-xs transition duration-300 hover:opacity-100 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 active:scale-100 disabled:cursor-not-allowed disabled:opacity-50',
              !(value || rhfValue) && 'text-muted-foreground'
            )}
            id={id}
            type="button"
            variant="unstyled"
          >
            {!!products.length ? (
              <span className="flex items-center gap-2">
                <span className="min-w-0 max-w-[2rem] [@media(min-width:356px)]:max-w-[8rem]">
                  {`${products.length} ${products.length > 1 ? 'products' : 'product'
                    } selected`}
                </span>
              </span>
            ) : (
              <span className="font-normal text-input-placeholder">
                {placeholder || 'Select an option'}
              </span>
            )}

            <span>
              <svg
                fill="none"
                height={7}
                viewBox="0 0 12 7"
                width={12}
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  className="fill-label-text"
                  clipRule="evenodd"
                  d="M8.357 5.522a3.333 3.333 0 0 1-4.581.126l-.133-.126L.41 2.089A.833.833 0 0 1 1.51.84l.078.07L4.82 4.342c.617.617 1.597.65 2.251.098l.106-.098L10.411.91a.833.833 0 0 1 1.248 1.1l-.07.079-3.232 3.433Z"
                  fillRule="evenodd"
                />
              </svg>
            </span>
          </Button>
        </PopoverTrigger>

        <PopoverContent align="end" className="max-h-none p-0">
          <Command>
            <CommandInput
              isLoading={areUsersFetching}
              placeholder={'Search for new product'}
              value={search}
              onValueChange={setSearch}
            />

            <button
              className="m-2 flex items-center justify-center gap-4 rounded-lg bg-[#5879FD] p-3 text-sm text-white"
              onClick={() => setOpen(false)}
            >
              <span className="inline-block">Continue</span>
              {!!products.length && (
                <span className="rounded-full bg-[#fff]/10 p-1 text-xs font-bold">
                  {products.length}
                </span>
              )}
            </button>

            <CommandEmpty className="p-1">
              <p className="px-2 py-3 pl-6 pt-1 text-xs">No matches found.</p>
            </CommandEmpty>
            <CommandList className="max-h-60 overflow-y-auto">
              {options?.map((option, index) => {
                const isChecked = isSelected(option.value);
                return (
                  <CommandItem
                    className="relative py-3 text-xs"
                    key={option.value}
                    value={option.value}
                    onSelect={currentValue => {
                      // setValue(currentValue === value ? '' : currentValue);
                      const category = options?.find(
                        option =>
                          option.value.toLowerCase() ==
                          currentValue.toLowerCase()
                      );

                      setSelectedCategory(category);
                      if (category) {
                        handleAddOrRemoveProducts(category);
                      }

                      // Maintain Hook Form state below alongside local state above.
                      // This enables the component to be usable without Hook Form
                      onChange?.(
                        category?.label.toLowerCase() === value.toLowerCase()
                          ? ''
                          : category?.value || ''
                      );
                    }}
                  >
                    <svg
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === option.value ? 'opacity-100' : 'opacity-0'
                      )}
                      fill="none"
                      height={16}
                      viewBox="0 0 16 16"
                      width={16}
                      xmlns="http://www.w3.org/2000/svg"
                      aria-hidden
                    >
                      <path
                        d="m14.53 5.03-8 8a.751.751 0 0 1-1.062 0l-3.5-3.5a.751.751 0 1 1 1.063-1.062L6 11.438l7.47-7.469a.751.751 0 0 1 1.062 1.063l-.001-.002Z"
                        fill="#4E4E4E"
                      />
                    </svg>

                    <span className="">
                      <span className="flex flex-col gap-2">
                        <span
                          className={cn(
                            'text-xxs capitalize text-[#646464] text-opacity-80'
                          )}
                        >
                          {option.label.toLowerCase()}
                        </span>

                        <Input
                          checked={isChecked}
                          className="absolute inset-y-0 !top-[.85rem] right-2 h-4 w-4"
                          id="all_branches"
                          type="checkbox"
                          onChange={() => handleCheckboxChange(index)}
                        />
                      </span>
                    </span>
                  </CommandItem>
                );
              })}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
