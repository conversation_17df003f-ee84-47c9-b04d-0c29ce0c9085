// import { useMutation } from "@tanstack/react-query";

// import axios from "axios";

// export const requestResetPassword = async (email: string) => {
//     const response = await axios.post(`/agency/user/first_reset_login_pin/`, { entry_type: "email", identifier: email })
//     return response.data
// }
// export const useRequestResetPassword = () => {
//     return useMutation({
//         mutationFn: requestResetPassword,
//         mutationKey: ["requestreset"]
//     })
// }


import { authAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';

export interface requestPasswordDTO {
    entry_type: string;
    identifier: string;
}



const requestResetPassword = async (requestPasswordDTO: requestPasswordDTO) => {
    const response = await authAxios.post(
        '/agency/user/first_reset_login_pin/',
        requestPasswordDTO
    );
    return response.data;
};

export const useRequestResetPassword = () => {
    return useMutation({
        mutationFn: requestResetPassword,
    });
};
