<svg width="280" height="151" viewBox="0 0 280 151" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_4358_65535)">
<rect width="280" height="151" rx="14" fill="#001453"/>
<rect opacity="0.2" width="61.1882" height="137.539" rx="2" transform="matrix(-0.394528 -0.918884 0.990345 0.138626 -2.71191 89.0154)" fill="url(#paint0_linear_4358_65535)"/>
<g opacity="0.1" filter="url(#filter0_d_4358_65535)">
<rect width="61.0427" height="121.287" rx="2" transform="matrix(0.390905 0.920431 -0.99013 -0.140152 289.692 68.5619)" fill="url(#paint1_linear_4358_65535)" shape-rendering="crispEdges"/>
</g>
<g opacity="0.1" filter="url(#filter1_d_4358_65535)">
<rect width="53.265" height="85.5614" rx="2" transform="matrix(0.968236 0.250039 -0.507484 0.861661 173.78 -45.665)" fill="url(#paint2_linear_4358_65535)" shape-rendering="crispEdges"/>
</g>
</g>
<rect x="0.5" y="0.5" width="279" height="150" stroke="black"/>
<defs>
<filter id="filter0_d_4358_65535" x="166.235" y="51.822" width="150.687" height="80.6666" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4358_65535"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4358_65535" result="shape"/>
</filter>
<filter id="filter1_d_4358_65535" x="127.124" y="-45.2366" width="101.465" height="94.1863" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4358_65535"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4358_65535" result="shape"/>
</filter>
<linearGradient id="paint0_linear_4358_65535" x1="30.5941" y1="0" x2="30.5941" y2="137.539" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_4358_65535" x1="30.5214" y1="0" x2="30.5214" y2="121.287" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_4358_65535" x1="26.6325" y1="0" x2="26.6325" y2="85.5614" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_4358_65535">
<rect width="280" height="151" fill="white"/>
</clipPath>
</defs>
</svg>
