'use client'

import { redirect } from 'next/navigation';
import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';


import { ViewProductsModal } from '../../misc/components/modals';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';
const ViewProducts = () => {

  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies, count: companiesCount } = companiesResponse as Companies;

  if (companiesCount < 1) {
    redirect('/spend-management/companies/create-company');
  }

  return (
    <>
      <ViewProductsModal
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies}
      // initialUsersResponse={usersResponse}
      />
    </>
  );
};

export default ViewProducts;
