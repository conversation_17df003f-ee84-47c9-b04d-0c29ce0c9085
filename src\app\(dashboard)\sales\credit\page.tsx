'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

import { ViewBranchesDialog } from '../../stock/misc/components';
import { LoaderModal } from '@/components/core';
import { useCompaniesList } from '../../instant-web/misc/api';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';
import { CompaniesResultsEntity } from '../../spend-management/misc/types';
import SalesCredit from '../misc/components/tables/SalesCredit';
import { SalesCreditDashboardStats } from '../misc/components/SalesCreditDashboardStats';

const Page = () => {
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const router = useRouter();

    const branchId = searchParams.get('branch') || '';
    const companyId = searchParams.get('company') || '';

    const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
    const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

    useEffect(() => {
        if (!companyId && salesUserDetails?.data) {
            router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [salesUserDetails?.data.company_id])



    return (
        <>
            <LoaderModal isOpen={isDefaultLoading || !companyId} />

            {branchId ? <div className=''>
                <div className='mt-2 px-8 pt-3 space-y-2 bg-white'>
                    <h2 className='font-bold'>Sales Credit records</h2>
                    <h2 className='text-muted-foreground'>Manage customers credit history</h2>
                </div>
                <SalesCreditDashboardStats branchId={branchId} companies={companies?.results as unknown as CompaniesResultsEntity[]} />

                <div className="">
                    <SalesCredit branchId={branchId} companyId={companyId} />
                </div>
            </div> :
                <>
                    {!isCompanyLoading && <ViewBranchesDialog company={companyId}
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        companyList={companies?.results} link={'/sales/customers'} />}
                </>
            }
        </>

    );
};

export default Page;