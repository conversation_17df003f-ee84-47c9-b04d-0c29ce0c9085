"use client";

import React from "react";
import Image from "next/image";

const Marquee = () => {
  return (
    <>
      <section className="!z-50 w-full bg-[#b7d1ff10] z- py-[.375rem] xl:py-6 text-lg font-display">
        <div className="marquee-content">
          <div className="marquee-list font-clash flex items-center gap-28 xl:text-xl">
            <Image
              alt="logo"
              height={50}
              src={"/images/marketing/hero-images/assuredlogo.png"}
              width={150}
            />
            <Image
              alt="logo"
              height={50}
              src={"/images/marketing/hero-images/seedslogo.png"}
              width={100}
            />
            <Image
              alt="logo"
              height={50}
              src={"/images/marketing/hero-images/whisperlogo.png"}
              width={150}
            />
            <Image
              alt="logo"
              height={60}
              src={"/images/marketing/hero-images/getlinkedlogo.png"}
              width={150}
            />
            <Image
              alt="logo"
              height={50}
              src={"/images/marketing/hero-images/winwiselogo.png"}
              width={120}
            />
          </div>
        </div>
      </section>

      <style jsx>{`
        .marquee-content {
          overflow: hidden;
          position: relative;
          width: 100%;
          zIndex:50
        }

        .marquee-list {
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          animation: marquee 40s linear infinite; /* Slowed down to 40 seconds */
        }

        .marquee-content:hover .marquee-list {
          animation-play-state: paused;
        }

        @keyframes marquee {
          0% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(-100%);
          }
        }

        @media (max-width: 640px) {
          .marquee-list img {
            width: 50px;
            height: auto;
          }

          .marquee-list {
            gap: 30px;
          }
        }
      `}</style>
    </>
  );
};

export default Marquee;
