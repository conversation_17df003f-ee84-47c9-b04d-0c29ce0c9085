'use client';
import React from 'react';
import TokenBalanceCard from './TokenBalanceCard';

// Example component showing different token balance scenarios
const TokenBalanceExample: React.FC = () => {
  // Example data based on your API structure
  const activeTokenBalance = {
    current_balance: 1250,
    previous_balance: 1000,
    status: 'active' as const,
    total_purchased: 2000
  };

  const inactiveTokenBalance = {
    current_balance: 0,
    previous_balance: 150,
    status: 'inactive' as const,
    total_purchased: 500
  };

  const newUserTokenBalance = {
    current_balance: 500,
    previous_balance: 0,
    status: 'active' as const,
    total_purchased: 500
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-[#032282] mb-8">Token Balance Display Examples</h1>
        
        {/* Default variant examples */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Default Variant</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">Active User with Positive Change</h3>
              <TokenBalanceCard tokenBalanceInfo={activeTokenBalance} />
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">Inactive User</h3>
              <TokenBalanceCard tokenBalanceInfo={inactiveTokenBalance} />
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">New User</h3>
              <TokenBalanceCard tokenBalanceInfo={newUserTokenBalance} />
            </div>
          </div>
        </div>

        {/* Compact variant examples */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Compact Variant (for Company Lists)</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">Active Balance</h3>
              <TokenBalanceCard 
                tokenBalanceInfo={activeTokenBalance} 
                variant="compact"
              />
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">Inactive Balance</h3>
              <TokenBalanceCard 
                tokenBalanceInfo={inactiveTokenBalance} 
                variant="compact"
              />
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">New User</h3>
              <TokenBalanceCard 
                tokenBalanceInfo={newUserTokenBalance} 
                variant="compact"
              />
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-600 mb-2">Zero Balance</h3>
              <TokenBalanceCard 
                tokenBalanceInfo={{
                  current_balance: 0,
                  previous_balance: 0,
                  status: 'inactive',
                  total_purchased: 0
                }} 
                variant="compact"
              />
            </div>
          </div>
        </div>

        {/* Integration example */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Company List Integration Example</h2>
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Mock company card */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <p className="text-xs font-medium text-green-500">Subscribed</p>
                  <button className="text-gray-400 hover:text-gray-600">⋮</button>
                </div>

                <dl className="mb-4 grid grid-cols-3 gap-3">
                  <div className="flex flex-col-reverse gap-1.5">
                    <dt className="text-xs text-gray-500">Company</dt>
                    <dd className="text-sm text-gray-800">Abc Construction</dd>
                  </div>
                  <div className="flex flex-col-reverse gap-1.5">
                    <dt className="text-xs text-gray-500">Teams</dt>
                    <dd className="text-sm text-gray-800">3</dd>
                  </div>
                  <div className="flex flex-col-reverse gap-1.5">
                    <dt className="text-xs text-gray-500">Requisitions</dt>
                    <dd className="text-sm text-gray-800">12</dd>
                  </div>
                </dl>

                {/* Token Balance integrated */}
                <div className="mb-4">
                  <TokenBalanceCard 
                    tokenBalanceInfo={activeTokenBalance}
                    variant="compact"
                  />
                </div>

                {/* Action buttons */}
                <div className="flex flex-wrap gap-2">
                  <button className="px-3 py-2 text-xs bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100">
                    Create team
                  </button>
                  <button className="px-3 py-2 text-xs bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100">
                    View budgets
                  </button>
                  <button className="px-3 py-2 text-xs bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100">
                    View details
                  </button>
                </div>
              </div>

              {/* Another mock company card with inactive tokens */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <p className="text-xs font-medium text-red-500">Not Subscribed</p>
                  <button className="text-gray-400 hover:text-gray-600">⋮</button>
                </div>

                <dl className="mb-4 grid grid-cols-3 gap-3">
                  <div className="flex flex-col-reverse gap-1.5">
                    <dt className="text-xs text-gray-500">Company</dt>
                    <dd className="text-sm text-gray-800">Tech Startup Ltd</dd>
                  </div>
                  <div className="flex flex-col-reverse gap-1.5">
                    <dt className="text-xs text-gray-500">Teams</dt>
                    <dd className="text-sm text-gray-800">0</dd>
                  </div>
                  <div className="flex flex-col-reverse gap-1.5">
                    <dt className="text-xs text-gray-500">Requisitions</dt>
                    <dd className="text-sm text-gray-800">0</dd>
                  </div>
                </dl>

                {/* Token Balance integrated */}
                <div className="mb-4">
                  <TokenBalanceCard 
                    tokenBalanceInfo={inactiveTokenBalance}
                    variant="compact"
                  />
                </div>

                {/* Action buttons */}
                <div className="flex flex-wrap gap-2">
                  <button className="px-3 py-2 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Subscribe
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TokenBalanceExample;
