'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';
import {
  Button,
  ClientOnly,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  FormError,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { SmallSpinner } from '@/icons/core';
import { cn } from '@/utils/classNames';

import { useGetBranchesByCompany } from '../../api';
import { StockCategoryComboboxTest } from '../StockCategoryComboboxTest';
import { AssignStockRequestValues } from './AssignStockRequestValues';

const CreateCategoryFormSchema = z.object({
  company: z
    .string({ required_error: 'Please select a company name.' })
    .trim()
    .min(1, { message: 'Please select a company name.' }),
  demand_branch: z.string(),
  supply_branch: z.string(),
  category: z.string(),
  categories: z.array(
    z.object({
      value: z
        .string({ required_error: 'Please select a category name.' })
        .trim()
        .min(1, { message: 'Please select a category name.' }),
      label: z
        .string({ required_error: 'Please select a category name.' })
        .trim()
        .min(1, { message: 'Please select a category name.' }),
      stocks: z.array(
        z.object({
          value: z
            .string({ required_error: 'Please enter product value.' })
            .trim()
            .min(1, { message: 'Please enter product value.' }),
          label: z
            .string({ required_error: 'Please enter product name.' })
            .trim()
            .min(1, { message: 'Please enter product name.' }),
          quantity: z.coerce
            .number({ required_error: 'Please enter a quantity.' })
            .min(1, {
              message: 'Please enter a quantity greater than zero.',
            }),
        })
      ),
    })
  ).superRefine((categories, ctx) => {
    categories.forEach((category, categoryIndex) => {
      if (category.stocks.length === 0) {
        ctx.addIssue({
          path: [categoryIndex, `stocks`],
          message: 'Please enter at least one product.',
          code: z.ZodIssueCode.custom,
        });
      }
    });
  }),
});

export type CreateStockRequestFormValues = z.infer<
  typeof CreateCategoryFormSchema
>;

interface CreateCategoryModalProps {
  companies: CompaniesResultsEntity[];
  companyId: string;
}

export const MakeStockRequestModal = ({
  companyId,
}: CreateCategoryModalProps) => {
  const router = useRouter();

  const searchParams = useSearchParams();
  const branchId = searchParams.get('branch') as string;

  const {
    state: isModalOpen,
    setState: setModalState,
    setFalse: closeModal,
    setTrue: openModal,
  } = useBooleanStateControl();

  const {
    control,
    handleSubmit,
    register,
    reset,
    formState: { errors },
  } = useForm<CreateStockRequestFormValues>({
    resolver: zodResolver(CreateCategoryFormSchema),
    defaultValues: {
      company: companyId,
      supply_branch: branchId,
      categories: [],
    },
  });

  const demandBranchValue = useWatch({
    control,
    name: 'demand_branch',
  });

  const {
    fields: memberCategoryFields,
    append: appendCategoryField,
    remove: removeCategoryField,
  } = useFieldArray({
    control,
    name: 'categories',
  });

  const noSelectedCategories = !memberCategoryFields.length;

  const removeFromCategoryList = (id: string) => {
    const itemIndex = memberCategoryFields.findIndex(field => field.id === id);
    if (itemIndex !== -1) {
      removeCategoryField(itemIndex);
    }
  };

  const handleAddOrRemoveProducts = (newCategory: {
    value: string;
    label: string;
  }) => {
    const newProductExists = memberCategoryFields.findIndex(
      obj => obj.value === newCategory.value
    );

    if (newProductExists != -1) {
      const itemIndex = memberCategoryFields.findIndex(
        field => field.value === newCategory.value
      );
      if (itemIndex !== -1) {
        removeCategoryField(itemIndex);
      }
    } else {
      appendCategoryField({
        ...newCategory,
        stocks: [],
      });
    }
  };

  const { data: allBranchesByCompany, isLoading: isGetAllBranchesLoading } =
    useGetBranchesByCompany(companyId || '');

  const allBranches = allBranchesByCompany?.data.branches;

  const handleOnOpenChange = (open: boolean) => {
    if (!open) {
      router.back();
    }
  };
  const isMakeStockRequestModalHidden = isModalOpen;

  return (
    <ClientOnly>
      <Dialog open={true} onOpenChange={handleOnOpenChange}>
        <DialogTrigger className="hidden px-[.875rem] sm:py-2.5">
          Make Stock Request
        </DialogTrigger>

        <DialogContent
          className={cn(isMakeStockRequestModalHidden && 'hidden')}
          overlayClassName={cn(isMakeStockRequestModalHidden && 'hidden')}
        >
          <DialogHeader>
            <DialogTitle>Make Stock Request</DialogTitle>
            <DialogClose>Close</DialogClose>
          </DialogHeader>

          <DialogBody>
            <DialogDescription className="mb-4 text-light-text">
              Kindly select categories for your Stock request.
            </DialogDescription>

            <form>
              {/* <div className="mb-2">
                <Controller
                  control={control}
                  name="company"
                  render={({ field: { onChange, value, ref } }) => (
                    <Select value={value} onValueChange={onChange}>
                      <SelectTrigger
                        className="bg-[#F2F5FF] text-sm !font-semibold capitalize !text-[#032282]"
                        id="company"
                        ref={ref}
                      >
                        <div className="flex items-center">
                          <p className="text-xs text-[#566AAA]">
                            Company name:
                          </p>
                          &nbsp;
                          <SelectValue placeholder="Select Company" />
                        </div>
                      </SelectTrigger>
                      <SelectContent className="">
                        {Array.isArray(companies) && companies.length > 0 ? (
                          companies?.map(({ id, company_name }) => {
                            return (
                              <SelectItem key={id} value={id}>
                                {company_name}
                              </SelectItem>
                            );
                          })
                        ) : (
                          <SelectItem
                            className="text-xs text-gray-400"
                            key={'nil'}
                            value={'nil'}
                            disabled
                          >
                            No Companies
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  )}
                />

                {errors?.company && (
                  <FormError errorMessage={errors.company.message} />
                )}
              </div> */}

              <div className="mb-1 ">
                <p className="text-xs font-semibold text-[#37474F]">
                  <sup>*</sup>Branch you want to send request to
                </p>
                <Controller
                  control={control}
                  name="demand_branch"
                  render={({ field: { onChange, value, ref } }) => (
                    <Select
                      value={value}
                      onValueChange={e => {
                        onChange(e);
                      }}
                    >
                      <SelectTrigger
                        className="bg-[#F2F5FF] text-sm !font-semibold capitalize !text-[#032282]"
                        id="demand_branch"
                        ref={ref}
                      >
                        <div className="flex items-center">
                          <p className="text-xs text-[#566AAA]">Branch name:</p>
                          &nbsp;
                          <SelectValue placeholder="Select Branch" />
                        </div>
                      </SelectTrigger>
                      <SelectContent className="">
                        {isGetAllBranchesLoading ? (
                          <SmallSpinner
                            className="mr-2 animate-spin"
                            color="gray"
                          />
                        ) : Array.isArray(allBranches) &&
                          allBranches.length > 0 ? (
                          allBranches?.map(({ id, name }) => {
                            return (
                              <SelectItem key={id} value={id}>
                                {name}
                              </SelectItem>
                            );
                          })
                        ) : (
                          <SelectItem
                            className="text-xs text-gray-400"
                            key={'nil'}
                            value={'nil'}
                            disabled
                          >
                            No branches listed under the selected company.
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  )}
                />

                {errors?.demand_branch && (
                  <FormError errorMessage={errors.demand_branch.message} />
                )}
              </div>

              <div className="mb-3 rounded-lg bg-[#F8FAFF] p-4">
                <h2 className="text-center text-xs text-[#242424]">
                  Categories
                </h2>

                <div className="my-2 grow">
                  <Controller
                    control={control}
                    name={'category'}
                    render={({ field: { onChange } }) => (
                      <StockCategoryComboboxTest
                        companyId={companyId}
                        // defaultCategoryId={categoryId}
                        handleAddOrRemoveProducts={handleAddOrRemoveProducts}
                        id="category"
                        placeholder="Select categories"
                        products={memberCategoryFields}
                        onChange={onChange}
                      />
                    )}
                  />

                  {errors?.categories?.message && (
                    <FormError errorMessage={errors?.categories?.message} />
                  )}
                </div>

                <div className="mb-6 grid grid-cols-3 items-center gap-4 space-y-1">
                  {memberCategoryFields.map((item, index) => {
                    return (
                      <div className="h-10" key={index}>
                        <button
                          className="flex w-full items-center justify-between rounded-[10px] bg-white px-2 py-4 text-sm text-black "
                          type='button'
                          onClick={() => removeFromCategoryList(item.id)}
                        >
                          <span className="inline-block truncate">
                            {item.label}
                          </span>

                          <span className="inline-block">
                            <svg
                              fill="none"
                              height="16"
                              viewBox="0 0 16 16"
                              width="16"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M7.99992 15.1667C4.04659 15.1667 0.833252 11.9533 0.833252 7.99999C0.833252 4.04666 4.04659 0.833328 7.99992 0.833328C11.9533 0.833328 15.1666 4.04666 15.1666 7.99999C15.1666 11.9533 11.9533 15.1667 7.99992 15.1667ZM7.99992 1.83333C4.59992 1.83333 1.83325 4.59999 1.83325 7.99999C1.83325 11.4 4.59992 14.1667 7.99992 14.1667C11.3999 14.1667 14.1666 11.4 14.1666 7.99999C14.1666 4.59999 11.3999 1.83333 7.99992 1.83333Z"
                                fill="#4E4E4E"
                              />
                              <path
                                d="M6.11332 10.3867C5.98666 10.3867 5.85999 10.34 5.75999 10.24C5.56666 10.0467 5.56666 9.72667 5.75999 9.53334L9.53332 5.76001C9.72666 5.56667 10.0467 5.56667 10.24 5.76001C10.4333 5.95334 10.4333 6.27334 10.24 6.46667L6.46666 10.24C6.37332 10.34 6.23999 10.3867 6.11332 10.3867Z"
                                fill="#4E4E4E"
                              />
                              <path
                                d="M9.88666 10.3867C9.75999 10.3867 9.63332 10.34 9.53332 10.24L5.75999 6.46667C5.56666 6.27334 5.56666 5.95334 5.75999 5.76001C5.95332 5.56667 6.27332 5.56667 6.46666 5.76001L10.24 9.53334C10.4333 9.72667 10.4333 10.0467 10.24 10.24C10.14 10.34 10.0133 10.3867 9.88666 10.3867Z"
                                fill="#4E4E4E"
                              />
                            </svg>
                          </span>
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>

              <Button
                className="mt-2 flex items-center gap-x-2 py-3 text-sm"
                disabled={noSelectedCategories || !demandBranchValue}
                size="fullWidth"
                type="button"
                onClick={openModal}
              >
                <span className="inline-block">Continue</span>
              </Button>

              <AssignStockRequestValues
                categoriesList={memberCategoryFields}
                closeModal={closeModal}
                control={control}
                errors={errors}
                handleSubmit={handleSubmit}
                isModalOpen={isModalOpen}
                register={register}
                reset={reset}
                setModalState={setModalState}
              />
            </form>
          </DialogBody>
        </DialogContent>
      </Dialog>

      {/* <SuccessModal
        heading="Categories created successfully"
        isSuccessModalOpen={isSuccessModalOpen}
        setSuccessModalState={setSuccessModalState}
        subheading=""
      >
        <p className="text-sm">
          Do you want to create products from newly created categories?&nbsp;
          <Link
            className="font-bold text-blue-600"
            href={`/stock/create-categories/new`}
          >
            Yes
          </Link>
        </p>
        <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
          <Button
            className="grow text-base"
            size="lg"
            variant="outlined"
            onClick={() => router.back()}
          >
            Done
          </Button>

          <LinkButton className="grow text-base" href="/" size="lg">
            View categories
          </LinkButton>
        </div>
      </SuccessModal>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 text-base"
            size="lg"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal> */}
    </ClientOnly>
  );
};
