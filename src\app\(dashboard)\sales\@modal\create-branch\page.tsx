"use client"
// import { redirect } from 'next/navigation';
import React from 'react';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';


import { CreateBranchModal } from '../../misc/components/modals';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';

export default function CreateBranch() {
  // const CALLBACK_URL = '/sales/create-branch';

  const { data } = useCompaniesList()

  return (
    <>

      <CreateBranchModal
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={data?.results as CompaniesResultsEntity[]} />
    </>
  );
}
