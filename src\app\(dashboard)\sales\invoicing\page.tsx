"use client"

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

import { InvoicingBranchBaseDashboard } from '@/app/(dashboard)/sales/misc/components/InvoicingBranchBaseDashboard';
import { InvoicesTable } from '@/app/(dashboard)/sales/misc/components/tables/PendingInvoicesTable';
import { LoaderModal, Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core';
import { InvoiceAnalytics } from '../misc/components/tables/InvoiceAnalytics';
import { BranchesDashboardStats } from '../misc/components/BranchDashboardStats';
import { useCompaniesPayroll } from '../misc/api/getCompaniesPayroll';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { ViewBranchesDialog } from '@/app/(dashboard)/instant-web/misc/components';

const Page = () => {
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const router = useRouter();

    const branchId = searchParams.get('branch') || '';
    const companyId = searchParams.get('company') || '';

    const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
    const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

    useEffect(() => {
        if (!companyId && salesUserDetails?.data) {
            router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [salesUserDetails?.data.company_id])



    const { data: companiesResponse } = useCompaniesPayroll()

    return (
        <>
            <LoaderModal isOpen={isDefaultLoading || !companyId} />

            {branchId ?
                <div className="md:px-7 md:py-3 lg:px-11">
                    <BranchesDashboardStats
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        companies={companiesResponse?.results || []}
                        company={companyId}
                    // initialData={companyOverviewResponse}
                    />

                    <div>
                        <Tabs className="w-full" defaultValue="branch_dashboard">
                            <TabsList className="flex w-full flex-col justify-start gap-[22px] rounded-[.625rem] bg-white pb-0 lg:flex-row">
                                <div className="w-full rounded-[13px] bg-white px-4 py-[6px]">
                                    <TabsTrigger
                                        className={`rounded-[13px]
                bg-white px-[37px]
                py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:text-[#032282] data-[state=active]:shadow-none`}
                                        value="branch_dashboard"
                                    >
                                        Branches
                                    </TabsTrigger>

                                    <TabsTrigger
                                        className={`rounded-[13px]
                bg-white px-[37px]
                py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:text-[#032282] data-[state=active]:shadow-none`}
                                        value="total_invoices"
                                    >
                                        Total Invoicies
                                    </TabsTrigger>
                                    <TabsTrigger
                                        className={`rounded-[13px]
                bg-white px-[37px]
                py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:shadow-none`}
                                        value="paid_invoices"
                                    >
                                        Paid Invoicies
                                    </TabsTrigger>
                                    <TabsTrigger
                                        className={`rounded-[13px]
                bg-white px-[37px]
                py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:shadow-none`}
                                        value="pending_invoices"
                                    >
                                        Pending Invoicies
                                    </TabsTrigger>
                                    <TabsTrigger
                                        className={`rounded-[13px]
                bg-white px-[37px]
                py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:shadow-none`}
                                        value="due_invoices"
                                    >
                                        Due Invoicies
                                    </TabsTrigger>
                                    <TabsTrigger
                                        className={`rounded-[13px]
                bg-white px-[37px]
                py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:shadow-none`}
                                        value="analytics"
                                    >
                                        Analytics
                                    </TabsTrigger>
                                </div>
                            </TabsList>
                            <TabsContent className="mt-4 bg-[#F8FAFF]" value="branch_dashboard">
                                <InvoicingBranchBaseDashboard />
                            </TabsContent>
                            <TabsContent className="mt-4 bg-[#F8FAFF]" value="total_invoices">
                                <InvoicesTable companyId={companyId} invoice_type='' />
                            </TabsContent>
                            <TabsContent className="mt-4 bg-[#F8FAFF]" value="paid_invoices">
                                <InvoicesTable companyId={companyId} invoice_type='PAID' />
                            </TabsContent>
                            <TabsContent className="mt-4 bg-[#F8FAFF]" value="pending_invoices">
                                <InvoicesTable companyId={companyId} invoice_type='PENDING' />
                            </TabsContent>
                            <TabsContent
                                className="mt-4 rounded-sm bg-[#F8FAFF]"
                                value="due_invoices"
                            >
                                <InvoicesTable companyId={companyId} invoice_type='DUE' />
                            </TabsContent>
                            <TabsContent
                                className="mt-4 rounded-sm bg-[#F8FAFF]"
                                value="analytics"
                            >
                                <InvoiceAnalytics companyId={companyId} />
                            </TabsContent>
                        </Tabs>
                    </div>
                </div>
                :
                <>
                    {!isCompanyLoading && <ViewBranchesDialog company={companyId}
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        companyList={companies?.results} link={'/sales/invoicing'} />}
                </>
            }
        </>
    );
};

export default Page;