'use client';

import { Label } from '@radix-ui/react-label';
import React, { useEffect, useState } from 'react';

// import { AddCustomerRecipient } from '@/app/(dashboard)/stock/misc/components';

// import { InvoiceSummaryModal } from '@/app/(dashboard)/stock/misc/components';

import AddInvoiceInput from '../../misc/components/AddInvoiceInput';
import { AddCustomerRecipient } from '../../misc/components/modals/AddCustomerRecipient';
import Image from 'next/image';
import { cn } from '@/utils/classNames';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { ViewBranchesDialog } from '@/app/(dashboard)/instant-web/misc/components';
import { LoaderModal } from '@/components/core';

const Page = () => {
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const router = useRouter();

    const branchId = searchParams.get('branch') || '';
    const companyId = searchParams.get('company') || '';

    const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
    const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

    useEffect(() => {
        if (!companyId && salesUserDetails?.data) {
            router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [salesUserDetails?.data.company_id])
    const [customerDetails, setCustomerDetails] = React.useState<
        | {
            name: string;
            email: string;
            phone: string;
            address: string;
            id: string;
        }
        | undefined
    >();
    const [fileName, setFileName] = React.useState('');
    const [_, setDragActive] = React.useState(false);
    const [logoPreview, setLogoPreview] = useState<string>("")

    const [fileUploadLogo, setLogo] = React.useState<File>();
    // const [fileUploadSubmitLogo, setSubmitLogo] = React.useState<File>();

    // drag state
    const handleDrag = function (e: React.DragEvent<HTMLInputElement>) {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    };

    // triggers when file is dropped
    const handleDrop = function (e: React.DragEvent<HTMLInputElement>) {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            setFileName(e.dataTransfer.files?.[0]?.name);
            setLogo(e.dataTransfer.files[0]);
        }
    };
    // triggers when file is selected with click
    const handleChange = function (e: React.ChangeEvent<HTMLInputElement>) {
        if (e.target.files && e.target.files[0]) {
            setFileName(e.target.files?.[0]?.name);
            //store fileblob in state
            setLogo(e.target.files?.[0]);
        }
        // Create FormData object
        const formData = new FormData();
        // Append company_logo file
        if (e.target.files?.[0]) {
            formData.append('profile_picture', e.target.files?.[0]);
        }
    };

    useEffect(() => {
        if (fileUploadLogo) {
            setLogoPreview(URL.createObjectURL(fileUploadLogo as File));
        }
    }, [fileUploadLogo])
    return (
        <>
            <LoaderModal isOpen={isDefaultLoading || !companyId} />

            {branchId ?
                <section className="px-4 md:px-7 md:py-3 lg:px-11">
                    <div className="mt-4">
                        <div>
                            <h2 className="text-base font-[600] text-[#242424]">New Invoice</h2>
                            <p className="mt-2 text-sm font-[400] text-[#37474F]">
                                Create and send invoices to clients
                            </p>
                        </div>
                    </div>

                    <div className="upload__section mt-2 flex flex-wrap items-center justify-between gap-4">
                        <div className="add__recipient flex gap-4 rounded-[18px] bg-[#F3F6FF] p-4">
                            <div className="flex flex-col">
                                {customerDetails?.name ? (
                                    <>
                                        <p className="text-sm font-[600] text-[#032282]">
                                            {customerDetails.name}
                                        </p>
                                        <p className="text-xs font-[600] text-[#898989]">
                                            {customerDetails.email}, {customerDetails.phone}
                                        </p>
                                        <p className="text-xs font-[600] text-[#898989]">
                                            {customerDetails.address}
                                        </p>
                                    </>
                                ) : (
                                    <>
                                        <p className="text-xs font-[600] text-[#032282]">
                                            {customerDetails ? '' : ''}Add recipient
                                        </p>
                                        <p className="text-[10px] text-[#37474F]">
                                            Invoice recipient has to be a saved contact
                                        </p>
                                    </>
                                )}
                            </div>

                            <AddCustomerRecipient
                                customerDetails={customerDetails}
                                setCustomerDetails={setCustomerDetails}
                            />
                        </div>
                        <div className="logo__upload">
                            {/* <div className="rounded-[14px] border-[2px] border-dashed border-[#032282]"> */}
                            <div className={cn("mx-auto flex h-20 min-w-[200px] w-max relative flex-col items-center justify-center rounded-[14px] border-[1px] border-dashed border-[#032282] p-4")}>
                                {logoPreview ? <div className='w-full'>
                                    <Label htmlFor='company_logo'>
                                        <input
                                            accept=".png, .gif, .svg, .jpg, .jpeg"
                                            // accept="image/*,application/pdf"
                                            className="hidden"
                                            id="company_logo"
                                            type="file"
                                            onChange={handleChange}
                                        />
                                        <Image alt='logo' className='w-full h-full object-contain rounded-[14px]' src={logoPreview} fill />
                                    </Label>
                                    {
                                        <div
                                            className="absolute -right-2 -top-2 flex h-7 w-7  cursor-pointer items-center justify-center rounded-full bg-gray-900 text-white"
                                            onClick={() => {
                                                setLogoPreview("");
                                                setLogo(undefined)
                                            }}
                                        >
                                            x
                                        </div>
                                    }
                                </div> : <div
                                    className="items-left flex flex-col justify-center"
                                    onDragOver={handleDrag}
                                    onDrop={handleDrop}
                                >
                                    <Label
                                        className="cursor-pointer p-0 text-left text-[10px] font-normal text-[#667085]"
                                        htmlFor="company_logo"
                                    >
                                        <div className="flex w-full justify-center">
                                            <svg
                                                fill="none"
                                                height="30"
                                                viewBox="0 0 30 30"
                                                width="30"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    d="M27.5 17.3752V20.2377C27.5 24.7877 24.7875 27.5002 20.2375 27.5002H9.76251C6.57501 27.5002 4.27501 26.1627 3.20001 23.7877L3.33751 23.6877L9.48751 19.5627C10.4875 18.8877 11.9 18.9627 12.7875 19.7377L13.2125 20.0877C14.1875 20.9252 15.7625 20.9252 16.7375 20.0877L21.9375 15.6252C22.9125 14.7877 24.4875 14.7877 25.4625 15.6252L27.5 17.3752Z"
                                                    fill="#032282"
                                                    opacity="0.4"
                                                />
                                                <path
                                                    d="M26.2125 10H22.5375C20.95 10 20 9.05 20 7.4625V3.7875C20 3.2875 20.1 2.8625 20.275 2.5C20.2625 2.5 20.25 2.5 20.2375 2.5H9.7625C5.2125 2.5 2.5 5.2125 2.5 9.7625V20.2375C2.5 21.6 2.7375 22.7875 3.2 23.7875L3.3375 23.6875L9.4875 19.5625C10.4875 18.8875 11.9 18.9625 12.7875 19.7375L13.2125 20.0875C14.1875 20.925 15.7625 20.925 16.7375 20.0875L21.9375 15.625C22.9125 14.7875 24.4875 14.7875 25.4625 15.625L27.5 17.375V9.7625C27.5 9.75 27.5 9.7375 27.5 9.725C27.1375 9.9 26.7125 10 26.2125 10Z"
                                                    fill="#032282"
                                                />
                                                <path
                                                    d="M11.25 12.9754C12.893 12.9754 14.225 11.6434 14.225 10.0004C14.225 8.35734 12.893 7.02539 11.25 7.02539C9.60695 7.02539 8.27499 8.35734 8.27499 10.0004C8.27499 11.6434 9.60695 12.9754 11.25 12.9754Z"
                                                    fill="white"
                                                />
                                                <path
                                                    d="M26.2125 1.25H22.5375C20.95 1.25 20 2.2 20 3.7875V7.4625C20 9.05 20.95 10 22.5375 10H26.2125C27.8 10 28.75 9.05 28.75 7.4625V3.7875C28.75 2.2 27.8 1.25 26.2125 1.25ZM27.3875 6.1625C27.2625 6.2875 27.075 6.375 26.875 6.3875H25.1125L25.125 8.125C25.1125 8.3375 25.0375 8.5125 24.8875 8.6625C24.7625 8.7875 24.575 8.875 24.375 8.875C23.9625 8.875 23.625 8.5375 23.625 8.125V6.375L21.875 6.3875C21.4625 6.3875 21.125 6.0375 21.125 5.625C21.125 5.2125 21.4625 4.875 21.875 4.875L23.625 4.8875V3.1375C23.625 2.725 23.9625 2.375 24.375 2.375C24.7875 2.375 25.125 2.725 25.125 3.1375L25.1125 4.875H26.875C27.2875 4.875 27.625 5.2125 27.625 5.625C27.6125 5.8375 27.525 6.0125 27.3875 6.1625Z"
                                                    fill="#032282"
                                                    opacity="0.4"
                                                />
                                            </svg>
                                        </div>
                                        <p>
                                            <span className="text-[13px] font-semibold text-[#032282]">
                                                {' '}
                                                Click to upload a logo
                                            </span>
                                        </p>
                                    </Label>

                                    <input
                                        accept=".png, .gif, .svg, .jpg, .jpeg"
                                        // accept="image/*,application/pdf"
                                        className="hidden"
                                        id="company_logo"
                                        type="file"
                                        onChange={handleChange}
                                    />
                                    {fileName ? (
                                        <p className="text-xs text-[#4E4E4E]">{fileName}</p>
                                    ) : null}
                                </div>}
                            </div>
                            {/* </div> */}
                        </div>
                    </div>

                    <div className="">
                        <AddInvoiceInput customerDetails={customerDetails} logoPreview={logoPreview} />
                    </div>
                </section>
                :
                <>
                    {!isCompanyLoading && <ViewBranchesDialog company={companyId}
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        companyList={companies?.results} link={'/sales/invoicing'} />}
                </>
            }
        </>
    );
};

export default Page;