'use client';
import React from 'react';
import { Badge } from '@/components/core';

interface PromotionalBannerProps {
  isVisible: boolean;
  className?: string;
}

const PromotionalBanner: React.FC<PromotionalBannerProps> = ({
  isVisible,
  className = ''
}) => {
  if (!isVisible) return null;

  return (
    <div className={`relative overflow-hidden bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 rounded-xl p-6 shadow-xl border border-emerald-200 ${className}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 right-0 w-24 h-24 bg-white rounded-full translate-x-12 translate-y-12"></div>
        <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-white rounded-full"></div>
      </div>

      <div className="relative z-10">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              <Badge className="bg-white text-emerald-600 font-bold px-3 py-1 text-xs">
                🎉 LIMITED TIME OFFER
              </Badge>
              <div className="flex items-center gap-1">
                <span className="text-white text-xs font-medium">✨</span>
                <span className="text-white text-xs font-medium">BONUS INCLUDED</span>
                <span className="text-white text-xs font-medium">✨</span>
              </div>
            </div>
            
            <h3 className="text-white text-xl md:text-2xl font-bold mb-2">
              Get 30 Days FREE Access!
            </h3>
            
            <p className="text-emerald-50 text-sm md:text-base mb-3">
              Subscribe to <span className="font-semibold text-white">Stock & Inventory</span> and{' '}
              <span className="font-semibold text-white">Sales</span> modules and unlock:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span className="text-emerald-50 text-sm font-medium">HR Management (30 days free)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span className="text-emerald-50 text-sm font-medium">Spend Management (30 days free)</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-white text-sm font-bold">💰 Total Value:</span>
              <span className="text-emerald-100 text-sm line-through">₦50,000</span>
              <span className="text-white text-lg font-bold">FREE</span>
            </div>
          </div>

          <div className="hidden md:block ml-6">
            <div className="text-center">
              <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-2">
                <span className="text-3xl">🎁</span>
              </div>
              <p className="text-emerald-100 text-xs font-medium">Bonus Gift</p>
            </div>
          </div>
        </div>

        {/* Countdown or urgency indicator */}
        <div className="mt-4 pt-4 border-t border-emerald-400 border-opacity-30">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-emerald-100 text-xs">⏰</span>
              <span className="text-emerald-100 text-xs font-medium">
                Offer automatically applied when you select both modules
              </span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-white text-xs font-bold">No code needed</span>
              <span className="text-emerald-100 text-xs">✓</span>
            </div>
          </div>
        </div>
      </div>

      {/* Animated shine effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 -skew-x-12 animate-pulse"></div>
    </div>
  );
};

export default PromotionalBanner;
