'use client';
import React from 'react';
import { Badge } from '@/components/core';

interface PromotionalBannerProps {
  isVisible: boolean;
  className?: string;
}

const PromotionalBanner: React.FC<PromotionalBannerProps> = ({
  isVisible,
  className = ''
}) => {
  if (!isVisible) return null;

  return (
    <div className={`bg-white border border-[#032282] border-opacity-20 rounded-lg p-6 shadow-sm ${className}`}>
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-[#032282] bg-opacity-10 rounded-lg flex items-center justify-center">
            <span className="text-[#032282] text-xl">🎁</span>
          </div>
        </div>

        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Badge className="bg-[#032282] text-white font-medium px-2 py-1 text-xs">
              SPECIAL OFFER
            </Badge>
          </div>

          <h3 className="text-[#032282] text-lg font-bold mb-2">
            30 Days FREE Access Included
          </h3>

          <p className="text-gray-600 text-sm mb-3">
            You've selected both <span className="font-semibold text-[#032282]">Stock & Inventory</span> and{' '}
            <span className="font-semibold text-[#032282]">Sales</span> modules. As a bonus, you'll receive:
          </p>

          <div className="space-y-2 mb-4">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-[#032282] rounded-full"></div>
              <span className="text-gray-700 text-sm">HR Management (30 days free)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-[#032282] rounded-full"></div>
              <span className="text-gray-700 text-sm">Spend Management (30 days free)</span>
            </div>
          </div>

          <div className="bg-[#032282] bg-opacity-5 rounded-lg p-3">
            <p className="text-[#032282] text-sm font-medium">
              This offer is automatically applied to your subscription
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionalBanner;
