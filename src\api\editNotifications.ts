import { useMutation, useQueryClient } from '@tanstack/react-query';

import { managementAxios } from '@/lib/axios';

export interface EditNotificationsPayload {
  company: string;
  id: string[];
}

const editNotifications = async (editNotificationsDto: EditNotificationsPayload) => {
  const { company, id } = editNotificationsDto;
  const response = await managementAxios.patch(`/core/notifications/`, {
    company,
    id
  });
  return response.data;
};

export const useEditNotifications = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: editNotifications,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['notifications-list', variables.company],
      });
    },
  });
};
