'use client'

import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

import { ViewBranchesDialog } from '../../../misc/components';
import { LoaderModal } from '@/components/core';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';

const Page = () => {
  const searchParams = useSearchParams();
  const company = searchParams.get('company') || '';

  const router = useRouter()

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  const isLoading = isCompanyLoading || isDefaultLoading;

  useEffect(() => {
    if (salesUserDetails?.data.company_id) {
      if (!company || (salesUserDetails?.data.company_id && (salesUserDetails?.data.company_id === company) && salesUserDetails?.data.branch_id)) {
        return router.replace(`/stock/stock-out?company=${salesUserDetails?.data.company_id}&companyName=${salesUserDetails?.data.company}&branch=${salesUserDetails?.data.branch_id}`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])

  return (
    <>
      <LoaderModal isOpen={isLoading} />
      {!isLoading && <ViewBranchesDialog company={company}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={companies?.results} link={'/stock/stock-out'} />}
    </>
  );
};

export default Page;
