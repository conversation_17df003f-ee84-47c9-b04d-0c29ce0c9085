'use client'

import * as React from 'react';

import { checkIsIOS, disableIOSTextFieldZoom } from '@/utils/inputs';
import { useOnlineStatus } from '@/hooks/useConnectionStatus';

export function Wrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const isOnline = useOnlineStatus();

  React.useEffect(() => {
    // Check if the current device is iOS and disable text field zooming.
    if (checkIsIOS()) {
      disableIOSTextFieldZoom();
    }
  }, []);

  return (
    <>
      {!isOnline && (
        <div className="fixed top-0 z-[2000] w-screen bg-red-500 px-6 py-1.5 text-center text-sm text-white">
          You&apos;re not connected to the internet, the app may not work as
          expected
        </div>
      )}
      {children}
    </>
  );
}
