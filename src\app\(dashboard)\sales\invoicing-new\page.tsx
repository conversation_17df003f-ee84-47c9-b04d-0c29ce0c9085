/* eslint-disable @typescript-eslint/no-explicit-any */
"use client"

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { InvoicesTable } from '@/app/(dashboard)/sales/misc/components/tables/PendingInvoicesTable';
import { Button, LinkButton, LoaderModal } from '@/components/core';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { ViewBranchesDialog } from '@/app/(dashboard)/instant-web/misc/components';
import { InvoiceOverviewStats } from './misc/components/Overview';
import { InvoicingBranchBaseDashboard } from '../misc/components/InvoicingBranchBaseDashboard';
import { cn } from '@/utils/classNames';
// import SubscriptionErrorModal from '../../spend-management/misc/components/subscription/ErrorSubscriptionModal';

const Page = () => {
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const router = useRouter();
    const [invoice_type, setInvoice_type] = useState<"" | "PAID" | "PENDING" | "DUE">("")
    const [tab, setTab] = useState("Invoices")
    const [filterOption, setFilterOption] = useState("this_year")
    const branchId = searchParams.get('branch') || '';
    const companyId = searchParams.get('company') || '';

    const invoiceType = [
        {
            name: "All invoice",
            value: ""
        },
        {
            name: "Paid invoice",
            value: "PAID"
        },
        {
            name: "Pending invoice",
            value: "PENDING"
        },
        {
            name: "Due invoice",
            value: "DUE"
        },
        {
            name: "Not completed",
            value: "NOT_COMPLETED"
        }
    ]

    const periodOptions = [
        { name: 'Today', value: 'today' },
        { name: 'This week', value: 'this_week' },
        { name: 'This month', value: 'this_month' },
        { name: 'All Time', value: 'this_year' },
    ];


    const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
    const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

    useEffect(() => {
        if (!companyId && salesUserDetails?.data) {
            router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [salesUserDetails?.data.company_id])


    return (
        <>
            <LoaderModal isOpen={isDefaultLoading || !companyId} />
            {/* {
                (!salesUserDetails?.data?.is_subscription && !isDefaultLoading) && <SubscriptionErrorModal
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    message={"Access Forbidden"}
                />
            } */}

            {branchId ?

                <>

                    <div className="md:px-7 md:py-3 lg:px-11 bg-white my-1 py-2 flex justify-between items-center">
                        <div className=' flex flex-col'>
                            <div className="border-muted max-w-max border rounded-md flex items-center">
                                <Button className='p-2 text-primary rounded-none' variant="light">Outgoing</Button>
                                <Button className='p-2 text-primary rounded-none' variant="unstyled">Incoming</Button>
                            </div>
                            <span className='text-lg font-semibold'>Invoice Management</span>
                            <span className='text-xxs'>Manage and track all your invoices and their statuses</span>
                        </div>
                        <div className='flex items-center space-x-2'>
                            <Button className='border-muted text-muted-foreground' variant="outlined">Import</Button>
                            <LinkButton href='./invoicing-new/create-invoice'>New Invoice</LinkButton>

                        </div>
                    </div>

                    <div className="md:px-7 md:py-3 lg:px-11 bg-white py-2">
                        <div className="border-muted border p-3 rounded-lg">
                            <div className="flex justify-between">
                                <div className='flex flex-col'>
                                    <span className='text-lg font-semibold'>Overview</span>
                                    <span className='text-xxs'>Manage and track all your invoices and their statuses</span>
                                </div>
                                <div className='space-x-1'>
                                    {periodOptions.map((v, key) =>
                                        <Button className={cn(filterOption !== v.value && 'border-muted text-muted-foreground')} key={key} variant={filterOption === v.value ? "default" : "outlined"} onClick={() => setFilterOption(v.value as any)} > {v.name}</Button>
                                    )}
                                </div>
                            </div>
                            <InvoiceOverviewStats
                                branch={branchId}
                                company={companyId}
                                filterOption={filterOption}
                            />
                        </div>



                        <div className='mt-5 border-muted border rounded-lg p-4'>
                            <div className=' flex flex-col space-y-2'>
                                <div className="border-muted max-w-max border rounded-md flex items-center">
                                    <Button className='p-2 text-primary rounded-none' variant={tab === "Invoices" ? "light" : "unstyled"} onClick={() => setTab("Invoices")}>Invoices</Button>
                                    <Button className='p-2 text-primary rounded-none' variant={tab === "Branches" ? "light" : "unstyled"} onClick={() => setTab("Branches")}>Branches</Button>
                                </div>
                                <span className='text-lg font-semibold'>{tab === "Invoices" ? "Invoice" : "Branch"} Overview</span>
                                <div className='flex items-center space-x-2'>
                                    {tab === "Invoices" && invoiceType.map((v, key) =>
                                        <Button className='border py-1 font-light' key={key} variant={invoice_type === v.value ? "default" : "light"} onClick={() => setInvoice_type(v.value as any)} > {v.name}</Button>
                                    )}
                                </div>
                                <div className='mt-1 border-muted border rounded-lg p-2'>
                                    {tab === "Invoices" ? <InvoicesTable companyId={companyId} invoice_type={invoice_type} /> : <InvoicingBranchBaseDashboard />}
                                </div>
                            </div>

                        </div>
                    </div>
                </>

                :
                <>
                    {!isCompanyLoading && <ViewBranchesDialog company={companyId}
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        companyList={companies?.results} link={'/sales/invoicing'} />}
                </>
            }
        </>
    );
};

export default Page;