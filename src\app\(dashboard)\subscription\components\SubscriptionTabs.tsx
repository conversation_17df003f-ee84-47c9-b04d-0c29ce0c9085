

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>, <PERSON><PERSON><PERSON>lose, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/core";
import React from "react";
import SixMonthSubscription from "./SixMonthSubscription";
import AnnualSubscription from "./AnnualSubscription";
import { useBooleanStateControl } from "@/hooks";
// import SubscriptionSummary from "./Modals/SubscriptionSummary";
import { Separator } from "@radix-ui/react-select";
import { X, MessageSquare, Mail, ExternalLink, Phone } from "lucide-react";
// import SubscriptionSummary from "../misc/components/Modals/SubscriptionSummary";
// import SubscriptionSummary from "../misc/components/Modals/SubscriptionSummary";

export default function SubscriptionTabs() {

  const {
    state: _isSubscriptionSummaryOpen,
    setState: _setSubscriptionSummaryState,
    setTrue: _openSubscriptionSummaryModal,
  } = useBooleanStateControl();


  const [isContactModalOpen, setIsContactModalOpen] = React.useState(false);
  // Function to open the contact modal
  const handleSubscribeClick = () => {
    setIsContactModalOpen(true);
  };

  return (
    <div className="mx-4 sm:mx-6 md:mx-10 lg:mx-[2.625rem]">
      <Tabs className="w-full  " defaultValue="6-Months-Subscription">
        <div className="w-full bg-[#E6EDFF] pt-4  md:pt-8 pb-3 rounded-md">
          <div className="flex flex-col [@media(min-width:950px)]:flex-row justify-between items-center  md:px-6 lg:px-6">
            <TabsList className="flex flex-col [@media(min-width:450px)]:flex-row 2xl:gap-[5.4375rem] xl:gap-12 ">
              <TabsTrigger
                className="  text-[#4E4E4E]  leading-[1.3638rem] font-medium data-[state=active]:border-b-2 data-[state=active]:border-[#5879FD] data-[state=active]:text-[#032180]"
                value="6-Months-Subscription"
              >
                6 Months Subscription
              </TabsTrigger>
              <TabsTrigger
                className=" text-[#4E4E4E]  leading-[1.3638rem] font-medium data-[state=active]:border-b-2 data-[state=active]:border-[#5879FD] data-[state=active]:text-[#032180]"
                value="Annual-Subscription"
              >
                Annual Subscription
              </TabsTrigger>
            </TabsList>

            <div className="">
              <Button
                className="text-white px-4 lg:px-[5.25rem] py-2 sm:py-3 rounded-lg text-sm sm:text-base"
                onClick={handleSubscribeClick}
              >

                Subscribe
              </Button>
            </div>

          </div>
        </div>

        <div className="mt-4">
          <TabsContent value="6-Months-Subscription">
            <div className="">
              <SixMonthSubscription />
            </div>
          </TabsContent>

          <TabsContent value="Annual-Subscription">
            <div className="">
              <AnnualSubscription />
            </div>
          </TabsContent>
        </div>
      </Tabs>


      {/* <SubscriptionSummary
        isSubscriptionSummaryOpen={isSubscriptionSummaryOpen}
        openSubscriptionSummaryModal={openSubscriptionSummaryModal}
        setSubscriptionSummaryState={setSubscriptionSummaryState}
      /> */}


      {/* Contact Support Modal TEMPORARY*/}
      <Dialog open={isContactModalOpen} onOpenChange={setIsContactModalOpen}>
        <DialogContent className="max-w-md sm:max-w-[500px] p-0 overflow-hidden rounded-lg">
          <div className="bg-gradient-to-r from-[#032180] to-blue-800 p-6">
            <div className="flex items-center justify-between">
              <DialogTitle className="text-xl font-semibold text-white">Contact Support</DialogTitle>
              <DialogClose className="text-white hover:bg-white/20 rounded-full p-1 transition-colors">
                <X className="h-5 w-5" />
              </DialogClose>
            </div>
            <p className="text-blue-100 mt-2 text-sm">
              Our support team is ready to assist you with subscription information for all modules
            </p>
          </div>

          <div className="p-6 pt-5">
            <div className="flex items-center mb-4">
              <MessageSquare className="h-5 w-5 text-[#032180] mr-2" />
              <h3 className="font-medium">Get in touch with us</h3>
            </div>

            <div className="grid gap-4 mb-6">
              <Card className="border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                <CardContent className="p-4 flex items-center">
                  <div className="bg-blue-100 p-2 rounded-full mr-4">
                    <Mail className="h-5 w-5 text-[#032180]" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Email us at</p>
                    <a
                      className="text-[#032180] hover:text-blue-800 font-medium flex items-center"
                      href="mailto:<EMAIL>"
                    >
                      <EMAIL>
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </div>
                </CardContent>
              </Card>

              <Card className="border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                <CardContent className="p-4 flex items-center">
                  <div className="bg-blue-100 p-2 rounded-full mr-4">
                    <Phone className="h-5 w-5 text-[#032180]" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Call us at</p>
                    <a
                      className="text-[#032180] hover:text-blue-800 font-medium flex items-center"
                      href="tel:02013300187"
                    >
                      02013300187
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Separator className="my-4" />

            {/* <div className="flex justify-end gap-3">
                            <Button
                                className="border-blue-200 hover:bg-blue-50 hover:text-[#032180]"
                                variant="outlined"
                                onClick={handleCloseModal}
                            >
                                Close
                            </Button>
                            <Button
                                className="bg-[#032180] hover:bg-blue-800 text-white"
                                onClick={() => window.open("https://paybox360.com/support", "_blank")}
                            >
                                Visit Support Center
                            </Button>
                        </div> */}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

