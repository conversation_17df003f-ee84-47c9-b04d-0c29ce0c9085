import { managementAxios } from '@/lib/axios';

import { useQuery } from '@tanstack/react-query';

import {
  // EmployeeData,
  AllEmployeeData,
} from '../../types';

interface FetchEmployeeOptions {
  pageIndex?: number;
  pageSize?: number;
  // startDate: string | undefined
  // endDate: string | undefined;.
  search?: string;
  companyid: string;
}

export const getAllEmployeesWithoutPermission = async ({ companyid }: FetchEmployeeOptions) => {
  const response = await managementAxios.get(
    `/payroll/company_employees_data?company_uuid=${companyid}`
  );

  return response.data as AllEmployeeData;
};

export const useAllEmployeesWithoutPermission = (fetchOptions: FetchEmployeeOptions) => {
  return useQuery({
    queryKey: ['employee', fetchOptions],
    queryFn: () => {
      if (fetchOptions) return getAllEmployeesWithoutPermission(fetchOptions);
    },
    enabled: !!fetchOptions,
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};
