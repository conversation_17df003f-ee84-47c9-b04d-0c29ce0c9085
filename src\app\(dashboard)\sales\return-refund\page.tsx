"use client"
// import { redirect } from 'next/navigation';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

// import { BranchSwitcher } from '@/app/(dashboard)/sales/misc/components/BranchSwitcher';
import { ReturnRefundTable } from '@/app/(dashboard)/sales/misc/components/tables/ReturnAndRefundTable';
import { ViewBranchesDialog } from '@/app/(dashboard)/stock/misc/components';
import { LoaderModal } from '@/components/core';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';

// const CALLBACK_URL = '/sales';
const Page = () => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const branchId = searchParams.get('branch') || '';
  const companyId = searchParams.get('company') || '';

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  useEffect(() => {
    if (!companyId && salesUserDetails?.data) {
      router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])




  return (
    <div>
      <LoaderModal isOpen={isDefaultLoading || !companyId} />
      {/* {
        (!salesUserDetails?.data?.is_subscription && !isDefaultLoading) && <SubscriptionErrorModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          message={"Access Forbidden"}
        />
      } */}

      {branchId ?
        <div className="mt-4 md:px-7 lg:px-11">
          <ReturnRefundTable />
        </div>
        :
        <>
          {!isCompanyLoading && <ViewBranchesDialog company={companyId}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            companyList={companies?.results} link={'/sales/return-refund'} />}
        </>
      }
    </div>
  );
};

export default Page;
