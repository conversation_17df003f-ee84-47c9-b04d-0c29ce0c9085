/* eslint-disable @typescript-eslint/no-explicit-any */
export interface Branches {
  count: number;
  next?: string;
  previous?: null;
  branches: BranchesEntity[];
}

export interface BranchesEntity {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  address: string;
  vat: number;
  location: string;
  is_super_branch: boolean;
  user: string;
}

export interface BranchesResponse {
  data: {
    count: number;
    message: string;
    branches: BranchesEntity[];
  };
}

export type SingleCategory = {
  company: string;
  created_at: string;
  id: string;
  name: string;
  updated_at: string;
  user: string;
  has_products?: string;
  subcategories: string;
};

export interface StockCategories {
  data: {
    categories: Array<SingleCategory>;
    count: number;
    message: string;
    total_categories: number;
  };
  errors: null;
  status: string;
  status_code: number;
}

export type SingleSubCategory = {
  company: string;
  created_at: string;
  id: string;
  name: string;
  category: string;
  category_name: string;
  created_by: string;
};

export interface ISubcategory {
  status: string
  status_code: number
  data: Data
  errors: null
}

export interface Data {
  message: string
  subcategories: Subcategory[]
  count: number
  total_subcategories: number
}

export interface Subcategory {
  id: string
  created_at: string
  name: string
  company: string
  created_by: string
  category: string
  category_name: string
}

export interface StockSubCategories {
  data: {
    subcategories: Array<SingleSubCategory>;
    count: number;
    message: string;
    total_subcategories: number;
  };
  errors: null;
  status: string;
  status_code: number;
}

export interface SuppliersResponse {
  errors: null;
  status: string;
  status_code: number;
  data: {
    count: number;
    message: string;
    total_supplier: number;
    suppliers: Array<Supplier>;
  };
}

export type Supplier = {
  address: string;
  company: string;
  created_at: string;
  email: string;
  id: string;
  name: string;
  phone_number: string;
  updated_at: string;
  user: string;
};

export type Product = {
  company: string;
  created_at: string;
  id: string;
  name: string;
  updated_at: string;
  user?: string;
  selected_branches?: Array<string>;
  branches_count?: number;
  product_price: number;
  selling_price: number;
  sku: string;
  vat?: number;
};

export type ProductResponse = {
  status: string;
  status_code: number;
  data: {
    count: number;
    total_products: number;
    message: string;
    products: Array<Product>;
  };
};

export type Variant = {
  description: string;
  quantity: number;
  stock_price: number;
  selling_price: number;
};

export type SingleVariant = {
  id: string;
  created_at: string;
  updated_at: string;
  description: string;
  quantity: number;
  stock_alert: number;
  stock_price: string;
  selling_price: string;
  stock_value: string;
  discount: number;
  currency: string;
  stock_item: string;
  supply_history: string;
  branch: string;
  company: string;
  uploaded_by: string;
  has_unique_ids?: string;
};

export type StockItem = {
  supplied_by?: string;
  category: string;
  item: string;
  quantity: number;
  stock_price: number;
  stock_alert?: number;
  expiry_date?: string;
  selling_price: number;
  image?: File | string;
  name?: string;
  has_bulk: 'Yes' | 'No';
  sku: string,
  measure_unit: string,
  bulk_name?: string,
  subcategory?: string,
  bulk_quantity?: number,
  vat?: number,
  show_in_web_stores: 'Yes' | 'No',
  description?: string,
};

export type StockOutLists = {
  company: string;
  branch: string;
  comment: string;
  stocks: StockItem[];
};

export type StockItemSimple = {
  supplied_by?: string;
  category: string;
  item: string;
  stock_price: number;
  stock_alert?: number;
  expiry_date?: string;
  selling_price: number;
  image?: File | string;
  name?: string;
  quantity: number;
  show_in_web_stores: 'Yes' | 'No',
  description?: string,
  subcategory?: string,
  vat?: number,
};

export type StockOutListsSimple = {
  company: string;
  branch: string;
  stocks: StockItemSimple[];
  comment: string;
};

export type StockResultsEntity = {
  id: string;
  updated_at: string;
  company: string;
  branch: string;
  category: string;
  item: string;
  quantity: number;
  supervisor: string;
  stock_on_hold: number;
};

export type StockTableResponse = {
  stock_details: StockResultsEntity[];
  count: number;
  stock_value: number;
  total_count: number;
  total_stocks: number;
};

export interface IndustriesResponse {
  count: number;
  next?: null;
  previous?: null;
  results?: ResultsEntity[];
}
export interface ResultsEntity {
  id: number;
  industry: string;
  created_at: string;
  updated_at: string;
}

export interface PinVerificationResponse {
  status: boolean;
  pin: boolean;
  message: string;
}

export type CompanyListData = CompanyListEntity[];

export interface CompanyListEntity {
  id: string;
  company_name: string;
  account: string;
  company_wallet_type: string;
  industry: string;
  cac_num: string;
  company_status: string;
  verification_status: string;
  size: number;
  registration_date: string;
  registration_submission_date: string;
  corporate_id: string;
  on_boarded_corporate_user: boolean;
  corporate_account_created: boolean;
}

export interface AllCompaniesBranchInfo {
  message: string;
  companies: ICompanies[];
  count: number;
  total_companies: number;
}

export interface ICompanies {
  id: string
  company_name: string
  branches: number
  teams: number
  total_stock: number
  stock_on_hold: number
  stock_value: number
  is_subscription: boolean
  is_free_trial: boolean
  is_paid_subscription: boolean
  // subscription_info?: SubscriptionInfo
  active_subscriptions: ActiveSubscription[]
  not_started_subscriptions: NotStartedSubscription[]
}

export interface ActiveSubscription {
  id: number
  status: string
  access_type: string
  plan: any
  plan_id: any
  overall_sub_days: string
  created_at: string
  pos_included: boolean
  modules: Module[]
  has_active_modules: boolean
  has_not_started_modules: boolean
}

export interface Module {
  module_subscription_id: number
  module: Module2
  plan_name: string
  plan_id: number
  start_date: string
  end_date: string
  status: string
  is_active: boolean
  is_not_started: boolean
}

export interface Module2 {
  id: number
  name: string
  code: string
  description: string
  is_premium: boolean
}

export interface NotStartedSubscription {
  id: number
  status: string
  access_type: string
  plan: any
  plan_id: any
  overall_sub_days: string
  created_at: string
  pos_included: boolean
  modules: Module3[]
  has_active_modules: boolean
  has_not_started_modules: boolean
}

export interface Module3 {
  module_subscription_id: number
  module: Module4
  plan_name: string
  plan_id: number
  start_date: string
  end_date: string
  status: string
  is_active: boolean
  is_not_started: boolean
}

export interface Module4 {
  id: number
  name: string
  code: string
  description: string
  is_premium: boolean
}



export type RecipesResponse = {
  company: string;
  created_at: string;
  id: string;
  name: string;
  recipes: Recipe[];
  updated_at: string;
};

export type Recipe = {
  created_at: string;
  finished_product: string;
  id: string;
  product: string;
  quantity: string;
  updated_at: string;
};

export type PriceListResponse = {
  data: {
    count: number;
    message: string;
    price_lists: Array<PriceList>;
    total_price_lists: number;
  };
  errors: string | null;
  status: string;
  status_code: number;
};

export interface PriceList {
  id?: string;
  created_at?: string;
  updated_at?: string;
  size_or_volume: string | undefined;
  price: number | undefined;
  all_branches: boolean | undefined;
  currency?: string;
  category: string | undefined;
  item: string | undefined;
  company?: string;
  user?: string;
  selected_branches?: Array<{ value: string }>;
  branches_count?: number;
}

export type InputPriceListDto = {
  company: string;
  price_lists: Array<SingleInputPriceList>;
};

export interface SingleInputPriceList {
  id?: string;
  image?: string | File;
  category: string;
  branch?: string;
  item: string;
  recipe?: string;
  size_or_volume: string;
  price: number;
  all_branches: boolean;
  selected_branches?: Array<{ value: string }>;
}

export type PriceVariationListDto = {
  company: string;
  products: Array<PriceVariationListSingleDto>;
}

export type PriceVariationListSingleDto = {
  product: string;
  name?: string;
  product_price: string;
  selling_price: string;
  method: string;
  price_variations: {
    [key: string]: PriceVariation
  };
  is_edited?: boolean;
};

export interface PriceVariation {
  price: string;
  price_tag: string;
  percentage: number;
  tag?: string;
};

export type TeamsResponse = {
  company_name: string;
  count: number;
  next: null;
  previous: null;
  results?: Array<Teams>;
};

export type Teams = {
  id: string;
  team_name: string;
  team_type: string;
  company_id: string;
  company_name: string;
  branch: string;
  members: Array<Members>;
  requisitions: number;
  created_at: string;
  allocated_amount: number;
  spent_amount: number;
  budget: null;
  no_of_members: number;
  approved_req: number;
  pending_req: number;
};

export type Members = {
  id: string;
  req_no: 0;
  phone_no: string;
  email: string;
  user_email: string;
  first_name: string;
  last_name: string;
  team_name: string;
  company_name: string;
  branch_name: null;
  user_phone_no: string;
  role: string;
  is_registered: boolean;
  created_at: string;
  status: string;
};

export type TeamMemberResponse = {
  count: number;
  next: null;
  previous: null;
  results: Array<TeamMember>;
};

export type TeamMember = {
  id: string;
  req_no: number;
  phone_no: string;
  email: string;
  user_email: string;
  first_name: string;
  last_name: string;
  team_name: string;
  company_name: string;
  branch_name: string;
  user_phone_no: string;
  role: string;
  is_registered: boolean;
  created_at: string;
  status: string;
};

export type TeamMemberResponseData = {
  count: number;
  next: null;
  previous: null;
  results: Array<TeamMemberData>;
};

export type TeamMemberData = {
  id: string;
  company_name: string;
  email: string;
  first_name: string;
  last_name: string;
  role_status: string;
  role: string;
  status: string;
  phone_no: string;
  is_registered: boolean;
  branch_name: string;
};

export interface ITop4Branches {
  branches: Branch[]
}

export interface Branch {
  branch: string
  branch_name: string
  amount: string
  percentage: string
}

export interface ISalesComparison {
  message: string
  global_transactions: number
  total_branches: number
  sales_comparison: SalesComparison[]
}

export interface SalesComparison {
  month: string
  branch_name: string
  sales_value: string
}

export interface ITop4Customer {
  customers: Customer[]
}

export interface Customer {
  customer: string
  customer_name: string
  orders: number
  revenue: string
  last_purchased: string
}
export interface ICashAtHand {
  report: Report[]
}

export interface Report {
  branch_name: string
  cash_balance: string
  last_updated: string
}

export interface IProfitTracking {
  message: string
  total_sales: TotalSale[]
}

export interface TotalSale {
  month: string
  gross_profit: string
}

export interface IStockSalesReport {
  report: StockSalesReport[]
}

export interface StockSalesReport {
  month: string
  stocks_value: string | number
  sales_value: string | number
}


export interface ITopProduct {
  message: string
  products: IProduct[]
}

export interface IProduct {
  count: number
  product: string
  product_id: string
  branch: string
  total_quantity: number
  price: string
  sales_value: string
  profit: string
}

export interface IClerk {
  report: IClerkReport[]
}

export interface IClerkReport {
  clerk: string
  clerk_name: string
  branch_name: string
  total_sales: number
  amount_sold: string
  last_transaction: string
}


export interface ISingleCompanyOverviewResponse {
  branches: number
  teams: number
  total_sales_value: number
  total_sales_count: number
  total_products_sold: number
  average_order_value: number
  returning_customer_rate: number
  global_sales_value: GlobalSalesValue
  global_gross_profit: GlobalGrossProfit
  global_transaction_count: GlobalTransactionCount
}

export interface GlobalSalesValue {
  today: number
  yesterday: number
  this_week: number
  last_week: number
  this_month: number
  last_month: number
  this_year: number
  last_year: number
}

export interface GlobalGrossProfit {
  today: number
  yesterday: number
  this_week: number
  last_week: number
  this_month: number
  last_month: number
  this_year: number
  last_year: number
}

export interface GlobalTransactionCount {
  today: number
  yesterday: number
  this_week: number
  last_week: number
  this_month: number
  last_month: number
  this_year: number
  last_year: number
}

export type SingleCompanyOverviewResponse = {
  status: string;
  status_code: number;
  data: CompanyInfo;
  errors: null;
};
export type CompanyInfo = {
  company: string;
  branches: number;
  teams: number;
  total_stock_value: number;
  total_sales_values: number;
  total_expected_profit: number;
};

export type SingleStockCount = {
  id: string;
  categories_count: number;
  products_count: number;
  quantity: number;
  supervisor: number;
  branch: string;
};
export type StockCountResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    branches: Array<SingleStockCount>;
    count?: number;
  };
};

export type StockOutDashboardStatsResponse = {
  status: string;
  status_code: number;
  data: {
    company: string;
    branch_location: string;
    members: number;
    request: number;
    requests?: number;
    approved: number;
    pending: number;
    declined: number;
  };
  errors: null;
};

export type StockOutResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    stockout_requests: Array<SingleStockOutRequest>;
    count: number;
    total_stockout_requests: number;
  };
};

export type SingleStockOutRequest = {
  id: string;
  created_at: string;
  updated_at: string;
  quantity_requested: number;
  quantity_approved: number;
  cost_price: string;
  status: string;
  request_reason: string;
  requested_by: string;
  category: string;
  item: string;
  branch: string;
  company: string;
  approved_by: string | null;
  date_approved?: string | null;
  product?: string;
};

export type StockRequestResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    requests: Array<SingleStockRequest>;
    count: number;
    total_requests: number;
  };
};

export type SingleStockRequest = {
  request_id: string;
  category: number;
  item: number;
  quantity: number;
  requested_by: string;
  status: string;
  date: string;
  type: string;
  branch: string;
  approved_by?: string;
};

export type SingleStockRequestDashboardStatsResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    requests: {
      approved_by: string;
      request_id: string;
      category: number;
      item: number;
      quantity: number;
      requested_by: string;
      status: string;
      date: string;
      type: string;
      branch: string;
    }[];
  };
  errors: null;
};

export type SingleStockRequestsDetailsResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    requests: Array<SingleStockRequestsDetails>;
    count: number;
  };
  errors: null;
};

// export type AllStockRequestsDetailsResponse = StockRequestsDetailsResponse<
//   Array<SingleStockRequestsDetails>
// >;

// export type SingleStockRequestsDetailsResponse =
//   StockRequestsDetailsResponse<SingleStockRequestsDetails>;

// SingleStockRequestsDetails
export type SingleStockRequestsDetails = {
  id: string;
  request_id: string;
  category: string;
  item: string;
  quantity_requested: number;
  quantity_approved: number;
  category_id: string;
  available_stock: number;
  status: string;
  stock_item_has_ids: boolean;
  stock_item: string;
  created_at: string;
  supply_branch: string;
};

export type CompanyProductsOverviewResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    products: Array<SingleCompanyProductsOverview>;
    count: number;
    total_products: number;
  };
  errors: null;
};

export type SingleCompanyProductsOverview = {
  id: string;
  company: string;
  category: string;
  product: string;
  supplier: string;
  variants: string;
  sku?: string;
  category_id?: string;
  quantity: number;
  currentQuantity?: number;
  totalQuantity?: number;
  stock_price: number;
  selling_price: number;
  barcode: null;
  stock_on_hold: number;
  unique_ids: boolean;
  stock_item_id: string;
  product_image_1?: string;
  subcategory?: string;
  subcategory_name?: string;
  unique_id?: string;
  description?: string;
  unlist?: boolean;
};

export type CategoriesListDto = {
  id?: string;
  value: string;
  label: string;
  quantity?: string;
};

export type SingleStockFormValues = {
  company: string;
  branch: string;
  request_id: string;
  decline_reason?: string;
  requests?: Array<{
    id: string;
    quantity_requested: number;
    available_stock: number;
    item: string;
    stock_item_has_ids?: boolean | null;
    stock_item?: string | null;
    selected_unique_ids?: Array<{ value: string; label: string }>;
    unique_ids?: string | null;
  }>;
};

export type InventoryStatusStocksDto = {
  date: string;
  opening_stock: number;
  opening_value: string;
  closing_stock: number;
  closing_value: string;
  branch: string;
  company: string;
};
export type InventoryStatusStocksResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    inventory_status: Array<InventoryStatusStocksDto>;
    count: number;
    total_inventory_status: number;
  };
  errors: null;
};

export type InventoryStatusQuantityDto = {
  id: string;
  created_at: string;
  updated_at: string;
  opening_quantity: number;
  closing_quantity: number;
  opening_value: string;
  closing_value: string;
  item: string;
  branch: string;
  company: string;
};
export type InventoryStatusQuantityResponse = {
  status: string;
  status_code: number;
  data: {
    message: "successfully fetched branch's inventory status.";
    inventory_status: Array<InventoryStatusQuantityDto>;
    count: number;
    total_inventory_status: number;
  };
  errors: null;
};

export interface StockHistoriesResponse {
  status: string;
  status_code: number;
  data: {
    message: string;
    stock_histories: Array<StockHistory>;
    count: number;
    total_stock_histories: number;
  };

  errors: null;
}

export type StockHistory = {
  id: string;
  created_at: string;
  updated_at: string;
  type: string;
  price: string;
  quantity_before: string;
  quantity_after: string;
  category: string;
  item: string;
  supplier: string | null;
  branch: string;
  company: string;
};

export type StockRequestProductsResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    requests: Array<StockRequestProduct>;
    count: number;
  };
  errors: null;
};

export type StockRequestProduct = {
  id: string;
  request_id: string;
  category: string;
  item: string;
  quantity_requested: number;
  category_id: string;
  available_stock: number;
};

export type SinglePurchaseOrders = {
  id?: string;
  category: string;
  item: string;
  quantity: number;
};

export type InputPurchaseOrdersDto = {
  company: string;
  branch: string;
  supplier: string;
  orders: Array<SinglePurchaseOrders>;
};

export interface PurchaseOrdersEntity {
  branch: string;
  request_id: string;
  date_issued: string;
  supplier: string;
  category: number;
  item: number;
  quantity: number;
  status: string;
  date_delivered: string | null;
}

export interface PurchaseOrdersListEntity {
  id: string;
  created_at: string;
  updated_at: string;
  quantity_requested: number;
  quantity_delivered: string;
  status: string;
  date_delivered: string | null;
  request_id: string;
  company: string;
  branch: string;
  requested_by: string;
  category: string;
  item: string;
  supplier: string;
}

export type OrderSummaryResponse = {
  status: string;
  status_code: number;
  data: {
    supplier: string;
    category: number;
    item: number;
    quantity: number;
    status: string;
  };
  errors: null;
};

export interface StockTransferResponse {
  status: string;
  status_code: number;
  data: {
    message: string;
    transfers: Array<SingleStockTransfer>;
    count: number;
    total_transfers: number;
  };
  errors: null;
}
export type SingleStockTransfer = {
  date: string;
  request_id: string;
  approved_by: string;
  branch: string;
  category: number;
  item: number;
  quantity: number;
  status: string;
};

export type InputStockTransferDto = {
  company: string;
  branch: string;
  transfer_to: string;
  transfers: Array<SingleStockTransferList>;
};

export interface SingleStockTransferList {
  id?: string;
  category: string;
  item: string;
  quantity: number;
  variant?: string;
  stock_price: number;
  selling_price: number;
}

//

export type SingleStockTransferDetailsResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    transfers: Array<SingleStockTransferDetails>;
    count: number;
  };
};

export type SingleStockTransferDetails = {
  id: string;
  created_at: string;
  updated_at: string;
  quantity: number;
  stock_price: string;
  selling_price: string;
  status: string;
  request_id: string;
  company: string;
  branch: string;
  transfer_to: string;
  approved_by: string;
  category: string;
  item: string;
  variant: string | null;
};

export type SingleStockTransferDetailsDashboardStatsResponse = {
  status: string;
  status_code: number;
  data: {
    date: string;
    supply_to: string;
    receiver: null;
    category: number;
    item: number;
    quantity: number;
    status: string;
  };
  errors: null;
};

export type UniqueIds = {
  id: string;
  created_at: string;
  unique_id: string;
  stock_item: null;
  stock_variant: string;
  branch: string;
  company: string;
  uploaded_by: string;
};

export type StockVariantsUniqueIdsResponse = {
  status: string;
  status_code: number;
  data: {
    message: string;
    unique_ids: Array<UniqueIds>;
    count: number;
    total_unique_ids: number;
  };
};

export interface CompaniesData {
  companies: number,
  branches: number,
  team_members: number,
  total_stock: number,
  stock_value: number
}

export interface Companies {
  status: string;
  status_code: number;
  data: CompaniesData;
  errors: null;
}

export type SingleSupplyHistory = {
  id?: string;
  category: string;
  item: string;
  quantity: number;
  created_at: string;
};

export type SupplyHistory = {
  status: string;
  status_code: number;
  data: {
    message: string;
    supply_history: Array<SingleSupplyHistory>;
    count: number,
    total_history: number
  };
  errors: null;
};

export type PriceTag = {
  name: string;
  company: string;
  id: string;
  created_at: string;
  updated_at: string;
  system_default: boolean;
  created_by: string;
  updated_by: string;
}

export interface BarcodeResponse {
  status: string;
  status_code: number;
  data: {
    barcode?: string;
    message?: string;
  };
  errors: null;
}
