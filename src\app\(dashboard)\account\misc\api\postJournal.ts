import { managementAxios } from "@/lib/axios";;
import { useMutation } from "@tanstack/react-query";
import { JournalEntryFormData } from "../../create-journal/page";

type JournalEntryFormDataWithoutDate = Omit<JournalEntryFormData, 'journal'> & {
    journal: Omit<JournalEntryFormData['journal'], 'date'>;
};

type JournalEntryFormDataWithStringDate = JournalEntryFormDataWithoutDate & {
    journal: JournalEntryFormDataWithoutDate['journal'] & {
        date: string;
    };
};

export type JournalEntry = {
    company: string;
    branch?: string;
    date: string;
    references: string;
    notes: string;
    journal_type: "CASH" | "BANK" | "SALES" | "PURCHASE" | string;
    transaction_type: "debit" | "credit";
    is_draft: boolean;
    lines: JournalLine[];
};

export type JournalLine = {
    account: string;
    description: string;
    debit_amount: number;
    credit_amount: number;
    bal_before: number;
    bal_after: number;
    customer: string;
    // tax: number;
};

type Response = {
    message: string
}


const postJournal = async ({ journal }: JournalEntryFormDataWithStringDate) => {
    const response = await managementAxios.post('api/v1/accounting/journals/', journal)
    return response.data as Response
}

const postDraftJournal = async ({ journal }: Partial<JournalEntryFormDataWithStringDate>) => {
    const response = await managementAxios.post('api/v1/accounting/journals/', journal)
    return response.data as Response
}

export const UsePostJournal = () => {
    return useMutation({
        mutationFn: postDraftJournal,
    })
}

export const UsePostDraftJournal = () => {
    return useMutation({
        mutationFn: postJournal,
    })
}