"use client"
import { <PERSON><PERSON> } from '@/components/core'
import Link from 'next/link'
import React, { useState } from 'react'
import SubscriptionSummary from './misc/components/Modals/SubscriptionSummary'

import { useSearchParams } from 'next/navigation'
import EnhancedSubscriptionModules from './misc/components/EnhancedSubscriptionModules'
import { Plan, SubScriptionModulePropTypes } from './misc/api/getSubscriptionPlan'
import GenerateInvoice from './misc/components/Modals/GenerateInvoice'
import PromotionalBanner from './misc/components/PromotionalBanner'
import { convertNumberToNaira } from '@/utils/currency'
import { addCommasToNumber } from '@/utils/numbers'
// import { useSpendManagementSingleCompany } from '../spend-management/misc/api/company/getSpendManagementSingleCompany';


const Page = () => {
  const [selectedModules, setSelectedModules] = useState<SubScriptionModulePropTypes[]>([]); // Store entire module data
  const [isAnySelected, setIsAnySelected] = useState(false); // Track if any module is selected
  const [selectedPlans, setSelectedPlans] = useState<Map<number, Plan | null>>(new Map()); // Track selected plan for each module
  const [showSummaryModal, setShowSummaryModal] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [moduleTokenUnits, setModuleTokenUnits] = useState<Map<number, number>>(new Map()) // Track token units for each module

  const search = useSearchParams()
  const company_id = search.get("companyId")
  const companyName = search.get("companyName")

  // Check if both Stock & Inventory and Sales are selected for promotional offer
  const hasStockAndSales = selectedModules.some(m => m.name.toLowerCase().includes('stock')) &&
    selectedModules.some(m => m.name.toLowerCase().includes('sales'))

  // Calculate total token units across all selected modules
  const getTotalTokenUnits = () => {
    return Array.from(moduleTokenUnits.values()).reduce((total, units) => total + units, 0)
  }

  // Calculate total amount (total tokens × 10)
  const getTotalAmount = () => {
    return getTotalTokenUnits() * 10
  }

  // const { data } = useSpendManagementSingleCompany(String(company_id))

  // console.log(data);


  return (
    <div className="min-h-screen bg-gray-50">
      <div className="md:px-[1.9375rem] px-4 py-6">
        <div className="bg-white border border-gray-200 w-full rounded-lg py-8 mt-5 shadow-sm">
          <div className="px-10">
            <p className='text-[#032282] font-bold lg:text-[1.875rem] md:text-[1.3625rem] text-[1.2rem] md:leading-[3.5806rem] leading-8'>
              Find the Perfect Plan for Your Business
            </p>
            <p className='max-w-lg text-xs md:text-sm leading-[1.3638rem] font-medium text-[#242424] '>
              Choose the right module for your business and streamline HR, Spend, Stock, and Sales with powerful tools for efficiency and growth.

            </p>
            <p className='max-w-lg text-xs md:text-sm leading-[1.3638rem] font-medium text-[#242424] mt-2'>
              Select one or more modules and make payment to get started.

            </p>

            <div className="mt-6 flex flex-col lg:flex-row items-start justify-between gap-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href={`/subscription-dashboard?companyId=${company_id}&companyName=${companyName}`}>
                  <Button className="text-[#032282] bg-white border border-[#032282] hover:bg-[#032282] hover:text-white px-6 py-2 rounded-lg font-medium transition-all duration-200">
                    Dashboard
                  </Button>
                </Link>

                {isAnySelected && (
                  <Button
                    className='bg-[#032282] hover:bg-[#032282]/90 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200'
                    onClick={() => setShowSummaryModal(true)}
                  >
                    Subscribe Now
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Promotional Banner */}
        <PromotionalBanner
          className="mt-6"
          isVisible={hasStockAndSales}
        />

        <div className='flex items-start gap-10 '>
          <EnhancedSubscriptionModules
            moduleTokenUnits={moduleTokenUnits}
            selectedModules={selectedModules}
            selectedPlans={selectedPlans}
            setIsAnySelected={setIsAnySelected}
            setModuleTokenUnits={setModuleTokenUnits}
            setSelectedModules={setSelectedModules}
            setSelectedPlans={setSelectedPlans}
            setShowSummaryModal={setShowSummaryModal}
          />

          {/* Total Summary */}
          <div className="lg:w-60 w-full mt-10">
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="text-sm font-semibold text-[#032282] mb-4">Total Summary</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs font-medium text-gray-600">Total Token Units:</span>
                  <span className="text-sm font-bold text-[#032282]">{addCommasToNumber(getTotalTokenUnits())}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs font-medium text-gray-600">Total Amount:</span>
                  <span className="text-lg font-bold text-[#032282]">{convertNumberToNaira(getTotalAmount())}</span>
                </div>
                <p className="text-xs text-gray-500 text-center">
                  Select modules and enter token units to see pricing
                </p>
              </div>

              <div className='mt-2'>
                {isAnySelected && (
                  <Button
                    className='bg-[#032282] hover:bg-[#032282]/90 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200'
                    size={"fullWidth"}
                    onClick={() => setShowSummaryModal(true)}
                  >
                    Subscribe Now
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        <SubscriptionSummary
          moduleTokenUnits={moduleTokenUnits}
          selectedModules={selectedModules}
          setSelectedModules={setSelectedModules}
          setShowSuccessModal={setShowSuccessModal}
          setShowSummaryModal={setShowSummaryModal}
          showSummaryModal={showSummaryModal}
        />


        <GenerateInvoice
          company_id={company_id as string}
          companyName={companyName as string}
          description="An invoice has been sent to your registered email"
          heading='Invoice Sent Successfuly'
          setShowSuccessModal={setShowSuccessModal}
          showSuccessModal={showSuccessModal}

        // email={data?.results[0]?.em}


        />


      </div>

    </div>
  );
}

export default Page;
