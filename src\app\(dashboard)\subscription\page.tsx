"use client"
import { But<PERSON> } from '@/components/core'
import Link from 'next/link'
import React, { useState } from 'react'
import SubscriptionSummary from './misc/components/Modals/SubscriptionSummary'

import { useSearchParams } from 'next/navigation'
import SubscriptionModules from './misc/components/SubscriptionModules'
import { Plan, SubScriptionModulePropTypes } from './misc/api/getSubscriptionPlan'
import GenerateInvoice from './misc/components/Modals/GenerateInvoice';
// import { useSpendManagementSingleCompany } from '../spend-management/misc/api/company/getSpendManagementSingleCompany';


const Page = () => {
  const [selectedModules, setSelectedModules] = useState<SubScriptionModulePropTypes[]>([]); // Store entire module data
  const [isAnySelected, setIsAnySelected] = useState(false); // Track if any module is selected
  const [selectedPlans, setSelectedPlans] = useState<Map<number, Plan | null>>(new Map()); // Track selected plan for each module
  const [showSummaryModal, setShowSummaryModal] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false)

  const search = useSearchParams()
  const company_id = search.get("companyId")
  const companyName = search.get("companyName")

  // const { data } = useSpendManagementSingleCompany(String(company_id))

  // console.log(data);


  return (
    <div>
      <div className="md:px-[1.9375rem] px-4">
        <div className="bg-[#E6EDFF] w-full rounded-md py-[1.625rem] mt-5 px-1rem">
          <div className="px-10">
            <p className='text-[#032282] font-bold lg:text-[1.875rem] md:text-[1.3625rem] text-[1.2rem] md:leading-[3.5806rem] leading-8'>
              Find the Perfect Plan for Your Business
            </p>
            <p className='max-w-lg text-xs md:text-sm leading-[1.3638rem] font-medium text-[#242424] '>
              Choose the right module for your business and streamline HR, Spend, Stock, and Sales with powerful tools for efficiency and growth.

            </p>
            <p className='max-w-lg text-xs md:text-sm leading-[1.3638rem] font-medium text-[#242424] mt-2'>
              Select one or more modules and make payment to get started.

            </p>

            <div className=" mt-6 flex    border gap-4">
              <div className="">
                <Link href={`/subscription-dashboard?companyId=${company_id}&companyName=${companyName}`}>
                  <Button className="text-[#032282] h-8 bg-white px-8   rounded-lg w-full font-medium">
                    Dashboard
                  </Button>
                </Link>
              </div>

              {isAnySelected && (
                <div className="">
                  <Button
                    className=' h-[44px] w-full'
                    onClick={() => setShowSummaryModal(true)}// Handle global subscribe action}
                  >
                    Subscribe
                  </Button>
                </div>
              )}
            </div>




          </div>
        </div>
        <SubscriptionModules
          selectedModules={selectedModules}
          selectedPlans={selectedPlans}
          setIsAnySelected={setIsAnySelected}
          setSelectedModules={setSelectedModules}
          setSelectedPlans={setSelectedPlans}
          setShowSummaryModal={setShowSummaryModal}

        />

        <SubscriptionSummary
          selectedModules={selectedModules}
          setSelectedModules={setSelectedModules}
          setShowSuccessModal={setShowSuccessModal}
          setShowSummaryModal={setShowSummaryModal}
          showSummaryModal={showSummaryModal}

        />


        <GenerateInvoice
          company_id={company_id as string}
          companyName={companyName as string}
          description="An invoice has been sent to your registered email"
          heading='Invoice Sent Successfuly'
          setShowSuccessModal={setShowSuccessModal}
          showSuccessModal={showSuccessModal}

        // email={data?.results[0]?.em}


        />


      </div>

    </div>
  );
}

export default Page;
