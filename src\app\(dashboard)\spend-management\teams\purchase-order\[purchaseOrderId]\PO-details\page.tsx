"use client"
import { Button, DropdownMenu, DropdownMenuContent, DropdownMenuItem, Table, DropdownMenuTrigger, ErrorModal, LinkButton, TableBody, TableCell, TableHeader, TableRow, Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core';
import { useParams, useSearchParams } from 'next/navigation'
import React, { useRef, useState } from 'react'
import PurchasedOrderDetails from '../../../../misc/components/purchase-order/details/PurchasedOrderDetails';
import { useGetSinglePurchaseOrderList } from '../../../../misc/api/purchase-order/getSinglePo';
import { cn } from '@/utils/classNames';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import PoHistory from '../../../../misc/components/purchase-order/details/PoHistory';
import PoComment from '../../../../misc/components/purchase-order/details/PoComment';
import PoAttachment from '../../../../misc/components/purchase-order/details/PoAttachment';
import { useCancelledPo } from '@/app/(dashboard)/spend-management/misc/api/purchase-order/cancelledPo';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { useQueryClient } from '@tanstack/react-query';
import { useErrorModalState } from '@/hooks';
import toast from 'react-hot-toast';
import { ChevronDownIcon } from 'lucide-react';
import { handleDownloadImageOrPDF } from '@/app/(dashboard)/spend-management/misc/components/procurement/util/HandleDownloadPdf';
import ConfirmDeliveryModal from '@/app/(dashboard)/spend-management/misc/components/purchase-order/details/confirm-delivery/ConfirmDeliveryModal';
import moment from 'moment';
import { Spinner } from '@/icons/core';
import ConfirmDeliveryType from '@/app/(dashboard)/spend-management/misc/components/purchase-order/details/confirm-delivery/ConfirmDeliveryType';
import ScanWaybill from '@/app/(dashboard)/spend-management/misc/components/purchase-order/details/confirm-delivery/ScanWaybill';
import InvoicingDetailsModal from '@/app/(dashboard)/spend-management/suppliers/components/supply-management/invoicing/InvoiceDetails';
import ConfirmOcrDelivery from '@/app/(dashboard)/spend-management/misc/components/purchase-order/details/confirm-delivery/ComfirmOcrDelivery';
import { OcrItem } from '@/app/(dashboard)/spend-management/misc/components/purchase-order/details/confirm-delivery/types';
import SubscriptionErrorModal from '@/app/(dashboard)/spend-management/misc/components/subscription/ErrorSubscriptionModal';

const PurchaseOrderId = () => {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();

    const params = useParams();
    const search = useSearchParams()
    const companyId = search.get("companyId")
    const companyName = search.get("companyName")
    const { purchaseOrderId } = params;
    const { data: poDetatails, isFetching,
        isLoading, error } = useGetSinglePurchaseOrderList(purchaseOrderId as string)
    const [showConfirmDeliveryModal, setShowConfirmDeliveryModal] = useState(false)
    const [showConfirmOcrDeliveryModal, setShowConfirmOcrDeliveryModal] = useState(false)
    const [showConfirmDeliveryType, setShowConfirmDeliveryType] = useState(false)
    const [showInvoiceData, setShowInvoiceData] = useState(false)
    const [ocrItems, setOcrItems] = useState<OcrItem[]>()

    const [showOCRModal, setShowOCRModal] = useState(false)
    // const [first, setfirst] = useState(second)
    const componentRef = useRef<HTMLDivElement>(null);
    const tabArray = ["history", "comments", "attachment"]
    const [activeTab, setActiveTab] = useState(tabArray[0])
    const handleTabChange = (value: string) => {
        setActiveTab(value); // Update active tab    };
    }
    const queryClient = useQueryClient();

    const { mutate: handleCancelPo } = useCancelledPo()
    const handleCancelledPo = (id: string) => {
        handleCancelPo(id, {
            onSuccess: (data) => {
                queryClient.invalidateQueries({ queryKey: ["get-procurement-list", "get-single-purchase-order-list"] });
                toast.success(data?.message)


            },
            onError: (error) => {
                const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                openErrorModalWithMessage(errorMessage);
            }
        })
    }    // Download as Image



    // Generate and download as PDF

    const statusError = error as AxiosError


    return (
        <>

            {isLoading ? <div className='h-screen w-full flex justify-center items-center'><Spinner className='w-40 h-40' color='blue ' /></div> :
                <>
                    {(!isLoading && statusError?.response?.status !== 403) && (
                        <div className=' p-4 md:px-12 md:bg-white  w-full max-h-[50vh] md:max-h-max md:h-full mt-2'>


                            <div className="flex items-center justify-between flex-wrap">
                                <div className="flex items-center gap-x-2">
                                    <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.17758 14.1148C7.03508 14.1148 6.89258 14.0623 6.78008 13.9498L2.22758 9.39734C2.01008 9.17984 2.01008 8.81984 2.22758 8.60234L6.78008 4.04984C6.99758 3.83234 7.35758 3.83234 7.57508 4.04984C7.79258 4.26734 7.79258 4.62734 7.57508 4.84484L3.42008 8.99984L7.57508 13.1548C7.79258 13.3723 7.79258 13.7323 7.57508 13.9498C7.47008 14.0623 7.32008 14.1148 7.17758 14.1148Z" fill="#292D32" />
                                        <path d="M15.3754 9.5625H2.75293C2.44543 9.5625 2.19043 9.3075 2.19043 9C2.19043 8.6925 2.44543 8.4375 2.75293 8.4375H15.3754C15.6829 8.4375 15.9379 8.6925 15.9379 9C15.9379 9.3075 15.6829 9.5625 15.3754 9.5625Z" fill="#292D32" />
                                    </svg>

                                    <p className='text-xxs text-opacity-60'>Purchase order &gt; #{poDetatails?.data?.order_no}</p>

                                </div>
                                <div className="flex items-center gap-5">

                                    {poDetatails?.previous &&
                                        <LinkButton className="px-2 gap-3 flex justify-between items-center" href={`/spend-management/teams/purchase-order/${poDetatails?.previous}/PO-details?companyId=${companyId}8&companyName=${companyName}`} variant={'outlined'} replace> <svg fill="none" height="12" viewBox="0 0 6 12" width="6" xmlns="http://www.w3.org/2000/svg">
                                            <path clipRule="evenodd" d="M1.63008 3.87868C0.497563 5.0112 0.459813 6.82392 1.51683 8.00179L1.63008 8.12132L4.71966 11.0303C5.01256 11.3232 5.48743 11.3232 5.78032 11.0303C6.05069 10.76 6.07148 10.3345 5.84272 10.0403L5.78032 9.96967L2.69074 7.06066C2.13579 6.50571 2.10658 5.62409 2.60312 5.03475L2.69074 4.93934L5.78032 2.03033C6.07322 1.73744 6.07322 1.26256 5.78032 0.96967C5.50996 0.699307 5.08454 0.67851 4.79032 0.907278L4.71966 0.96967L1.63008 3.87868Z" fill="#032282" fillRule="evenodd" />
                                        </svg>
                                            Prev
                                        </LinkButton>}
                                    {poDetatails?.next && <LinkButton className="px-2 gap-3 flex justify-between items-center" href={`/spend-management/teams/purchase-order/${poDetatails?.next}/PO-details?companyId=${companyId}8&companyName=${companyName}`} variant={'outlined'} replace> Next <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                                        <path clipRule="evenodd" d="M10.3699 6.87868C11.5024 8.0112 11.5401 9.82392 10.4831 11.0018L10.3699 11.1213L7.28027 14.0303C6.98738 14.3232 6.51251 14.3232 6.21961 14.0303C5.94925 13.76 5.92845 13.3345 6.15722 13.0403L6.21961 12.9697L9.3092 10.0607C9.86415 9.50571 9.89336 8.62409 9.39682 8.03475L9.3092 7.93934L6.21961 5.03033C5.92672 4.73744 5.92672 4.26256 6.21961 3.96967C6.48998 3.69931 6.9154 3.67851 7.20962 3.90728L7.28028 3.96967L10.3699 6.87868Z" fill="#032282" fillRule="evenodd" />
                                    </svg>

                                    </LinkButton>}
                                </div>
                            </div>
                            {/* purchase_order_status": "", */}

                            <div className=" flex justify-between items-center gap-3 flex-wrap mt-3 ">
                                <div className="md:text-xl text-sm   flex items-center gap-[.875rem] font-semibold text-[#242424]"><h2>#{poDetatails?.data?.order_no}</h2>
                                    {/* <LinkButton className='text-[#032282] border-[0.3px] border-[#7E92D0] rounded bg-[#f2f5ff] px-2 py-[.3125rem]' href={`/spend-management/teams/purchase-order/${poDetatails?.data?.id}/PO-details/ocr?companyId=${companyId}&companyName=${companyName}`}>View Attachement OCR</LinkButton> */}
                                </div>


                                <div className="flex items-center gap-2 flex-wrap">
                                    <Button className='text-[#667085] border-[0.3px] border-[#7E92D0]'
                                        disabled={poDetatails?.data?.purchase_order_status !== "UNDER_REVIEW"}
                                        type='button'
                                        variant={"outlined"}
                                        onClick={() => handleCancelledPo(poDetatails?.data?.id as string)}>Cancel</Button>
                                    <Button className='text-[#667085] border-[0.3px] border-[#7E92D0]'
                                        disabled={!poDetatails?.data?.invoice}
                                        type='button'
                                        variant={"outlined"}
                                        onClick={() => setShowInvoiceData(true)}>View invoice</Button>
                                    <div className="relative ">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button className='text-[#667085] border-[0.3px] border-[#7E92D0]' variant={"outlined"}>
                                                    Export
                                                    <ChevronDownIcon height={15} width={15} />
                                                </Button>
                                            </DropdownMenuTrigger>

                                            <DropdownMenuContent className="bg-white border border-gray-300 rounded-md shadow-lg p-1 mt-2 w-40">
                                                <DropdownMenuItem
                                                    className="flex items-start px-3 py-2 cursor-pointer hover:bg-gray-100"
                                                    onSelect={() => handleDownloadImageOrPDF("png", ".div-to-print")}
                                                >
                                                    <span>Image</span>

                                                </DropdownMenuItem>
                                                <DropdownMenuItem
                                                    className="flex items-start px-3 py-2 cursor-pointer hover:bg-gray-100"
                                                    onSelect={() => handleDownloadImageOrPDF("pdf", ".div-to-print")}
                                                >
                                                    <span>PDF</span>

                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                    <Button className='' disabled={poDetatails?.data?.purchase_order_status !== "ACCEPTED" || poDetatails?.data?.delivery_status === "FULLY_DELIVERED"} onClick={() => setShowConfirmDeliveryType(true)}>Confirm delivery</Button>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 gap-2 py-5 bg-white mt-4 xl:grid-cols-[1.5fr_1fr] 2xl:grid-cols-[2.6fr_1fr]">
                                <div className=" md:max-h-[65vh] overflow-auto rounded-lg py-4">
                                    <PurchasedOrderDetails isFetching={isFetching} isLoading={isLoading}
                                        poDetatails={poDetatails} />
                                </div>
                                <div className="md:max-h-[65vh] overflow-auto " >

                                    <Tabs className=" border rounded-lg py-4 flex flex-col w-full gap-x-4 px-0 mt-2" defaultValue={activeTab} onValueChange={handleTabChange}>
                                        <div className="w-full bg-white flex justify-between flex-wrap lg:flex-nowrap items-center ">
                                            <div className="w-full rounded border-b-[0.7px] border-opacity-90   p-0  flex">
                                                {tabArray?.map((item, idx: number) => (
                                                    <TabsList
                                                        className=" bg-transparent  px-0 text-main-solid"
                                                        key={idx}
                                                    >
                                                        <TabsTrigger
                                                            className={cn(
                                                                activeTab === item
                                                                    ? ' '
                                                                    : ' px-0 bg-transparent ',
                                                                "data-[state=active]:shadow-none  data-[state=active]:border-b-[3px]   data-[state=active]:font-semibold border-[#032282]   data-[state=active]:bg-transparent px-2 "
                                                            )}
                                                            value={item}
                                                        >
                                                            <Button className="flex flex-col  bg-transparent pb-0  px-3  text-sm font-semibold">

                                                                <p className={cn(' text-xs font-normal')}
                                                                    style={{ color: activeTab === item ? "#032282" : "#0E0E2C66" }}>
                                                                    {convertKebabAndSnakeToTitleCase(item)}
                                                                </p>
                                                            </Button>
                                                        </TabsTrigger>
                                                    </TabsList>
                                                ))}
                                            </div>


                                        </div>
                                        <div className="md:max-h-[53vh] overflow-auto relative">
                                            <TabsContent className="bg-white w-full pt-2" value={"history"} ><PoHistory history={poDetatails?.data?.indent?.history} /></TabsContent>
                                            <TabsContent className="bg-white w-full pt-2" value={"comments"} ><PoComment supplierName={poDetatails?.data?.indent?.supplier?.name} /></TabsContent>
                                            <TabsContent className="bg-white w-full pt-2" value={"attachment"} ><PoAttachment id={poDetatails?.data?.invoice as string} /></TabsContent>
                                        </div>


                                    </Tabs>
                                </div>
                                {/* export as pdf or image file */}
                                <div className="hidden">


                                    <div className="bg-white w-full  flex flex-col  p-[1.375rem] rounded-[.75rem] grow div-to-print" ref={componentRef}>

                                        <div className=" ">

                                            <div className="flex justify-between pb-[.875rem] border-b border-[#F8F9FB] items-center">
                                                <div>
                                                    <h2 className='text-[1.375rem] font-semibold text-[#032282] font-clash'>Paybox360</h2>
                                                    <p className='text-[#373737] text-xxs w-4/5' style={{ wordBreak: "break-word" }}>23 Alara Street, Off Herbert Macauley Industrial Road, Sabo, Yaba, Lagos</p>
                                                    <p className='text-[#373737] text-xxs '>09090909090, 08080808080</p>
                                                </div>
                                                <div className="text-right">
                                                    <h2 className='font-semibold text-sm'>PO Details</h2>
                                                    <p className='text-[#373737] font-medium text-xxs '>Order No: {poDetatails?.data?.order_no}</p>
                                                    <p className='text-[#373737] font-medium text-xxs '>Date Issued:{moment(`${poDetatails?.data?.created_at}`)?.format("ll") ?? ""}</p>
                                                </div>
                                            </div>

                                            <div className="mt-[.875rem]">
                                                <p className='text-[#032282] text-sm font-semibold'>Deliver to:</p>
                                                <div className="mt-2 grid grid-cols-2 items-start">
                                                    <div>
                                                        <p className='font-medium text-xs text-[#262626]'>{poDetatails?.data?.indent?.team?.team_name}</p>
                                                        <p className='text-[#373737] text-xxs w-4/5'>{poDetatails?.data?.indent?.delivery_address}</p>
                                                        <p className='text-[#262626] text-xxs font-medium mt-[1.875rem] '>Date: {moment(`${poDetatails?.data?.indent?.expected_date_of_delivery}`)?.format("ll") ?? ""}</p>
                                                    </div>
                                                    <div className="space-y-1">
                                                        <p className='font-medium text-xs text-[#262626]'>Supplier:</p>
                                                        <p className='text-[#373737] text-xxs font-medium'>Name:{poDetatails?.data?.indent?.supplier?.name}</p>
                                                        <p className='text-[#373737] text-xxs font-medium'>{poDetatails?.data?.indent?.supplier?.address}</p>
                                                        <p className='text-[#373737] text-xxs font-medium '> {poDetatails?.data?.indent?.supplier?.phone_number} |  {poDetatails?.data?.indent?.supplier?.email}</p>

                                                    </div>
                                                </div>
                                            </div>

                                            {/* Table Content */}
                                            <div className="mt-5 flex-1 overflow-y-auto">
                                                <h2 className='text-sm text-[#032282] font-semibold'>Order Details</h2>
                                                <div className="mt-2">
                                                    <Table>
                                                        <TableHeader className="bg-[#E2E8F0]">
                                                            <TableRow>
                                                                <TableCell className="text-xs p-2 text-center border">S/N</TableCell>
                                                                <TableCell className="text-xs p-2 text-center border" colSpan={3}>Item Description</TableCell>
                                                                <TableCell className="text-xs p-2 text-center border">Qty</TableCell>
                                                                <TableCell className="text-xs p-2 text-center border">Unit Price</TableCell>
                                                            </TableRow>
                                                        </TableHeader>
                                                        <TableBody className="bg-white">
                                                            {poDetatails?.data?.indent?.products?.map((product, index) => (
                                                                <TableRow className="border text-xs" key={index}>
                                                                    <TableCell className="text-center text-xs p-3 border">{index + 1}</TableCell>
                                                                    <TableCell className="text-center text-xs p-3 border" colSpan={3}>
                                                                        {product?.name}
                                                                    </TableCell>
                                                                    <TableCell className="text-center p-3 border">{product?.quantity}</TableCell>
                                                                    <TableCell className="text-center text-xs p-3 border">{product?.current_stock_details?.price}</TableCell>
                                                                </TableRow>
                                                            ))}
                                                        </TableBody>
                                                    </Table>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Footer */}

                                        <footer className="mt-auto h-[20vh]">
                                            <div className="grid grid-cols-[1fr_1.5fr] gap-8 justify-between mt-12 pt-4">
                                                <div className="space-y-2"></div>
                                                <div className="space-y-2">
                                                    <p className='text-xs text-[#262626]'>Subtotal: .........................................................................</p>
                                                    {/* {/* <p className='text-xs text-[#262626]'>Tax: .....................................................................</p>
                                    <p className='text-xs text-[#262626]'>Others: ...................................................................................</p> */}
                                                    <p className='text-sm font-medium text-[#262626]'>Amount Due: .....................................................</p>
                                                </div>
                                            </div>
                                            <div className="py-4 mt-8 grid grid-cols-[1fr_1.5fr] gap- 8 item-center ">
                                                <p className='text-xs text-[#262626]'>Approved by: {`${poDetatails?.data?.indent?.approved_by?.first_name ?? ""} ${poDetatails?.data?.indent?.approved_by?.last_name ?? ""}`}</p>
                                                <p className='text-xs text-[#262626]'>Date : .........................................................................................................................</p>
                                            </div>
                                        </footer>
                                    </div>
                                </div>


                                {
                                    showConfirmDeliveryModal && <ConfirmDeliveryModal
                                        id={companyId as string}
                                        poDetatail={poDetatails}
                                        purchaseOrderId={purchaseOrderId as string}
                                        setShowConfirmDeliveryModal={setShowConfirmDeliveryModal}
                                        showConfirmDeliveryModal={showConfirmDeliveryModal}

                                    />
                                }
                                {
                                    showConfirmOcrDeliveryModal && <ConfirmOcrDelivery
                                        id={companyId as string}
                                        ocrItems={ocrItems}
                                        poDetatail={poDetatails}
                                        purchaseOrderId={purchaseOrderId as string}
                                        setShowConfirmDeliveryModal={setShowConfirmOcrDeliveryModal}
                                        showConfirmDeliveryModal={showConfirmOcrDeliveryModal}

                                    />
                                }
                                {
                                    showOCRModal && <ScanWaybill
                                        id={companyId as string}
                                        poDetatail={poDetatails}
                                        purchaseOrderId={purchaseOrderId as string}
                                        setOcrItems={setOcrItems}
                                        setOpenShowConfirmDeliveryModal={setShowConfirmOcrDeliveryModal}
                                        setShowConfirmDeliveryModal={setShowOCRModal}
                                        // setShowConfirmOcrDeliveryModal={setShowConfirmOcrDeliveryModal}
                                        showConfirmDeliveryModal={showOCRModal}
                                    />
                                }

                                {
                                    showConfirmDeliveryType && <ConfirmDeliveryType
                                        poDetatail={poDetatails}
                                        setOpenShowConfirmDeliveryModal={setShowConfirmDeliveryModal}
                                        setShowConfirmDeliveryModal={setShowConfirmDeliveryType}
                                        setShowOCRModal={setShowOCRModal}
                                        showConfirmDeliveryModal={showConfirmDeliveryType}
                                    />
                                }

                                {
                                    showInvoiceData &&
                                    <InvoicingDetailsModal
                                        id={poDetatails?.data?.invoice as string}
                                        setShowInvoiceDetailsModal={setShowInvoiceData}
                                        showInvoiceDetailsModal={showInvoiceData}
                                    />
                                }

                            </div>
                            <ErrorModal
                                isErrorModalOpen={isErrorModalOpen}
                                setErrorModalState={setErrorModalState}
                                subheading={
                                    errorModalMessage || 'Please check your inputs and try again.'
                                }
                            >
                                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                                    <Button
                                        className="grow bg-red-950 text-base"
                                        size="lg"
                                        onClick={closeErrorModal}
                                    >
                                        Okay
                                    </Button>
                                </div>
                            </ErrorModal>

                        </div>)}
                </>




            }
            {
                (statusError?.response?.status === 403 && !isLoading) && <SubscriptionErrorModal
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    message={String(statusError?.response?.data?.error as unknown as string) ?? ""}
                />
            }
        </>
    )
}

export default PurchaseOrderId