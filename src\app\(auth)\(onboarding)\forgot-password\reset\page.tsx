import { OnboardingPageWrapper } from "../../misc/components";
import ResetPasswordForm from "../misc/components/ResetPasswordForm";


export default function ForgotPassword({
    searchParams,
}: {
    searchParams: { email: string; phone: string; otp: string };
}) {
    const { email, phone, otp } = searchParams;

    return (
        <OnboardingPageWrapper
            heading="Reset Password?"
            subHeading="Enter your new passcode and confirm new passcode. We will update your account with the new passcode once verified."
        >
            <ResetPasswordForm email={email} otp={otp} phone={phone} />
        </OnboardingPageWrapper>
    );
}