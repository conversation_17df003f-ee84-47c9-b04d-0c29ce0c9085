"use client"

import { useState, useEffect } from 'react';
import { Button } from '@/components/core/Button';

interface ResendOTPProps {
    minutes: number;
    seconds: number;
    handleResendOTP: (event: React.MouseEvent<HTMLButtonElement>) => void;
    isRequestResetPasswordLoading: boolean
}

const ResendOTP = ({ minutes, seconds, handleResendOTP, isRequestResetPasswordLoading }: ResendOTPProps) => {
    const [timeLeft, setTimeLeft] = useState(minutes * 60 + seconds);

    useEffect(() => {
        const intervalId = setInterval(() => {
            setTimeLeft(prevTime => (prevTime > 0 ? prevTime - 1 : 0));
        }, 1000);

        return () => clearInterval(intervalId);
    }, []);

    const formatTime = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}mins ${secs.toString().padStart(1, '0')}secs`;
    };


    return (
        <div className='flex flex-col items-end'>
            <div className='h-6 calc(w-fit)'>
                {timeLeft > 0 && <div className='w-fit relative mb-9 text-xs leading-[normal] text-white lg:text-sm'>{formatTime(timeLeft)} till resend</div>}
            </div>
            <Button
                className="ml-auto mt-4 flex w-fit gap-2 text-xxs font-normal text-white"
                disabled={isRequestResetPasswordLoading || (timeLeft > 0)}
                size="unstyled"
                type="button"
                variant="unstyled"
                onClick={(e) => {
                    handleResendOTP(e)
                    setTimeLeft(minutes * 60 + seconds)
                }}
            >
                <svg
                    fill="none"
                    height={24}
                    viewBox="0 0 24 24"
                    width={24}
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <circle cx={12} cy={12} fill="#F2F6FF" r={12} />
                    <path
                        d="M5.75 12.606V8.87a3.12 3.12 0 0 1 3.125-3.12h6.25a3.12 3.12 0 0 1 3.125 3.119v4.362a3.12 3.12 0 0 1-3.125 3.113h-.938a.633.633 0 0 0-.5.25l-.937 1.243c-.412.55-1.088.55-1.5 0l-.938-1.243a.695.695 0 0 0-.5-.25h-.937A3.12 3.12 0 0 1 5.75 13.23v-.625Z"
                        fill="#073D9F"
                        opacity={0.6}
                    />
                    <path
                        d="M12 12a.623.623 0 0 1-.625-.625c0-.344.281-.625.625-.625s.625.281.625.625A.623.623 0 0 1 12 12Zm2.5 0a.623.623 0 0 1-.625-.625c0-.344.281-.625.625-.625s.625.281.625.625A.623.623 0 0 1 14.5 12Zm-5 0a.623.623 0 0 1-.625-.625c0-.344.281-.625.625-.625s.625.281.625.625A.623.623 0 0 1 9.5 12Z"
                        fill="#fff"
                    />
                </svg>

                <span>Resend OTP</span>
            </Button>
        </div>
    );
};

export default ResendOTP;
