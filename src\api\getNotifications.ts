import { useQuery } from '@tanstack/react-query';
import { z } from 'zod';

import { managementAxios } from '@/lib/axios';

const NotificationsResponseSchema = z.object({
  status: z.string(),
  status_code: z.number(),
  data: z.object({
    details: z.string(),
    notifications: z.array(z.object({
      id: z.string(),
      created_at: z.string(),
      updated_at: z.string(),
      title: z.string(),
      message: z.string(),
      notification_type: z.string(),
      is_read: z.boolean(),
      company: z.string(),
      branch: z.string().nullable(),
    })),
    count: z.number(),
    total_notifications: z.number(),
    total_unread: z.number(),
  }),
  errors: z.null(),
});

type NotificationsResponse = z.infer<
  typeof NotificationsResponseSchema
>;

type FetchOptions = {
  companyId: string;
};

export const getNotifications = async (
  fetchOptions: FetchOptions
): Promise<NotificationsResponse> => {
  const { companyId } = fetchOptions;

  const response = await managementAxios.get(`/core/notifications/?company=${companyId}`);
  return NotificationsResponseSchema.parse(response.data);
};

export const useGetNotifications = (
  fetchOptions: FetchOptions,
) => {
  return useQuery({
    queryKey: ['notifications-list', fetchOptions.companyId],
    queryFn: () => getNotifications(fetchOptions),
    enabled: !!fetchOptions.companyId,
  });
};
