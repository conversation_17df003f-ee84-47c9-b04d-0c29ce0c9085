'use client'
import { redirect } from 'next/navigation';
import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';

import { ViewStocksDialog } from '../../misc/components/modals';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';

const ViewStocks = () => {

  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies, count: companiesCount } = companiesResponse as Companies;

  if (companiesCount < 1) {
    redirect('/spend-management/companies/create-company');
  }

  return (
    <>
      <ViewStocksDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies}
      // initialUsersResponse={usersResponse}
      />
    </>
  );
};

export default ViewStocks;
