'use client'

import * as React from 'react';

import { Button, LinkButton, LoaderModal } from '@/components/core';
import { GenericLoaderFallback } from '@/icons/core';

import { cn } from '@/utils/classNames';

import { CompaniesWithoutTabs, CompanySummaryCard } from '../misc/components';
import { Companies } from '../misc/types';

import { useSpendManagementCompanies } from '../misc/api/getSpendManagementCompanies';
import { CreateSpendTeamModal } from '../misc/components/team/CreateSpendTeamModal';

// const CALLBACK_URL = '/spend-management/companies';

export default function Company() {
  // const {
  //   user: { access },
  // } = await getServerSessionOrRedirect({
  //   authOptions,
  //   callbackUrl: CALLBACK_URL,
  // });
  // const pageSize = 50;


  // const companiesResponse = await spendManagementFetchClient<Companies>(
  //   `req/get-companies/?page_size=${pageSize}`,
  //   {
  //     headers: { Authorization: `Bearer ${access}` },
  //   }
  // );

  // if (companiesResponse === 'unauthorized') {
  //   redirect(`/login?callbackUrl=${CALLBACK_URL}`);
  // }


  const { data: companiesResponse, isLoading: isLoadingCompanyListResponse } = useSpendManagementCompanies()
  const [showCreateSpendTeam, setShowCreateSpendTeam] = React.useState(false)

  const isLoading =
    isLoadingCompanyListResponse

  return (
    <>
      <LoaderModal isOpen={isLoading} />
      {!isLoading && (
        <> <div className="relative z-2 mb-2 overflow-hidden bg-[#EBEFFB] px-6 py-4 md:rounded-10 md:bg-requisition-companies-bg-gradient md:px-7 lg:px-11">
          <div className="lg:flex lg:gap-4">
            <div className="flex flex-col rounded-xl bg-white lg:grow">
              <div className="flex max-w-[22rem] gap-2 rounded-xl bg-white px-3.5 pb-0 pt-4 min-[380px]:px-5 sm:gap-4 md:px-6">
                <LinkButton
                  className="grow px-1.5 py-2.5 sm:text-sm md:px-6 whitespace-nowrap"
                  href="/spend-management/companies/create-company"
                  size="lg"
                >
                  Create company
                </LinkButton>

                <Button
                  className="grow px-1.5 py-2.5 sm:text-sm md:px-6 whitespace-nowrap"
                  // href="/spend-management/teams/select-company"
                  size="lg"
                  variant="outlined"
                  onClick={() => setShowCreateSpendTeam(true)}
                >
                  Create team
                </Button>

                <Button
                  className="hidden"
                  size="unstyled"
                  variant="unstyled"
                  disabled
                >
                  <span className="sr-only">Roles and permissions</span>
                  <svg
                    fill="none"
                    height={20}
                    viewBox="0 0 20 20"
                    width={20}
                    xmlns="http://www.w3.org/2000/svg"
                    aria-hidden
                  >
                    <path
                      d="M10 13.125a3.126 3.126 0 0 1 0-6.25 3.126 3.126 0 0 1 0 6.25Zm0-5A1.878 1.878 0 0 0 8.125 10c0 1.033.842 1.875 1.875 1.875A1.878 1.878 0 0 0 11.875 10 1.878 1.878 0 0 0 10 8.125Z"
                      fill="#032282"
                    />
                    <path
                      d="M12.675 18.491c-.175 0-.35-.025-.525-.066a2.047 2.047 0 0 1-1.225-.925l-.1-.167c-.492-.85-1.167-.85-1.659 0l-.091.158a2 2 0 0 1-1.225.934 1.95 1.95 0 0 1-1.525-.209l-1.434-.825a2.208 2.208 0 0 1-.816-3.016c.241-.425.308-.809.167-1.05-.142-.242-.5-.384-.992-.384a2.212 2.212 0 0 1-2.208-2.208V9.266c0-1.216.991-2.208 2.208-2.208.491 0 .85-.142.992-.383.141-.242.083-.625-.167-1.05a2.218 2.218 0 0 1-.217-1.675 2.15 2.15 0 0 1 1.033-1.342l1.442-.825c.942-.558 2.184-.233 2.75.725l.1.167c.492.85 1.167.85 1.659 0l.091-.159c.567-.966 1.809-1.291 2.758-.725l1.434.825a2.208 2.208 0 0 1 .816 3.017c-.241.425-.308.808-.166 1.05.141.242.5.383.992.383 1.216 0 2.208.992 2.208 2.209v1.466a2.212 2.212 0 0 1-2.209 2.209c-.491 0-.85.141-.991.383-.142.242-.084.625.166 1.05a2.18 2.18 0 0 1 .217 1.675 2.15 2.15 0 0 1-1.033 1.342l-1.442.825a2.074 2.074 0 0 1-1.008.266ZM10 15.408c.742 0 1.433.467 1.908 1.292l.092.158c.1.175.267.3.466.35.2.05.4.025.567-.075l1.442-.833a.963.963 0 0 0 .358-1.309c-.475-.816-.533-1.658-.167-2.3.367-.641 1.125-1.008 2.075-1.008a.953.953 0 0 0 .959-.958V9.258a.958.958 0 0 0-.959-.958c-.95 0-1.708-.367-2.075-1.009-.366-.641-.308-1.483.167-2.3a.952.952 0 0 0 .092-.725.975.975 0 0 0-.442-.583l-1.442-.825a.767.767 0 0 0-1.05.275l-.091.158c-.475.825-1.167 1.292-1.909 1.292-.741 0-1.433-.467-1.908-1.292l-.091-.166a.765.765 0 0 0-1.034-.267l-1.442.833A.964.964 0 0 0 5.159 5c.475.816.533 1.658.167 2.3-.368.64-1.126 1.008-2.076 1.008a.953.953 0 0 0-.958.958v1.467c0 .525.425.958.958.958.95 0 1.708.367 2.075 1.009.366.641.308 1.483-.167 2.3a.952.952 0 0 0-.092.725c.067.25.226.45.442.583l1.442.825a.737.737 0 0 0 .575.083.79.79 0 0 0 .475-.358l.091-.158c.476-.817 1.167-1.292 1.909-1.292Z"
                      fill="#032282"
                    />
                  </svg>
                </Button>
              </div>

              <CompanySummaryCard companiesResponse={companiesResponse as Companies} />
            </div>

            <div
              className={cn(
                "hidden rounded-xl bg-main-solid bg-[url('/images/requisition/card-backgrounds/budgeting-card-cubes.png')] bg-contain bg-right bg-no-repeat px-6 py-5 text-white lg:inline-flex lg:w-1/3 lg:max-w-[19.875rem] lg:shrink lg:flex-col xl:w-[26%]"
              )}
            >
              <h2 className="mb-1 text-lg font-semibold">Budgeting</h2>
              <p className="mb-2 text-xs font-medium">
                Have all your business expenses within a set budget and have them
                tracked with ease.
              </p>

              <div className="mt-auto flex gap-3">
                <LinkButton
                  className="text bg-white/10 px-3 py-2.5 text-white"
                  color="white"
                  href="/spend-management/budgeting/budget-options"
                >
                  Create budget
                </LinkButton>

                <LinkButton
                  className="text bg-white/10 px-3 py-2.5 text-white"
                  color="white"
                  href="/spend-management/budgeting/all-budgets"
                >
                  View budgets
                </LinkButton>
              </div>
            </div>
          </div>
        </div>

          <div className="md:px-7 lg:px-11">
            <React.Suspense
              fallback={
                <GenericLoaderFallback className="mx-6 rounded-10 md:mx-0" />
              }
            >
              <CompaniesWithoutTabs
                companiesResponse={companiesResponse as Companies}
              />
            </React.Suspense>
          </div>
        </>
      )}


      {
        showCreateSpendTeam && (
          <CreateSpendTeamModal
            companyId=''
            companyName=''
            setShowCreateSpendTeam={setShowCreateSpendTeam}
            showCreateSpendTeam={showCreateSpendTeam}
          />
        )
      }
    </>

  );
}
