'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

// import {
//   LinkButton,
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from '@/components/core';

// import { useGetAssignedBranchesByCompany } from '../misc/api';
import SalesDashboardCards from '../misc/components/SalesDashboardCardsNew';
// import SalesDashboardCharts from '../misc/components/SalesDashboardCharts';
import SalesHistoryTable from '../misc/components/tables/BranchSalesHistoryTable';
import { ViewBranchesDialog } from '../../stock/misc/components';
import { Button, LinkButton, LoaderModal } from '@/components/core';
import { useCompaniesList } from '../../instant-web/misc/api';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';
import toast from 'react-hot-toast';
import { SalesHQIcon } from '@/components/icons';
import { CopyIcon } from 'lucide-react';
import { AuthorizeSellWithoutInventoryDialog } from '../misc/components/modals/AuthorizeSellWithoutInventoryDialog';
import { useGetBranchDetails } from '../misc/api';
import { useBooleanStateControl } from '@/hooks';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core';
import { CompanyOverviewAnalytics } from '../company-details-new/misc/components/overview';
import WalletHistoryTable from '../misc/components/tables/BranchWalletHistoryTable';


const Page = () => {

  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const branchId = searchParams.get('branch') || '';
  const companyId = searchParams.get('company') || '';
  // const companyName = searchParams.get('companyName') as string;
  // const branchName = searchParams.get('branchName') as string;

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  useEffect(() => {
    if (!companyId && salesUserDetails?.data) {
      router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])

  function copyText(text: string) {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('Text copied to clipboard!');
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  }
  const { data: branchDetails } = useGetBranchDetails(companyId, branchId);

  const {
    state: isAuthorizeSellWithoutInventoryModalOpen,
    setState: setAuthorizeSellWithoutInventoryModalState,
    setTrue: openAuthorizeSellWithoutInventoryModal,
  } = useBooleanStateControl();

  return (
    <>
      <LoaderModal isOpen={isDefaultLoading || !companyId} />

      {branchId ?
        <section className="md:px-7 md:py-3 lg:px-11">
          <div className="bg-[#99C7FF4A] rounded-[20px] py-4 px-6 relative overflow-hidden">
            <div className="flex flex-col space-y-4">
              <h2 className='text-primary font-semibold text-xl'>Sale on Web POS</h2>
              <div className='text-sm w-3/4'></div>
              <div className='flex items-center space-x-2'>
                <Button className='px-1 bg-[#EAF4FF] w-[150px] flex text-primary' onClick={() => { copyText("https://www.sales.paybox360.com/login") }}>
                  <p className='w-full pl-1 flex-1 max-w-full truncate'>https://www.sales.paybox360.com/login</p>
                  <CopyIcon size={16} />
                </Button>

                <LinkButton className='' href={`/sales/sell?company=${companyId}&branch=${branchId}`} >Sell with admin</LinkButton>
              </div>
            </div>
            <div className="absolute right-0 bottom-0">
              <SalesHQIcon />
            </div>
          </div>

          <SalesDashboardCards />
          <Tabs className="w-full" defaultValue="analytics">
            <div className="my-4 flex items-center justify-between gap-[21.73px] bg-white py-2" >
              <TabsList className="flex w-full flex-col justify-start gap-[22px] rounded-[.625rem] bg-white pb-0 lg:flex-row">
                <div className="w-full rounded-[13px] bg-white px-2 py-1">
                  <TabsTrigger
                    className={`
                    bg-white px-[37px]
                    py-[14px] text-[#032282] data-[state=active]:border-b-4 data-[state=active]:border-[#032282] data-[state=active]:text-[#032282] data-[state=active]:shadow-none`}
                    value="analytics"
                  >
                    Analytics
                  </TabsTrigger>
                  <TabsTrigger
                    className={`
                    bg-white px-[37px]
                    py-[14px] text-[#032282] data-[state=active]:border-b-4 data-[state=active]:border-[#032282] data-[state=active]:text-[#032282] data-[state=active]:shadow-none`}
                    value="all_transaction"
                  >
                    All Transaction
                  </TabsTrigger>

                  <TabsTrigger
                    className={`
                    bg-white px-[37px]
                    py-[14px] text-[#032282] data-[state=active]:border-b-4 data-[state=active]:border-[#032282] data-[state=active]:text-[#032282] data-[state=active]:shadow-none`}
                    value="single_transaction"
                  >
                    Single Transaction
                  </TabsTrigger>
                  <TabsTrigger
                    className={`
                    bg-white px-[37px]
                    py-[14px] text-[#032282] data-[state=active]:border-b-4 data-[state=active]:border-[#032282] data-[state=active]:text-[#032282] data-[state=active]:shadow-none`}
                    value="wallet_history"
                  >
                    Wallet History
                  </TabsTrigger>
                </div>
              </TabsList>

              <Button
                className='py-3 px-1 font-medium text-sm space-x-2 text-[#032282] flex items-center flex-1 shrink-0 min-w-[200px]'
                variant={'light'}
                onClick={openAuthorizeSellWithoutInventoryModal}
              >
                <p>Sales settings</p>
                <svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 13.125C8.275 13.125 6.875 11.725 6.875 10C6.875 8.275 8.275 6.875 10 6.875C11.725 6.875 13.125 8.275 13.125 10C13.125 11.725 11.725 13.125 10 13.125ZM10 8.125C8.96667 8.125 8.125 8.96667 8.125 10C8.125 11.0333 8.96667 11.875 10 11.875C11.0333 11.875 11.875 11.0333 11.875 10C11.875 8.96667 11.0333 8.125 10 8.125Z" fill="#032282" />
                  <path d="M12.6748 18.4917C12.4998 18.4917 12.3248 18.4667 12.1498 18.425C11.6332 18.2833 11.1998 17.9583 10.9248 17.5L10.8248 17.3333C10.3332 16.4833 9.65817 16.4833 9.1665 17.3333L9.07484 17.4917C8.79984 17.9583 8.3665 18.2917 7.84984 18.425C7.32484 18.5667 6.78317 18.4917 6.32484 18.2167L4.8915 17.3917C4.38317 17.1 4.0165 16.625 3.85817 16.05C3.70817 15.475 3.78317 14.8833 4.07484 14.375C4.3165 13.95 4.38317 13.5667 4.2415 13.325C4.09984 13.0833 3.7415 12.9417 3.24984 12.9417C2.03317 12.9417 1.0415 11.95 1.0415 10.7333V9.26666C1.0415 8.05 2.03317 7.05833 3.24984 7.05833C3.7415 7.05833 4.09984 6.91667 4.2415 6.675C4.38317 6.43333 4.32484 6.05 4.07484 5.625C3.78317 5.11667 3.70817 4.51667 3.85817 3.95C4.00817 3.375 4.37484 2.9 4.8915 2.60833L6.33317 1.78333C7.27484 1.225 8.5165 1.55 9.08317 2.50833L9.18317 2.675C9.67484 3.525 10.3498 3.525 10.8415 2.675L10.9332 2.51667C11.4998 1.55 12.7415 1.225 13.6915 1.79167L15.1248 2.61667C15.6332 2.90833 15.9998 3.38333 16.1582 3.95833C16.3082 4.53333 16.2332 5.125 15.9415 5.63333C15.6998 6.05833 15.6332 6.44167 15.7748 6.68333C15.9165 6.925 16.2748 7.06667 16.7665 7.06667C17.9832 7.06667 18.9748 8.05833 18.9748 9.275V10.7417C18.9748 11.9583 17.9832 12.95 16.7665 12.95C16.2748 12.95 15.9165 13.0917 15.7748 13.3333C15.6332 13.575 15.6915 13.9583 15.9415 14.3833C16.2332 14.8917 16.3165 15.4917 16.1582 16.0583C16.0082 16.6333 15.6415 17.1083 15.1248 17.4L13.6832 18.225C13.3665 18.4 13.0248 18.4917 12.6748 18.4917ZM9.99984 15.4083C10.7415 15.4083 11.4332 15.875 11.9082 16.7L11.9998 16.8583C12.0998 17.0333 12.2665 17.1583 12.4665 17.2083C12.6665 17.2583 12.8665 17.2333 13.0332 17.1333L14.4748 16.3C14.6915 16.175 14.8582 15.9667 14.9248 15.7167C14.9915 15.4667 14.9582 15.2083 14.8332 14.9917C14.3582 14.175 14.2998 13.3333 14.6665 12.6917C15.0332 12.05 15.7915 11.6833 16.7415 11.6833C17.2748 11.6833 17.6998 11.2583 17.6998 10.725V9.25833C17.6998 8.73333 17.2748 8.3 16.7415 8.3C15.7915 8.3 15.0332 7.93333 14.6665 7.29167C14.2998 6.65 14.3582 5.80833 14.8332 4.99167C14.9582 4.775 14.9915 4.51667 14.9248 4.26667C14.8582 4.01667 14.6998 3.81667 14.4832 3.68333L13.0415 2.85833C12.6832 2.64167 12.2082 2.76667 11.9915 3.13333L11.8998 3.29167C11.4248 4.11667 10.7332 4.58333 9.9915 4.58333C9.24984 4.58333 8.55817 4.11667 8.08317 3.29167L7.9915 3.125C7.78317 2.775 7.3165 2.65 6.95817 2.85833L5.5165 3.69167C5.29984 3.81667 5.13317 4.025 5.0665 4.275C4.99984 4.525 5.03317 4.78333 5.15817 5C5.63317 5.81667 5.6915 6.65833 5.32484 7.3C4.95817 7.94167 4.19984 8.30833 3.24984 8.30833C2.7165 8.30833 2.2915 8.73333 2.2915 9.26666V10.7333C2.2915 11.2583 2.7165 11.6917 3.24984 11.6917C4.19984 11.6917 4.95817 12.0583 5.32484 12.7C5.6915 13.3417 5.63317 14.1833 5.15817 15C5.03317 15.2167 4.99984 15.475 5.0665 15.725C5.13317 15.975 5.2915 16.175 5.50817 16.3083L6.94984 17.1333C7.12484 17.2417 7.33317 17.2667 7.52484 17.2167C7.72484 17.1667 7.8915 17.0333 7.99984 16.8583L8.0915 16.7C8.5665 15.8833 9.25817 15.4083 9.99984 15.4083Z" fill="#032282" />
                </svg>
              </Button>
            </div>


            <TabsContent className="mt-4 bg-[#F8FAFF]" value="analytics">
              <div className='w-full'>
                <CompanyOverviewAnalytics company={companyId} />
              </div>
            </TabsContent>
            <TabsContent className="mt-4 bg-[#F8FAFF]" value="all_transaction">
              <SalesHistoryTable />
            </TabsContent>
            <TabsContent className="mt-4 bg-[#F8FAFF]" value="single_transaction">
              <SalesHistoryTable />
            </TabsContent>
            <TabsContent className="mt-4 bg-[#F8FAFF]" value="wallet_history">
              <WalletHistoryTable />
            </TabsContent>

          </Tabs>




          {isAuthorizeSellWithoutInventoryModalOpen && <AuthorizeSellWithoutInventoryDialog
            allow_store_credit={branchDetails?.data.branch.allow_store_credit as boolean}
            branch={branchId}
            company={companyId}
            isAuthorizeSellWithoutInventoryModalOpen={isAuthorizeSellWithoutInventoryModalOpen}
            passChargesToCustomer={branchDetails?.data.branch.transfer_charges_to_customer as boolean}
            sellWithInventory={branchDetails?.data.branch.sell_without_inventory as boolean}
            setAuthorizeSellWithoutInventoryModalState={setAuthorizeSellWithoutInventoryModalState}
          />}
        </section>
        :
        <>
          {!isCompanyLoading && <ViewBranchesDialog company={companyId}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            companyList={companies?.results} link={'/sales/dashboard'} />}
        </>
      }
    </>
  );
};

export default Page;
