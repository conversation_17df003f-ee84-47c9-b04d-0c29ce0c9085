'use client'

import React from 'react';

import { CreateBranchModal } from '../../misc/components/modals';
import { LoaderModal } from '@/components/core';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';

export default function CreateBranch() {

  const { data: companies, isLoading } = useCompaniesList();

  return (
    <>
      <LoaderModal isOpen={isLoading} />

      {!isLoading && (
        <CreateBranchModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          companies={companies?.results} />
      )}
    </>
  );
}
