'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-label';
import { AxiosError } from 'axios';
import { useSearchParams } from 'next/navigation';
import * as React from 'react';
import { Controller, useFieldArray, useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';
import Image from 'next/image';
import { ResetIcon } from '@radix-ui/react-icons';

import {
  Button,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  ErrorModal,
  FormError,
  Input,
  Textarea,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { SmallSpinner } from '@/icons/core';
import { cn } from '@/utils/classNames';
import { formatAxiosErrorMessage } from '@/utils/errors';

import { useEditStock } from '../../api';
import { SingleCompanyProductsOverview } from '../../types';
import { SuccessModal } from './SuccessModal';
import { StockCategoryCombobox } from '../StockCategoryCombobox';
import { ChangeEvent, useState } from 'react';
import { deleteFromCloudinary, uploadToCloudinary } from '@/app/(dashboard)/instant-web/misc/utils/cloudinaryFunctions';
import { StockSubCategoryCombobox } from '../StockSubCategoryCombobox';

const StockListsSchema = z.object({
  company: z.string(),
  branch: z.string(),
  stocks: z.array(
    z.object({
      item: z.string(),
      category: z.string(),
      product_image_1: z.union([z.instanceof(File), z.string()]).optional().nullable(),
      subcategory: z.string().optional(),
      subcategory_name: z.string().optional(),
      name: z.string().min(1, 'Required'),
      sku: z.string().nullable(),
      spec_model_variant: z.string().nullable(),
      stock_price: z.coerce.number().min(1, { message: 'Required' }),
      selling_price: z.coerce.number().min(1, { message: 'Required' }),
      product_id: z.string().optional(),
      product_description: z.string().optional(),
    })
  ),
});

export type StockListsFormValues = z.infer<typeof StockListsSchema>;

interface RestockDialogProps {
  rowValues?: SingleCompanyProductsOverview[];
  isEditProductModalOpen: boolean;
  setEditProductModalState: React.Dispatch<React.SetStateAction<boolean>>;
}

export function EditProductDialog({
  rowValues = [],
  isEditProductModalOpen,
  setEditProductModalState,
}: RestockDialogProps) {
  const searchParams = useSearchParams();
  const companyId = searchParams.get('company') as string;
  const branchId = searchParams.get('branch') as string;

  const [softDeletedImage, setSoftDeletedImage] = useState<string | File>('')

  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
    setFalse: closeSuccessModal,
  } = useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const reformedPayload = rowValues?.map(stock => {
    return {
      name: stock.product,
      subcategory: stock.subcategory || '',
      subcategory_name: stock.subcategory_name,
      spec_model_variant: stock.variants,
      sku: stock.sku || null,
      selling_price: stock.selling_price,
      stock_price: stock.stock_price,
      category: stock.category_id || '',
      item: stock.id,
      // supplier: stock.supplier,
      product_id: stock.unique_id,
      product_description: stock.description,
      product_image_1: stock.product_image_1,
    };
  });

  const {
    control,
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = useForm<StockListsFormValues>({
    resolver: zodResolver(StockListsSchema),
    defaultValues: {
      company: companyId,
      branch: branchId,
    },
  });

  const { fields, update: updateField } = useFieldArray({
    name: 'stocks',
    control,
  });

  const categoryWatch = useWatch({
    control,
    name: 'stocks.0.category'
  });

  React.useEffect(() => {
    setValue('stocks', reformedPayload || []);
    setSoftDeletedImage('')
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reformedPayload[0]?.item]);

  const isRestockDialogModalHidden = isErrorModalOpen;

  const handleImageChange = async function async(e: ChangeEvent<HTMLInputElement>) {
    e.preventDefault();
    const uploadedFile = e.target.files?.[0];
    if (e.target.files && uploadedFile) {
      updateField(0, {
        ...fields[0],
        product_image_1: uploadedFile,
      })
    }
    // Reset the file input value to allow re-uploading the same file
    if (e.target.files?.[0]) e.target.value = '';
  };

  const [lastKeyTime, setLastKeyTime] = React.useState<number>(0);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - lastKeyTime;

    // Update timing data
    setLastKeyTime(currentTime);

    // Barcode scanners typically have very consistent and fast intervals (< 50ms)
    // Manual typing is usually much slower
    const isBarcodeScan = timeDiff < 150;

    // Prevent auto form submission from barcode scanner
    if (e.key === 'Enter' && isBarcodeScan) {
      e.preventDefault();
    }
  };

  const { mutate: editStock, isLoading: isUpdateStocksLoading } =
    useEditStock();

  const onUpdateStockSubmit = async (submittedData: StockListsFormValues) => {
    const cloudinaryIds: string[] = []
    async function getUploadedData() {
      const updatedReformedData = await Promise.all(
        submittedData.stocks.map(async (stock) => {
          let imageUrl = ''
          if (stock.product_image_1) {
            try {
              // Upload image to Cloudinary
              const stockImageObject = await uploadToCloudinary(stock.product_image_1 as File);
              cloudinaryIds.push(stockImageObject.id)
              imageUrl = stockImageObject.secure_url
            } catch (error) {
              openErrorModalWithMessage('Failed to upload product image. Please try again.');
              throw error; // Re-throw to prevent form submission
            }
          }
          return {
            ...stock,
            product_image_1: imageUrl
          };
        })
      );

      return {
        company: submittedData.company,
        branch: submittedData.branch,
        stocks: updatedReformedData
      };
    }
    const updatedData = await getUploadedData()

    editStock(updatedData, {
      onSuccess: () => {
        openSuccessModal();
      },
      onError: async (error: unknown) => {
        await Promise.all(
          cloudinaryIds.map(async (id) => {
            // Clean up images in Cloudinary
            await deleteFromCloudinary(id);
          })
        );
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        openErrorModalWithMessage(errorMessage);
      },
    });
  };

  return (
    <>
      <Dialog open={isEditProductModalOpen} onOpenChange={setEditProductModalState}>
        <DialogContent
          className={cn(
            'bg-modal-bg-gradient',
            isRestockDialogModalHidden && 'hidden'
          )}
          overlayClassName={cn(isRestockDialogModalHidden && 'hidden')}
        >
          <DialogHeader>
            <DialogTitle>Edit product</DialogTitle>
            <DialogClose>Close</DialogClose>
          </DialogHeader>

          <DialogBody>
            <DialogDescription className="mb-4 text-[#37474F]">
              <div className='text-sm'>Product image</div>
              <div className='text-xs'>Recommended dimension: 930px x 1163px, Max file size: 5mb</div>
            </DialogDescription>{' '}
            <form
              className="space-y-10"
              onKeyDown={handleKeyDown}
              onSubmit={handleSubmit(onUpdateStockSubmit)}
            >
              {fields.map((stock, index) => {
                return (
                  <div className="rounded-md space-y-4" key={index + stock.id}>
                    {(stock.product_image_1) ?
                      <div className='relative w-fit'>
                        <button
                          className='absolute top-2 right-2 bg-white rounded-[50%]'
                          onClick={() => {
                            setSoftDeletedImage(stock.product_image_1 as string | File)
                            updateField(0, {
                              ...fields[0],
                              product_image_1: '',
                            })
                          }}
                        >
                          <svg fill="none" height="24" viewBox="0 0 16 16" width="24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11.6003 5.89609C11.5923 5.89609 11.5803 5.89609 11.5683 5.89609C9.45231 5.68409 7.34031 5.60409 5.24831 5.81609L4.43231 5.89609C4.26431 5.91209 4.11631 5.79209 4.10031 5.62409C4.08431 5.45609 4.20431 5.31209 4.36831 5.29609L5.18431 5.21609C7.31231 5.00009 9.46831 5.08409 11.6283 5.29609C11.7923 5.31209 11.9123 5.46009 11.8963 5.62409C11.8843 5.78009 11.7523 5.89609 11.6003 5.89609Z" fill="#FF0000" />
                            <path d="M6.6001 5.49112C6.5841 5.49112 6.5681 5.49112 6.5481 5.48712C6.3881 5.45912 6.2761 5.30312 6.3041 5.14312L6.3921 4.61912C6.4561 4.23512 6.5441 3.70312 7.4761 3.70312H8.5241C9.4601 3.70312 9.5481 4.25512 9.6081 4.62312L9.6961 5.14312C9.7241 5.30712 9.6121 5.46312 9.4521 5.48712C9.2881 5.51512 9.1321 5.40312 9.1081 5.24312L9.0201 4.72312C8.9641 4.37512 8.9521 4.30712 8.5281 4.30712H7.4801C7.0561 4.30712 7.0481 4.36312 6.9881 4.71912L6.8961 5.23912C6.8721 5.38712 6.7441 5.49112 6.6001 5.49112Z" fill="#FF0000" />
                            <path d="M9.28425 12.303H6.71625C5.32025 12.303 5.26425 11.531 5.22025 10.907L4.96025 6.87896C4.94825 6.71496 5.07625 6.57096 5.24025 6.55896C5.40825 6.55096 5.54825 6.67496 5.56025 6.83896L5.82025 10.867C5.86425 11.475 5.88025 11.703 6.71625 11.703H9.28425C10.1243 11.703 10.1403 11.475 10.1803 10.867L10.4403 6.83896C10.4523 6.67496 10.5963 6.55096 10.7603 6.55896C10.9243 6.57096 11.0523 6.71096 11.0403 6.87896L10.7803 10.907C10.7363 11.531 10.6803 12.303 9.28425 12.303Z" fill="#FF0000" />
                            <path d="M8.66423 10.1039H7.33223C7.16823 10.1039 7.03223 9.96791 7.03223 9.80391C7.03223 9.63991 7.16823 9.50391 7.33223 9.50391H8.66423C8.82823 9.50391 8.96423 9.63991 8.96423 9.80391C8.96423 9.96791 8.82823 10.1039 8.66423 10.1039Z" fill="#FF0000" />
                            <path d="M9.00019 8.50234H7.0002C6.8362 8.50234 6.7002 8.36634 6.7002 8.20234C6.7002 8.03834 6.8362 7.90234 7.0002 7.90234H9.00019C9.16419 7.90234 9.30019 8.03834 9.30019 8.20234C9.30019 8.36634 9.16419 8.50234 9.00019 8.50234Z" fill="#FF0000" />
                            <rect fill="white" fillOpacity="0.1" height="15.8" rx="3.9" stroke="#CFCFCF" strokeWidth="0.2" width="15.8" x="0.1" y="0.1" />
                          </svg>
                        </button>
                        <Image
                          alt={stock.name}
                          className='px-1 py-2 rounded-10 border-4 border-[#F3F3F3]'
                          height={100}
                          src={typeof stock.product_image_1 === 'string' ? stock.product_image_1 : URL.createObjectURL(stock.product_image_1 as File)}
                          width={100}
                        />
                      </div>
                      :
                      <div className='flex'>
                        <Label
                          className="flex w-fit cursor-pointer items-center justify-center gap-2 relative"
                          htmlFor={`product-image`}
                        >
                          {softDeletedImage && (
                            <button
                              className='absolute top-2 right-2'
                              onClick={() => {
                                updateField(0, {
                                  ...fields[0],
                                  product_image_1: softDeletedImage,
                                })
                                setSoftDeletedImage('')
                              }}
                            >
                              <ResetIcon />
                            </button>
                          )}
                          <svg fill="none" height="100" viewBox="0 0 36 36" width="100" xmlns="http://www.w3.org/2000/svg">
                            <rect height="35.6" rx="3.8" stroke="#000000" strokeDasharray="2 2" strokeWidth="0.4" transform="matrix(-1 0 0 1 35.6 0)" width="35.6" x="-0.2" y="0.2" />
                            <path d="M14 18H22C22.205 18 22.375 17.83 22.375 17.625C22.375 17.42 22.205 17.25 22 17.25H14C13.795 17.25 13.625 17.42 13.625 17.625C13.625 17.83 13.795 18 14 18Z" fill="#000000" />
                            <path d="M18 14V22C18 22.205 17.83 22.375 17.625 22.375C17.42 22.375 17.25 22.205 17.25 22V14C17.25 13.795 17.42 13.625 17.625 13.625C17.83 13.625 18 13.795 18 14Z" fill="#000000" />
                          </svg>
                          <input
                            accept="image/*"
                            className="hidden"
                            id={`product-image`}
                            type="file"
                            onChange={handleImageChange}
                          />
                        </Label>
                      </div>
                    }
                    <div className="text-base font-medium">
                      Product details
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="category"
                      >
                        Product category
                      </Label>
                      <Controller
                        control={control}
                        defaultValue={stock.category}
                        name={`stocks.${index}.category`}
                        render={({ field: { onChange, value } }) => (
                          <StockCategoryCombobox
                            companyId={companyId}
                            defaultName={stock.category}
                            id="category"
                            placeholder="Select your Category"
                            value={value || stock.category}
                            onChange={onChange}
                          />
                        )}
                      />
                      {errors?.stocks?.[index]?.category && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.category?.message
                          }
                        />
                      )}
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="subcategory"
                      >
                        Sub category
                      </Label>
                      <Controller
                        control={control}
                        defaultValue={stock.subcategory}
                        name={`stocks.${index}.subcategory`}
                        render={({ field: { onChange, value } }) => (
                          <StockSubCategoryCombobox
                            categoryId={categoryWatch}
                            companyId={companyId}
                            defaultName={stock.subcategory_name}
                            id="subcategory"
                            placeholder="Select your Sub Category"
                            value={value || stock.subcategory}
                            onChange={onChange}
                          />
                        )}
                      />
                      {errors?.stocks?.[index]?.subcategory && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.subcategory?.message
                          }
                        />
                      )}
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="product_name"
                      >
                        Product name
                      </Label>
                      <Input
                        autoCapitalize="none"
                        autoComplete="off"
                        autoCorrect="off"
                        className="rounded-sm border bg-[#F5F7F9]"
                        id="product_name"
                        type="text"
                        {...register(`stocks.${index}.name`)}
                      />
                      {errors?.stocks?.[index]?.name && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.name?.message
                          }
                        />
                      )}
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="sku"
                      >
                        SKU
                      </Label>
                      <Input
                        autoCapitalize="none"
                        autoComplete="off"
                        autoCorrect="off"
                        className="rounded-sm border bg-[#F5F7F9]"
                        id="sku"
                        type="text"
                        {...register(`stocks.${index}.sku`)}
                      />
                      {errors?.stocks?.[index]?.sku && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.sku?.message
                          }
                        />
                      )}
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="spec_model_variant"
                      >
                        Spec/Model/Variant
                      </Label>
                      <Input
                        autoCapitalize="none"
                        autoComplete="off"
                        autoCorrect="off"
                        className="rounded-sm border bg-[#F5F7F9]"
                        id="spec_model_variant"
                        type="text"
                        {...register(`stocks.${index}.spec_model_variant`)}
                      />
                      {errors?.stocks?.[index]?.spec_model_variant && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.spec_model_variant?.message
                          }
                        />
                      )}
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="stock_price"
                      >
                        Cost price
                      </Label>
                      <Input
                        autoCapitalize="none"
                        autoComplete="off"
                        autoCorrect="off"
                        className="rounded-sm border bg-[#F5F7F9]"
                        id="stock_price"
                        type="number"
                        {...register(`stocks.${index}.stock_price`)}
                      />
                      {errors?.stocks?.[index]?.stock_price && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.stock_price?.message
                          }
                        />
                      )}
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="selling_price"
                      >
                        Selling Price
                      </Label>
                      <Input
                        autoCapitalize="none"
                        autoComplete="off"
                        autoCorrect="off"
                        className="rounded-sm border bg-[#F5F7F9]"
                        id="selling_price"
                        type="number"
                        {...register(`stocks.${index}.selling_price`, {
                          required: true,
                        })}
                      />
                      {errors?.stocks?.[index]?.selling_price && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.selling_price?.message
                          }
                        />
                      )}
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="product_id"
                      >
                        IDs
                      </Label>
                      <Input
                        autoCapitalize="none"
                        autoComplete="off"
                        autoCorrect="off"
                        className="rounded-sm border bg-[#F5F7F9]"
                        id="product_id"
                        type="text"
                        {...register(`stocks.${index}.product_id`)}
                      />
                      {errors?.stocks?.[index]?.product_id && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.product_id?.message
                          }
                        />
                      )}
                    </div>
                    <div className="w-full">
                      <Label
                        className="mb-1 block text-xs text-label-text"
                        htmlFor="product_description"
                      >
                        Product description
                      </Label>
                      <Textarea
                        autoCapitalize="none"
                        autoComplete="off"
                        autoCorrect="off"
                        className="rounded-sm border bg-[#F5F7F9]"
                        id="product_description"
                        {...register(`stocks.${index}.product_description`)}
                      />
                      {errors?.stocks?.[index]?.product_description && (
                        <FormError
                          errorMessage={
                            errors?.stocks?.[index]?.product_description?.message
                          }
                        />
                      )}
                    </div>
                  </div>
                );
              })}

              <Button
                className="flex py-3 text-sm"
                disabled={isUpdateStocksLoading || !reformedPayload.length}
                size="fullWidth"
              >
                {isUpdateStocksLoading && (
                  <SmallSpinner className="mr-2 animate-spin" />
                )}

                <span>Save changes</span>
              </Button>
            </form>
          </DialogBody>
        </DialogContent>
      </Dialog>

      <SuccessModal
        heading="Edit successful"
        isSuccessModalOpen={isSuccessModalOpen}
        setSuccessModalState={setSuccessModalState}
        subheading={`You have successfully edited product`}
      >
        <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
          <Button
            className="grow text-base"
            size="lg"
            onClick={() => {
              closeSuccessModal();
              setEditProductModalState(false);
            }}
          >
            Okay
          </Button>
        </div>
      </SuccessModal>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 text-base"
            size="lg"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
