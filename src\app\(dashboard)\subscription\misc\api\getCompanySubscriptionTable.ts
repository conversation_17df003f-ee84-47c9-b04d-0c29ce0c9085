import { managementAxios } from '@/lib/axios';
import { useQuery } from '@tanstack/react-query';
import { ModuleProp, } from './getSubscriptionCards';

interface FetchOptions {
    is_active?: boolean;
    module_filter?: string;
    company_id: string;
}

export interface subPropTypes {
    company: string;
    total_subscriptions: number;
    active_subscriptions: number;
    expired_subscriptions: number;
    is_free_trial: boolean
    is_paid_subscription: boolean
    free_trial_days_remaining: number
    subscriptions: ModuleProp[];
}

export const getCompanySubscriptionTable = async ({ is_active, module_filter, company_id }: FetchOptions) => {
    let filterStatus = '';

    // Only include is_active if it's provided (not null or undefined)
    if (is_active !== null && is_active !== undefined) {
        filterStatus += `&is_active=${is_active}`;
    }

    // Only include module_filter if it's provided (not null or undefined)
    if (module_filter) {
        filterStatus += `&module_name=${module_filter}`;
    }

    try {
        const response = await managementAxios.get(`/subscription_and_invoicing/company_subscription?company_id=${company_id}${filterStatus}`);

        const subscriptionCards = response.data;

        // Merge items from paid, free_trial, and free_merchant into one array
        const modules: ModuleProp[] = [
            ...(subscriptionCards?.subscriptions.paid?.items || []),
            ...(subscriptionCards?.subscriptions.free_merchant?.items || [])
        ];

        return {
            company: subscriptionCards.company,
            total_subscriptions: subscriptionCards.total_subscriptions,
            active_subscriptions: subscriptionCards.active_subscriptions,
            expired_subscriptions: subscriptionCards.expired_subscriptions,
            subscriptions: modules, // Return all modules merged into one array
            is_free_trial: subscriptionCards?.is_free_trial,
            is_paid_subscription: subscriptionCards?.is_paid_subscription,
            free_trial_days_remaining: subscriptionCards?.free_trial_days_remaining,
        } as subPropTypes;

    } catch (error) {
        // console.error('Error fetching company subscription table:', error);
        return {
            company: 'Error',
            total_subscriptions: 0,
            active_subscriptions: 0,
            expired_subscriptions: 0,
            free_trial_days_remaining: 0,
            is_free_trial: false,
            is_paid_subscription: false,
            subscriptions: [] // Return an empty array in case of error
        } as subPropTypes;
    }
};

export const useCompanySubscriptionTable = (fetchOptions: FetchOptions) => {
    return useQuery({
        queryKey: ['companySubscriptionTable', fetchOptions],
        queryFn: () => getCompanySubscriptionTable(fetchOptions),
        keepPreviousData: true,
        staleTime: 5 * 60 * 1000,
        cacheTime: 10 * 60 * 1000,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
    });
};
