'use client'

import * as React from 'react';
import { cn } from '@/utils/classNames';

const employeeBenefitsOptions = [
  {
    name: 'Buy Now Pay Later',
    icon: "bg-[url('/images/employee-benefits/buy_now-pay-later.svg')]",
    subText: 'Get items of your choice with ease and at flexible pocket friendly payment prices',
    link: '#',
    bgColors: 'bg-[#F8F8F8] py-[19px]',
  },
  {
    name: 'Instant wage',
    icon: "bg-[url('/images/employee-benefits/instant-wage-img.png')]",
    subText:
      "Get instant access to a portion of your earned wages before payday, helping you manage expenses whenever needed.",
    link: '/spend-management',
    bgColors: 'bg-[#FFE7C6]',
  },
  {
    name: 'Heath insurance for loved ones ',
    icon: "bg-[url('/images/employee-benefits/health-insurance.png')]",
    subText: "Secure health coverage for your family members, ensuring they receive the care they deserve when it matters most.",
    link: '#',
    bgColors: 'bg-[#F4F8FF]',
  },

  {
    name: 'Rent Financing',
    icon: "bg-[url('/images/employee-benefits/rent-financing.png')]",
    subText: "Take the stress out of rent payments with financing options that let you focus on other financial goals.",
    link: '#',
    bgColors: 'bg-[#E8FFF2]',
  },
  {
    name: 'Group Rotational Savings',
    icon: "bg-[url('/images/employee-benefits/group-rotational-savings.png')]",
    subText: "Join a community savings circle where funds rotate among participants, creating a support system for shared financial goals.",
    link: '#',
    bgColors: 'bg-[#F3F7FE]',
  },
  {
    name: 'Personal Savings',
    icon: "bg-[url('/images/employee-benefits/personal-savings.png')]",
    subText: "Grow your wealth through regular savings, with options tailored to your financial aspirations.",
    link: '#',
    bgColors: 'bg-[#FFF1F1]',
  },
  {
    name: 'Save Now Pay Later',
    icon: "bg-[url('/images/employee-benefits/save-pay-later.png')]",
    subText: "Save toward your purchases over time and enjoy what you need when you're ready—interest-free.",
    link: '#',
    bgColors: 'bg-[#F8F8F8]',
  },
  {
    name: 'Savings Backed Loans',
    icon: "bg-[url('/images/employee-benefits/savings-backend-loan.png')]",
    subText: "Use your savings as collateral to unlock low-interest loans, giving you more options without compromising your savings.",
    link: '#',
    bgColors: 'bg-[#FFFCF1]',
  },
];

export default function Dashboard() {
  return (
    <>
      <div className="mb-3 mt-1.5 bg-white px-6 pb-5 pt-6 md:px-7 md:py-3 md:pb-6 md:pt-10 lg:px-11">
        {/* eslint-disable-next-line tailwindcss/no-contradicting-classname */}
        <div className="rounded-2xl bg-[#EEF6FF] bg-[url('/images/employee-benefits/employee-benefits-banner.png')] bg-[length:9.375rem] bg-right-bottom bg-no-repeat px-4 py-5 sm:bg-[length:12.6875rem] sm:p-9 lg:bg-[length:18.1875rem] lg:px-16 lg:py-9">
          <h3 className="mb-2 max-w-[14.375rem] font-wix-display text-lg font-semibold leading-tight text-main-solid lg:max-w-[25.125rem]">
            Amazing benefits like
            no other at your fingertips
          </h3>

          <p className="max-w-[13.25rem] text-xs text-[#444F73] lg:max-w-[20.4375rem]">
            Discover packages that bring value and
            convenience, designed just for you.
          </p>
        </div>
      </div>

      <ul className="mb-6 grid items-stretch gap-4 px-6 sm:grid-cols-2 md:grid-cols-1 md:px-7 lg:grid-cols-2 lg:px-11 xl:grid-cols-3 2xl:grid-cols-4">
        {
          employeeBenefitsOptions.map(({ name, subText, bgColors, icon }) => {
            return (
              <li key={name}>
                <div className="flex flex-col justify-between items-stretch h-full rounded-2xl bg-white px-[6px] pt-[6px] pb-5 text-left text-base relative">
                  <div className={cn("flex flex-col gap-2 rounded-t-[17px] h-[190px] grow bg-center bg-contain object-fill bg-no-repeat relative", bgColors, icon)}>
                    <div className="absolute inset-0 bg-black/40 rounded-t-[17px] flex items-center justify-center">
                      <span className="text-white font-semibold text-lg">Coming Soon</span>
                    </div>
                  </div>

                  <div className="mt-[21px] px-4">
                    <div className='flex items-center gap-3'>
                      <h3 className="mb-0.5 text-lg text-[#000000]">{name}</h3>
                    </div>

                    <p className="text-[11px] text-[#444F73] leading-normal lg:pr-2">
                      {subText}
                    </p>
                  </div>
                </div>
              </li>
            );
          })}
      </ul>
    </>
  );
}
