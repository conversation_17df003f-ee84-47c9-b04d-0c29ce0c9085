"use client"
// import { redirect } from 'next/navigation';
import React from 'react';

import {
  // Companies,
  CompaniesResultsEntity,
  Users,
} from '@/app/(dashboard)/spend-management/misc/types';

import { CreateTeamModal } from '../../misc/components/modals';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useStockTeamUsers } from '@/app/(dashboard)/stock/misc/api/get-user-team/getUsers';

// const CALLBACK_URL = '/sales/create-team';

export default function CreateTeam() {

  const { data } = useCompaniesList()
  const { data: users } = useStockTeamUsers({})
  return (
    <>
      <CreateTeamModal
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={data?.results as CompaniesResultsEntity[]}
        initialUsersResponse={users as Users}
      />
    </>
  );
}
