'use client'


import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';
import { RecordExpenseReceiptDialog } from '@/app/(dashboard)/spend-management/misc/components';
import { Companies } from '@/app/(dashboard)/spend-management/misc/types';

export default function CaptureReciept({
  searchParams,
}: {
  searchParams: { companyId: string };
}) {
  const { companyId } = searchParams;

  const _CALLBACK_URL = `/spend-management/expenses/record-expense/capture-receipt/?companyId=${companyId}`;

  const { data: companiesResponse } = useSpendManagementCompanies();


  const { results: companies } = companiesResponse as Companies;

  return <RecordExpenseReceiptDialog
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    companies={companies} />;
}
