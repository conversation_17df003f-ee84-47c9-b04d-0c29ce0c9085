import { managementAxios } from '@/lib/axios';

import { useQuery } from '@tanstack/react-query';

export interface EmoloyeeAnnouncementData {
  announcement: Announcement[]
  event: Event[]
  birthday: Birthday[]
  work_anniversary: WorkAnniversary[]
}

export interface Event {
  announcement_type: string
  announcement_title: string
  announcement_body: string
  announcement_date: string
  created_by: string
}

export interface Announcement {
  announcement_type: string
  announcement_title: string
  announcement_body: string
  announcement_date: string
  created_by: string
}
export interface Birthday {
  announcement_type: string
  announcement_title: string
  announcement_body: string
  announcement_date: string
  created_by: string
}
export interface WorkAnniversary {
  announcement_type: string
  announcement_title: string
  announcement_body: string
  announcement_date: string
  created_by: string
}

interface FetchEmployeeEventsAnnouncement {
  companyid: string;
}

export const getEmployeeEventsAnnouncement = async ({
  companyid,
}: FetchEmployeeEventsAnnouncement) => {
  const response = await managementAxios.get(
    `/payroll/announcement_event/?company_uuid=${companyid}`
  );
  return response.data as EmoloyeeAnnouncementData;
};

export const useEmployeeEventsAnnouncement = (
  fetchOptions?: FetchEmployeeEventsAnnouncement
) => {
  return useQuery({
    queryKey: ['payroll-employee-announcement', fetchOptions],
    queryFn: () => {
      if (fetchOptions) return getEmployeeEventsAnnouncement(fetchOptions);
    },
    enabled: !!fetchOptions,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};
