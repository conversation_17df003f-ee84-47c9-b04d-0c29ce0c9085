'use client'
import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';

import { ViewTeamsDialog } from '../../misc/components/modals';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';

const ViewTeams = () => {

  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies /*count: companiesCount*/ } = companiesResponse as Companies;

  // if (companiesCount < 1) {
  //   redirect('/spend-management/companies/create-company');
  // }

  return (
    <>
      <ViewTeamsDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies}
      // initialUsersResponse={usersResponse}
      />
    </>
  );
};

export default ViewTeams;
