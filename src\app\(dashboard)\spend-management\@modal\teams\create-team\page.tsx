'use client'

import { redirect, useSearchParams } from 'next/navigation';



import { CreateTeamDialog } from '../../../misc/components';
import type { CompaniesResultsEntity, Users } from '../../../misc/types';
import { useSpendManagementCompanies } from '../../../misc/api/getSpendManagementCompanies';
import { useSpendManagementUsers } from '../../../misc/api';
import { LoaderModal } from '@/components/core';
import SubscriptionErrorModal from '../../../misc/components/subscription/ErrorSubscriptionModal';
import { AxiosError } from 'axios';

const _CALLBACK_URL = '/spend-management/teams/create-team';

export default function CreateTeam() {
  // const {
  //   user: { access },
  // } = await getServerSessionOrRedirect({
  //   authOptions,
  //   callbackUrl: CALLBACK_URL,
  // });

  // const companiesResponse = await spendManagementFetchClient<Companies>(
  //   'req/get-companies/',
  //   {
  //     headers: { Authorization: `Bearer ${access}` },
  //   }
  // );

  // const usersResponse = await spendManagementFetchClient<Users>(
  //   'core/query-users/?search=',
  //   {
  //     headers: { Authorization: `Bearer ${access}` },
  //   }
  // );

  // if (companiesResponse === 'unauthorized' || usersResponse == 'unauthorized') {
  //   redirect(`/login?callbackUrl=${CALLBACK_URL}`);
  // }

  const searchParams = useSearchParams();
  const company = (searchParams.get('company') as string);

  const fetchOptions = {
    companyId: company
  }
  const { data: companiesResponse, isLoading: isLoadingCompanyListResponse, error } = useSpendManagementCompanies()
  const { data: usersResponse, isLoading: isLoadingUserResponse } = useSpendManagementUsers(fetchOptions)

  const { results: companies, count: companiesCount } = companiesResponse || {};

  if (Number(companiesCount) < 1) {
    redirect('/spend-management/companies/create-company');
  }

  const isLoading =
    isLoadingCompanyListResponse
    || isLoadingUserResponse
  const statusError = error as AxiosError
  return (
    <>
      <LoaderModal isOpen={isLoading} />
      {(!isLoading && statusError?.response?.status !== 403) && (
        <CreateTeamDialog
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          companies={companies as CompaniesResultsEntity[]}
          initialUsersResponse={usersResponse as Users}
        />
      )}

      {
        (statusError?.response?.status === 403 && !isLoading) && <SubscriptionErrorModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          message={String(statusError?.response?.data?.error as unknown as string) ?? ""}
        />
      }
    </>
  );
}
