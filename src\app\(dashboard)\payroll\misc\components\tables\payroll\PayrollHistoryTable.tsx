'use client';

import { ColumnDef } from '@tanstack/react-table';
import { subMonths } from 'date-fns';
// import { useSearchParams } from 'next/navigation';
import * as React from 'react';
import { DateRange } from 'react-day-picker';
import { useForm } from 'react-hook-form';

import {
  // Accordion,
  // AccordionContent,
  // AccordionItem,
  // AccordionTrigger,
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/core';
import { cn } from '@/utils/classNames';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import { addCommasToNumber } from '@/utils/numbers';

// import { usePayrollHistory } from '../../api';
import { PayrollHistoryEntity } from '../../../types';
import { usePayrollHistoryDetails } from '@/stores';
import { useRouter } from 'next/navigation';

export type InviteEmployeeFormProps = {
  companyid: string;
  payrollHistoryList: PayrollHistoryEntity | undefined;
};

export const columns: ColumnDef<PayrollHistoryEntity>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
  },
  {
    accessorKey: 'phone_number',
    header: 'Phone number',
  },
  {
    accessorKey: 'gross_amount',
    header: 'Gross amount',
  },
  {
    accessorKey: 'net_amount',
    header: 'Net amount',
  },
  {
    accessorKey: 'gender',
    header: 'Gender',
  },
  {
    accessorKey: 'gross_amount',
    header: 'Gross amount',
  },
  {
    accessorKey: 'net_amount',
    header: 'Net amount',
  },
  {
    accessorKey: 'payable_amount',
    header: 'Payable amount',
  },
  {
    accessorKey: 'is_active',
    header: 'Status',
    cell: ({ row }) => {
      const status = String(row.getValue('is_active'));
      const formatted = convertKebabAndSnakeToTitleCase(status);
      return (
        <span
          className={cn(
            status === 'true'
              ? 'Approved text-[#18A201]'
              : 'Pending text-[#FF0000]'
          )}
        >
          {formatted}
        </span>
      );
    },
  },
  {
    id: 'actions',
    header: 'Action',
    cell: () => {
      return (
        <Dialog>
          <DialogTrigger className="whitespace-nowrap px-4" variant="light">
            View details
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Transaction details</DialogTitle>
              <DialogClose>Close</DialogClose>
            </DialogHeader>
          </DialogContent>
        </Dialog>
      );
    },
  },
];

const today = new Date();
const oneMonthAgo = subMonths(new Date(), 1);


export function PayrollHistoryTable({
  // companyid,
  payrollHistoryList,
}: InviteEmployeeFormProps) {

  const router = useRouter()

  const { setPayrollTransactionHistory } = usePayrollHistoryDetails();

  const { register } = useForm<{
    searchFilter: string;
    dateFilter: DateRange;
  }>({
    defaultValues: {
      searchFilter: '',
      dateFilter: {
        from: oneMonthAgo,
        to: today,
      },
    },
  });

  return (
    <div className="bg-[#F8F9FB] ">
      <div className=" bg-[#F8F9FB] border-y border-[#E9E9E9]">
        <div className=" flex w-full items-center gap-[14px] rounded-10 bg-white px-[20px] py-[22px] md:px-[40px]">
          <div>
            <h1 className="text-base font-normal text-black">
              Employee Payroll History
              <span className="ml-3 rounded-[5px] bg-[#F2F5FF] px-6 py-[5px] text-[13px] text-[#032282]">
                {
                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                  //@ts-ignore
                  payrollHistoryList?.length || 0}
              </span>
            </h1>
          </div>
          <section className="flex items-center justify-between">
            <div className="flex max-w-[17.1875rem] items-center gap-2.5 rounded-lg border border-[#d6d6d6]/50 px-4 transition duration-300 ease-in-out focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2">
              <svg
                className="shrink-0"
                fill="none"
                height={20}
                viewBox="0 0 20 20"
                width={20}
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m17.766 16.69-5.073-5.073a5.807 5.807 0 0 0 1.213-3.57 5.824 5.824 0 0 0-1.716-4.143 5.816 5.816 0 0 0-4.143-1.716 5.824 5.824 0 0 0-4.143 1.716 5.813 5.813 0 0 0-1.716 4.143c0 1.564.61 3.037 1.716 4.143a5.813 5.813 0 0 0 4.143 1.716c1.308 0 2.55-.426 3.568-1.21l5.072 5.07a.16.16 0 0 0 .227 0l.852-.85a.16.16 0 0 0 0-.227Zm-6.625-5.55a4.351 4.351 0 0 1-3.094 1.282 4.351 4.351 0 0 1-3.094-1.281 4.351 4.351 0 0 1-1.281-3.094c0-1.168.455-2.268 1.281-3.094a4.351 4.351 0 0 1 3.094-1.281 4.34 4.34 0 0 1 3.094 1.281 4.351 4.351 0 0 1 1.28 3.094 4.348 4.348 0 0 1-1.28 3.094Z"
                  fill="#556575"
                />
              </svg>

              <input
                autoCapitalize="none"
                autoComplete="off"
                autoCorrect="off"
                className="grow px-0 py-3 text-sm ring-0 placeholder:text-[#556575] focus-visible:outline-none"
                id="team_name"
                placeholder="Search"
                type="text"
                {...register('searchFilter')}
              />
            </div>
          </section>
        </div>
      </div>

      {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        payrollHistoryList?.map(transaction => (
          <>
            <div className="w-full rounded-[10px] mb-1 bg-white px-[20px] md:px-[30px]" key={transaction.bulk_id}>
              <div
                className="items-left mt-1 flex w-full flex-col justify-between md:pr-32 gap-9 md:gap-16 rounded-[10px] bg-white py-[19px] md:flex-row md:flex-wrap md:items-center"
                key={transaction.id}
              >
                <div>
                  <p className="text-[14px] text-[#00A37D] bg-[#00A37D] w-fit rounded-lg py-[6px] px-2 bg-opacity-[10%]">
                    {transaction.payroll_month || 'Not available'}
                  </p>
                  <p className="mt-[10px] font-sans text-sm text-[#818181]">
                    Payroll month
                  </p>
                </div>
                <div>
                  <p className="text-[14px] text-[#00A37D] bg-[#00A37D] w-fit rounded-lg py-[6px] px-2 bg-opacity-[10%]">
                    {transaction.payroll_year || 'Not available'}
                  </p>
                  <p className="mt-[10px] font-sans text-sm text-[#818181]">
                    Payroll Year
                  </p>
                </div>
                <div>
                  <p className="font-sans text-[13px] text-black">
                    {transaction.date_completed
                      ? new Date(transaction.date_completed).toLocaleString()
                      : 'Not available'}
                  </p>
                  <p className="mt-[10px] font-sans text-sm text-[#818181]">
                    Date Completed
                  </p>
                </div>
                <div>
                  <p className="font-sans text-[13px] text-black">
                    {transaction.employee_count}
                  </p>
                  <p className="mt-[10px] font-sans text-sm text-[#818181]">
                    Total employees
                  </p>
                </div>
                <div>
                  <p className="font-sans text-[13px] text-black">
                    {addCommasToNumber(transaction.total_amount)}
                  </p>
                  <p className="mt-[10px] font-sans text-sm text-[#818181]">
                    Total amount
                  </p>
                </div>
                <div>
                  <p className="font-sans text-[13px] text-[#00A37D]">
                    {transaction.status}
                  </p>
                  <p className="mt-[10px] font-sans text-sm text-[#818181]">
                    Status
                  </p>
                </div>

                <Button className="rounded-[8px] bg-[#F2F5FF] px-5 py-[10px] text-xs text-[#032282]"
                  onClick={() => {
                    setPayrollTransactionHistory(transaction)
                    router.push('/payroll/payroll-history-details')
                  }}>
                  View
                </Button>
                {/* <p>Gross Amount: {payrollData.gross_amount}</p>
                <p>Net Amount: {payrollData.net_amount}</p>
                <p>Salary Grade: {payrollData.salary_grade}</p>
                <p>Status: {payrollData.status}</p>
                <p>Status: </p> */}
              </div>

            </div>

            {/* <Accordion key={transaction.bulk_id} type="single" collapsible>
              <AccordionItem
                className="w-full rounded-lg border-none px-2"
                value="item-1"
              >
                <AccordionTrigger className="w-full no-underline hover:no-underline">
                  <div className="w-full rounded-[10px] bg-white px-5 py-[19px]">
                    <p className="items-left flex flex-col gap-[8px] whitespace-nowrap font-sans text-[14px] text-[#032282] lg:flex-row lg:items-center ">
                      Payroll Month:
                      <span className="rounded-[8px] border-[0.3px] border-[#5879fd80] px-8 py-2">
                        {transaction.payroll_month || 'Not available'}
                      </span>
                    </p>

                    <div
                      className="items-left mt-1 flex w-full flex-col justify-between gap-10 rounded-[10px] bg-white py-[19px] md:flex-row md:flex-wrap md:items-center"
                      key={transaction.id}
                    >
                      <div>
                        <p className="font-sans text-[13px] text-black">
                          {transaction.date_completed
                            ? new Date(transaction.date_completed).toLocaleString()
                            : 'Not available'}
                        </p>
                        <p className="mt-[10px] font-sans text-sm text-[#818181]">
                          Date Completed
                        </p>
                      </div>
                      <div>
                        <p className="font-sans text-[13px] text-black">
                          {transaction.payroll_month || 'Not available'}
                        </p>
                        <p className="mt-[10px] font-sans text-sm text-[#818181]">
                          Month
                        </p>
                      </div>
                      <div>
                        <p className="font-sans text-[13px] text-black">
                          {transaction.employee_count}
                        </p>
                        <p className="mt-[10px] font-sans text-sm text-[#818181]">
                          Total employees
                        </p>
                      </div>
                      <div>
                        <p className="font-sans text-[13px] text-black">
                          {transaction.total_amount}
                        </p>
                        <p className="mt-[10px] font-sans text-sm text-[#818181]">
                          Total amount
                        </p>
                      </div>
                      <div>
                        <p className="font-sans text-[13px] text-black">
                          {transaction.status}
                        </p>
                        <p className="mt-[10px] font-sans text-sm text-[#818181]">
                          Status
                        </p>
                      </div>

                      <Button className="rounded-[8px] bg-[#F2F5FF] px-5 py-[10px] text-xs text-[#032282]">
                        View
                      </Button>
  
                    </div>

                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  {transaction.payroll_history_data.map(
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    payrollData => (
                      <div
                        className="items-left mt-1 flex w-full flex-col justify-between gap-10 rounded-[10px] bg-white py-[19px] pl-3 pr-[48px] md:flex-row md:flex-wrap md:items-center md:pl-[46px] md:pr-[148px]"
                        key={payrollData.id}
                      >
                        <div>
                          <p className="font-sans text-[13px] text-black">
                            {transaction.date_completed
                              ? new Date(transaction.date_completed).toLocaleString()
                              : 'Not available'}
                          </p>
                          <p className="mt-[10px] font-sans text-sm text-[#818181]">
                            Date Completed
                          </p>
                        </div>
                        <div>
                          <p className="font-sans text-[13px] text-black">
                            {payrollData.phone_number}
                          </p>
                          <p className="mt-[10px] font-sans text-sm text-[#818181]">
                            Phone Number
                          </p>
                        </div>
                        <div>
                          <p className="font-sans text-[13px] text-black">
                            {transaction.payroll_month || 'Not available'}
                          </p>
                          <p className="mt-[10px] font-sans text-sm text-[#818181]">
                            Month
                          </p>
                        </div>
        
                        <div>
                          <p className="font-sans text-[13px] text-black">
                            {payrollData.payable_amount}
                          </p>
                          <p className="mt-[10px] font-sans text-xs text-[#818181]">
                            Total amount
                          </p>
                        </div>
                        <div>
                          <p className="font-sans text-[13px] text-black">
                            {transaction.status}
                          </p>
                          <p className="mt-[10px] font-sans text-xs text-[#818181]">
                            Status
                          </p>
                        </div>

                        <Button className="rounded-[8px] bg-[#F2F5FF] px-5 py-[10px] text-xs text-[#032282]">
                          View
                        </Button>
     
                      </div>
                    )
                  )}
                </AccordionContent>
              </AccordionItem>
            </Accordion> */}
          </>
        ))
      }

      {
        <div className="mt-4" >

          {/* <p>Payroll Month: {transaction.payroll_month || 'Not available'}</p>
            <p>Employee Count: {transaction.employee_count}</p>
            <p>Total Amount: {transaction.total_amount}</p> */}


        </div>
      }
      {/* <DataTable
        columns={columns}
        isFetching={isFetching}
        isLoading={isLoading}
        pageCount={pageCount}
        pageIndex={pageIndex}
        pageSize={pageSize}
        rows={rows}
        setPagination={setPagination}
      /> */}
    </div>
  );
}
