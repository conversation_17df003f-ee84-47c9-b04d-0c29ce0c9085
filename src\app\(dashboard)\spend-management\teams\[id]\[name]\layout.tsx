
'use client'; // Ensure this is a client component

import * as React from 'react';
import { useParams, usePathname } from 'next/navigation';

import {
  SingleTeamCards,
  SingleTeamTabs,
} from '@/app/(dashboard)/spend-management/misc/components';

import { Teams } from '@/app/(dashboard)/spend-management/misc/types';


import { useAllTeamsSpendManagement } from '../../../misc/api/getTeamListWIthoutCompanyId';
import { useSingleTeamOnly } from '../../../misc/api/getSingleTeamOnly';
import { AxiosError } from 'axios';
import SubscriptionErrorModal from '../../../misc/components/subscription/ErrorSubscriptionModal';
import { LoaderModal } from '@/components/core';


export default function SingleTeamLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { name: urlName, id, assetDetail } = useParams();
  const name = decodeURIComponent(String(urlName)).trim(); // Trim spaces to avoid comparison issues

  // Client-side hook to get the current pathname
  const pathname = usePathname();

  // Normalize pathname by decoding and trimming trailing slashes
  const normalizedPathname = decodeURIComponent(pathname).replace(/\/$/, '').trim(); // Trim spaces as well
  const normalizedPathnameWithoutQuery = normalizedPathname.split('?')[0];

  // Normalize spaces in pathname to avoid multiple spaces
  const normalizeSpaces = (str: string) => str.replace(/\s+/g, ' ').trim();

  const normalizedPathnameFinal = normalizeSpaces(normalizedPathnameWithoutQuery);

  // Define an array of pathnames where components should be hidden
  const excludedPathnames = [
    `/spend-management/teams/${id}/${normalizeSpaces(name)}/overview`,
    `/spend-management/teams/${id}/${normalizeSpaces(name)}/assets/overview`,
    `/spend-management/teams/${id}/${normalizeSpaces(name)}/assets/register`,
    `/spend-management/teams/${id}/${normalizeSpaces(name)}/assets/depreciation`,
    `/spend-management/teams/${id}/${normalizeSpaces(name)}/assets/${normalizeSpaces(String(assetDetail))}/details`, // Ensure assetDetail is used correctly
  ];


  // Check if the current pathname matches any of the excluded paths
  const shouldHideComponents = excludedPathnames.includes(normalizedPathnameFinal);


  const { data: teamsResponse, error: teamsError } = useAllTeamsSpendManagement();

  // Fetch data for a single team
  const { data: singleTeam, error: singleTeamError, isLoading } = useSingleTeamOnly(String(id));

  // Extract the company details from the single team data
  const { company_id: companyId, company_name: companyName } = singleTeam?.results?.[0] || {};

  // Define the errors as AxiosError types
  const teamsAxiosError = teamsError as AxiosError;
  const singleTeamAxiosError = singleTeamError as AxiosError;

  // Combine both errors to get the current error state (if any)
  const statusError: AxiosError = teamsAxiosError || singleTeamAxiosError;


  return (
    <>
      <LoaderModal isOpen={isLoading} />

      {
        (statusError?.response?.status === 403 && !isLoading) && <SubscriptionErrorModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          message={String(statusError?.response?.data?.error as unknown as string) ?? ""}
        />
      }
      {(!isLoading && statusError?.response?.status !== 403) && (
        <div className="mt-2">
          {/* Conditionally render components based on the pathname */}
          {(!shouldHideComponents && !assetDetail) && (
            <>
              <SingleTeamCards
                id={id as string}
                name={name}
                singleTeam={singleTeam as Teams}
                teamsResponse={teamsResponse as Teams}
              />

              <div className="mb-1 rounded-10 bg-white md:mx-7 lg:mx-11">
                <SingleTeamTabs companyId={String(companyId)} companyName={String(companyName)} />
              </div>
            </>
          )}

          {/* Render children regardless of the condition */}
          {children}
        </div>)}
    </>
  );
}
