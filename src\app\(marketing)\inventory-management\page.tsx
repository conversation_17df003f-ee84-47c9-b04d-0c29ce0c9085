"use client"
import Image from "next/image"
import { motion } from "framer-motion"
import { useInView } from "react-intersection-observer"
import { <PERSON><PERSON>, LinkButton } from "@/components/core"
import { useState, useEffect } from "react"
import {
  Package,
  TrendingUp,
  BarChart3,
  ShoppingCart,
  CreditCard,
  FileText,
  Users,
  DollarSign,
  Warehouse,
  PieChart,
  Receipt,
  RefreshCw,
  Smartphone,
  BookOpen,
  Banknote,
  ArrowRight,
  CheckCircle,
  Zap,
  Shield,
  Clock,
} from "lucide-react"
import { VideoModal } from "../landingmisc/components"

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
}

const stagger = {
  visible: { transition: { staggerChildren: 0.1 } },
}

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 },
}

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-ignore
const AnimatedSection = ({ children, className }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  return (
    <motion.div
      animate={inView ? "visible" : "hidden"}
      className={className}
      initial="hidden"
      ref={ref}
      variants={stagger}
    >
      {children}
    </motion.div>
  )
}

const stockFeatures = [
  {
    icon: Package,
    title: "Stock Inventory",
    description: "Real-time inventory tracking with automated alerts and comprehensive stock level monitoring",
    features: ["Real-time tracking", "Low stock alerts", "Batch management", "Expiry tracking"],
  },
  {
    icon: Warehouse,
    title: "Stock Management",
    description: "Advanced warehouse management with location tracking and optimization tools",
    features: ["Multi-location", "Transfer management", "Audit trails", "Optimization"],
  },
  {
    icon: BarChart3,
    title: "Analytics & Reports",
    description: "Detailed insights into stock performance with predictive analytics",
    features: ["Performance metrics", "Demand forecasting", "Cost analysis", "Custom reports"],
  },
]

const salesFeatures = [
  {
    icon: Banknote,
    title: "Business Loans",
    description: "Access to business financing solutions integrated with your sales data",
    color: "bg-blue-500",
  },
  {
    icon: CreditCard,
    title: "Sales Transactions",
    description: "Complete transaction management with detailed tracking and reporting",
    color: "bg-green-500",
  },
  {
    icon: Users,
    title: "Customer Price List",
    description: "Dynamic pricing management with customer-specific pricing tiers",
    color: "bg-purple-500",
  },
  {
    icon: ShoppingCart,
    title: "Sell",
    description: "Streamlined selling process with integrated inventory management",
    color: "bg-orange-500",
  },
  {
    icon: DollarSign,
    title: "Sales Credit",
    description: "Credit management system with automated payment tracking",
    color: "bg-red-500",
  },
  {
    icon: RefreshCw,
    title: "Return/Refund",
    description: "Efficient return processing with automated inventory updates",
    color: "bg-teal-500",
  },
  {
    icon: Smartphone,
    title: "Sales POS Access",
    description: "Mobile point-of-sale system for on-the-go transactions",
    color: "bg-indigo-500",
  },
  {
    icon: FileText,
    title: "Invoicing",
    description: "Professional invoice generation with automated delivery",
    color: "bg-pink-500",
  },
  {
    icon: BookOpen,
    title: "Ledger",
    description: "Comprehensive financial ledger with real-time updates",
    color: "bg-yellow-500",
  },
  {
    icon: Receipt,
    title: "Invoice Financing",
    description: "Convert invoices to immediate cash flow with integrated financing",
    color: "bg-cyan-500",
  },
  {
    icon: PieChart,
    title: "Stock Financing",
    description: "Inventory-backed financing solutions for business growth",
    color: "bg-emerald-500",
  },
]

const benefits = [
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Process transactions and updates in real-time",
  },
  {
    icon: Shield,
    title: "Secure & Reliable",
    description: "Bank-grade security with 99.9% uptime guarantee",
  },
  {
    icon: Clock,
    title: "24/7 Support",
    description: "Round-the-clock customer support and monitoring",
  },
]

const slideshowTitles = [
  "Master Your Inventory & Sales Operations",
  "Streamline Stock Management & Analytics",
  "Transform Your Business Operations",
]

export default function Home() {
  const [currentTitleIndex, setCurrentTitleIndex] = useState(0)
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTitleIndex((prev) => (prev + 1) % slideshowTitles.length)
    }, 3000) // Change every 3 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <main className="min-h-screen bg-gradient-to-br from-[#EDF2FF]/20 to-white font-sans">
      {/* Hero Section */}
      <section className="bg-[url('/images/login/bg-grain.png')] bg-gradient-to-br from-[#032282] to-[#1a237e] h-full !mb-0 pb-48  text-white shadow-2xl relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/images/grids.png')] opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#032282]/5 to-[#032282]/10"></div>
        <AnimatedSection className="relative w-full h-auto flex items-center justify-center flex-col py-24 px-4 md:px-0">
          <motion.div
            className="py-3 px-7 bg-[#032282]/80 rounded-[100px] text-white border border-[#EDF2FF]/30"
            variants={fadeInUp}
          >
            <p className="flex items-center gap-2">
              <Package className="w-4 h-4" />
              Complete Stock & Sales Solution
            </p>
          </motion.div>

          <motion.div className="max-w-[900px] h-[200px] flex items-center justify-center">
            <motion.h1
              animate={{ opacity: 1, y: 0 }}
              className="text-white leading-[1.2] text-4xl md:text-5xl font-bold text-center bg-gradient-to-r from-white to-[#EDF2FF] bg-clip-text "
              exit={{ opacity: 0, y: -20 }}
              initial={{ opacity: 0, y: 20 }}
              key={currentTitleIndex}
              transition={{ duration: 0.8, ease: "easeInOut" }}
            >
              {slideshowTitles[currentTitleIndex]}
            </motion.h1>
          </motion.div>

          <motion.h5
            className="mt-[7px] text-[#EDF2FF] text-lg md:text-lg text-center leading-[26.04px] max-w-[700px]"
            variants={fadeInUp}
          >
            Streamline your business with comprehensive stock management, sales tracking, and financial tools. From
            inventory control to invoice financing, we&apos;ve got everything covered.
          </motion.h5>

          <motion.div className="flex flex-col md:flex-row items-center gap-4 mt-[44px]" variants={fadeInUp}>
            <LinkButton
              className="whitespace-nowrap text-sm text-[#032282] flex items-center gap-[13px] hover:scale-105 transition-transform"
              href={"/book-demo"}
              size="default"
              variant="white"
            >
              Start Free Trial
              <ArrowRight className="w-4 h-4" />
            </LinkButton>
            <Button
              className="whitespace-nowrap text-sm text-white bg-[#032282] flex items-center gap-[13px] hover:bg-[#1a237e] hover:scale-105 transition-all"
              size="default"
              variant="default"
              onClick={() => {
                setIsModalOpen(true);
              }}
            >
              Watch Demo
              <svg fill="none" height="18" viewBox="0 0 18 18" width="18" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M12.75 15.9375H5.25C2.5125 15.9375 0.9375 14.3625 0.9375 11.625V6.375C0.9375 3.6375 2.5125 2.0625 5.25 2.0625H12.75C15.4875 2.0625 17.0625 3.6375 17.0625 6.375V11.625C17.0625 14.3625 15.4875 15.9375 12.75 15.9375ZM5.25 3.1875C3.105 3.1875 2.0625 4.23 2.0625 6.375V11.625C2.0625 13.77 3.105 14.8125 5.25 14.8125H12.75C14.895 14.8125 15.9375 13.77 15.9375 11.625V6.375C15.9375 4.23 14.895 3.1875 12.75 3.1875H5.25Z"
                  fill="white"
                />
                <path
                  d="M9.00008 9.65247C8.37008 9.65247 7.73258 9.45748 7.24508 9.05998L4.89758 7.18498C4.65758 6.98998 4.61258 6.63748 4.80758 6.39748C5.00258 6.15748 5.35508 6.11248 5.59508 6.30748L7.94257 8.18248C8.51257 8.63998 9.48007 8.63998 10.0501 8.18248L12.3976 6.30748C12.6376 6.11248 12.9976 6.14998 13.1851 6.39748C13.3801 6.63748 13.3426 6.99748 13.0951 7.18498L10.7476 9.05998C10.2676 9.45748 9.63008 9.65247 9.00008 9.65247Z"
                  fill="white"
                />
              </svg>
            </Button>
          </motion.div>
        </AnimatedSection>
      </section>

      {/* Dashboard Preview */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className="w-full flex justify-center items-center -mt-56 z-50 relative"
        initial={{ opacity: 0, y: 50 }}
        transition={{ delay: 0.5 }}
      >
        <div className="w-full max-w-[1100px] px-4">
          <div className="bg-white rounded-2xl shadow-sm p-2">
            <Image
              alt="Stock and Sales Dashboard"
              className="w-full h-auto rounded-xl object-fill"
              height={600}
              src="/images/stocks/stock_landing.png"
              width={1000}
              priority
            />
          </div>
        </div>
      </motion.div>

      {/* Stock Management Section */}
      <AnimatedSection className="py-24 px-4 md:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <div className="inline-flex items-center gap-2 bg-[#EDF2FF] text-[#032282] px-4 py-2 rounded-full text-sm font-medium mb-4">
              <Package className="w-4 h-4" />
              Stock Management
            </div>
            <h2 className="text-4xl md:text-3xl font-bold text-gray-900 mb-6">Complete Inventory Control</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto font-sans">
              Take full control of your inventory with advanced tracking, management, and analytics tools designed for
              modern businesses.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {stockFeatures.map((feature, index) => (
              <motion.div
                className="bg-white rounded-2xl p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 group hover:-translate-y-2"
                key={feature.title}
                transition={{ delay: index * 0.1 }}
                variants={scaleIn}
              >
                <div className="bg-gradient-to-br from-[#032282] to-[#1a237e] w-16 h-16 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.features.map((item, idx) => (
                    <li className="flex items-center gap-2 text-sm text-gray-700" key={idx}>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      {item}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* Sales Features Section */}
      <AnimatedSection className="py-24 px-4 md:px-8 bg-gradient-to-br from-[#EDF2FF]/10 to-[#EDF2FF]/30">
        <div className="max-w-7xl mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <div className="inline-flex items-center gap-2 bg-[#EDF2FF] text-[#032282] px-4 py-2 rounded-full text-sm font-medium mb-4">
              <TrendingUp className="w-4 h-4" />
              Sales Management
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Comprehensive Sales Suite</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From transactions to financing, manage every aspect of your sales process with our integrated tools and
              features.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {salesFeatures.map((feature, index) => (
              <motion.div
                className="bg-white rounded-xl p-6 shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-100 group hover:-translate-y-1"
                key={feature.title}
                transition={{ delay: index * 0.05 }}
                variants={scaleIn}
              >
                <div
                  className={`${feature.color} w-12 h-12 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}
                >
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* Benefits Section */}
      <AnimatedSection className="py-24 px-4 md:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Why Choose Our Platform?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Built for modern businesses that demand reliability, speed, and comprehensive functionality.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                className="text-center group"
                key={benefit.title}
                transition={{ delay: index * 0.1 }}
                variants={fadeInUp}
              >
                <div className="bg-gradient-to-br from-[#032282] to-[#1a237e] w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <benefit.icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">{benefit.title}</h3>
                <p className="text-gray-600 leading-relaxed">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* CTA Section */}
      <AnimatedSection className="py-24 px-4 md:px-8 bg-gradient-to-br from-[#032282] to-[#1a237e] text-white">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Transform Your Business?</h2>
            <p className="text-xl text-[#EDF2FF] mb-8 leading-relaxed">
              Join thousands of businesses already using our platform to streamline their operations and boost growth.
            </p>
            <div className="flex flex-col md:flex-row items-center justify-center gap-4">
              <Button className="bg-white text-[#032282] hover:bg-gray-100 px-8 py-4 text-lg font-semibold rounded-xl">
                Start Your Free Trial
              </Button>
              <Button
                className="border-white text-white hover:bg-white hover:text-[#032282] px-8 py-4 text-lg font-semibold rounded-xl bg-transparent"
              // variant="outline"
              >
                Schedule Demo
              </Button>
            </div>
          </motion.div>
        </div>
      </AnimatedSection>

      <VideoModal isOpen={isModalOpen} videoId={"uIdJ-Fou0_U"} onClose={() => setIsModalOpen(false)} />
    </main>
  )
}
