"use client"
import React, { useEffect } from 'react'
import { <PERSON>ton, <PERSON>lientOnly, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/core";
import { DialogBody } from '@/components/core/Dialog2';

import { Dialog, DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/core/Dialog3';
//import AnnualSubPlanDashboard from '../AnnualSubPlanDashboard';
import { useModules } from '../../api/getModules';
import { Plan, SubScriptionModulePropTypes, useSubscriptionPlan } from '../../api/getSubscriptionPlan';
import { SmallSpinner } from '@/icons/core';
import { formatDuration } from '../../util';
import { ModuleProp } from '../../api/getSubscriptionCards';

interface UseBooleanStateControlProps {
    setShowUpgradePlan: React.Dispatch<React.SetStateAction<boolean>>
    showUpgradePlan: boolean;
    selectedModule: ModuleProp | null;
    setSelectedModule: React.Dispatch<React.SetStateAction<ModuleProp | null>>
    setSelectedPlans: React.Dispatch<React.SetStateAction<Map<number, Plan | null>>>
    selectedPlans: Map<number, Plan | null>
    setShowSummaryModal: React.Dispatch<React.SetStateAction<boolean>>
    setSelectedModules: React.Dispatch<React.SetStateAction<SubScriptionModulePropTypes[]>>
}

function SubscriptionPlan({
    setShowSummaryModal,
    selectedModule,
    setSelectedModules,
    selectedPlans,
    setSelectedPlans,
    setShowUpgradePlan,
    showUpgradePlan

}: UseBooleanStateControlProps) {
    const { data: modulesList } = useModules();
    const { data: subscriptionPlanList, isLoading: SubscriptionPlanLoading } = useSubscriptionPlan();
    useEffect(() => {
        // Reset selectedModule on load so that no modules are pre-selected


        // Set default selected plan as the first one in the list for each module
        if (subscriptionPlanList && subscriptionPlanList?.plans?.length > 0) {
            const defaultPlan = subscriptionPlanList.plans[0];

            // Initialize selected plans with the default plan for each module
            const initialSelectedPlans = new Map();
            modulesList?.modules?.forEach((module) => {
                initialSelectedPlans.set(module.id, defaultPlan);
            });
            setSelectedPlans(initialSelectedPlans);
        }
    }, [subscriptionPlanList, modulesList]); // Depend on both subscriptionPlanList and modulesList




    // select duration dropdown
    const handlePlanChange = (moduleId: number, planId: number) => {
        const plan = subscriptionPlanList?.plans?.find((plan) => plan.id === planId);
        setSelectedPlans((prevPlans) => {
            // Update the plan in the selectedPlans map
            const newSelectedPlans = new Map(prevPlans);
            newSelectedPlans.set(moduleId, plan || null);

            // Update the selected module with the new plan
            setSelectedModules((prevModules) => {
                return prevModules.map((module) => {
                    if (module.id === moduleId) {
                        return { ...module, selectedPlan: plan || null }; // Update the module's selectedPlan
                    }
                    return module;
                });
            });

            return newSelectedPlans;
        });
    };


    const handleSubscribeModule = (module: ModuleProp) => {
        // Ensure you're adding a full object of type SubScriptionModulePropTypes
        const selectedPlan = selectedPlans.get(Number(module.module_id));

        setSelectedModules(() => [

            {
                ...module, // Spread existing properties
                selectedPlan, // Add selectedPlan
                id: module?.module_id,
                code: module?.module_code, // Include other required fields
                name: module?.module_name,
                description: "",
                module_plan: module?.module_plan, // If necessary, provide a default value
                is_auto_renewal: module?.is_auto_renewal, // or use a default value
                promotional_offer: module?.promotional_offer,
                updated_at: module?.updated_at,
            },
        ]);
    };


    return (
        <div className="rounded-xl">
            <ClientOnly>
                <Dialog open={showUpgradePlan} >
                    <DialogContent className="!overflow-hidden border border-[#407BFF] w-full">
                        <DialogHeader className="">
                            <DialogTitle>Subscription Plan</DialogTitle>
                            <DialogClose>
                                <button onClick={() => setShowUpgradePlan(false)}>close</button>
                            </DialogClose>
                        </DialogHeader>

                        <DialogBody className="bg-[#F2F5FF] w-full border-[0.01px] border-[#407BFF]">
                            {/* Content Section (Outside Blue Background) */}
                            <div className="mt-4 px-8">



                                <div className="w-full bg-[#F2F5FF]">
                                    <p className="font-normal text-[0.75rem] leading-[1.0231rem] mt-4">
                                        Choose the plan duration that best fits your business needs.
                                    </p>
                                    <div className="grid md:grid-cols-[1.5fr_1fr] gap-4">



                                        <div className="mt-4 border-[0.2px] border-[#333333] rounded-[10px] p-4"

                                        >
                                            <div className="flex items-start gap-4">

                                                <div>
                                                    <p className="font-normal text-[0.75rem] leading-[0.9762rem]">Plan Module</p>

                                                    <p className="mt-[0.1875rem] font-medium text-[1rem] leading-5 text-[#242424]">{

                                                        selectedModule?.module_name}</p>

                                                    <div className="">
                                                        <p className="text-[#4A4A68] text-[0.75rem] font-normal leading-[0.9762rem] mt-3">Amount</p>
                                                    </div>

                                                    {!SubscriptionPlanLoading && <span className="text-[#032180] font-extrabold text-sm">
                                                        {selectedPlans.get(selectedModule?.module_id as number)?.price || 'Select a plan'}/{formatDuration(Number(selectedPlans.get(Number(selectedModule?.module_id))?.duration_months) ?? 0)} {/* Show price of the selected plan */}
                                                    </span>}
                                                    {SubscriptionPlanLoading ?

                                                        <div className='flex  justify-center items-center '><SmallSpinner color='blue' /></div> : <div className="mt-4 w-full flex justify-between items-center gap-3 max-w-[250px]">
                                                            <Select value={selectedPlans.get(Number(selectedModule?.module_id))?.id?.toString() || ''} onValueChange={(planId) => handlePlanChange(Number(selectedModule?.module_id), Number(planId))}>
                                                                <SelectTrigger className="truncate max-w-[7rem]  text-xs border  h-8 border-primary" id="delivery_date">
                                                                    <SelectValue placeholder="Select a Plan" />
                                                                </SelectTrigger>
                                                                <SelectContent className="z-[99999]">
                                                                    {subscriptionPlanList?.plans?.map((sub) =>
                                                                        sub.is_active ? (
                                                                            <SelectItem key={sub?.id} value={String(sub?.id)}>
                                                                                {formatDuration(Number(sub?.duration_months ?? 0))}
                                                                            </SelectItem>
                                                                        ) : null
                                                                    )}
                                                                </SelectContent>
                                                            </Select>


                                                        </div>}
                                                </div>

                                                {/* Each module has its own Subscribe button */}
                                            </div>
                                        </div>


                                    </div>

                                    <div className="grid sm:grid-cols-2 gap-4 my-[3.3125rem]">
                                        <button
                                            className="rounded-md px-[5.75rem] py-3 border-[0.3px] border-[#032282] text-[#032282]"
                                            onClick={() => setShowUpgradePlan(false)}

                                        >
                                            Cancel
                                        </button>
                                        <Button
                                            className="px-[5.75rem] py-3"
                                            // disabled={selectedModule?.length === 0}
                                            onClick={() => {
                                                handleSubscribeModule(selectedModule as ModuleProp);
                                                // setShowUpgradePlan(false)
                                                setShowSummaryModal(true);
                                            }}
                                        >
                                            Subscribe
                                        </Button>
                                    </div>




                                </div>
                            </div>
                        </DialogBody>
                    </DialogContent>
                </Dialog>
            </ClientOnly>
        </div>
    )
}

export default SubscriptionPlan

