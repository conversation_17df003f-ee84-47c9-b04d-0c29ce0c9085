'use client';

import { usePathname, useSearchParams } from 'next/navigation';
import * as React from 'react';

import {
  LinkButton,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/core';
import { useBooleanStateControl, useRouteChangeEvent } from '@/hooks';

import { useCompanies } from '@/app/(dashboard)/stock/misc/api';
import { Coins } from 'lucide-react';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';


const pathsWithCompanySwitcher = [
  '/subscription',


];
const pathsWithoutCompanySwitcher = [
  '/instant-web/select-branchs',
  // '/instant-web/customize-website',
  // '/instant-web/customize-website/select-branch',
  // '/instant-web/set-company-branch-stock',
  // '/instant-web/product-details',
  // '/instant-web/product-details/customize-product',
  // '/instant-web/product-details/inventory',
  // '/instant-web/product-details/product-orders',
  // '/instant-web/product-details/post-purchase',
];

export function SubcriptionCompanySwitcher() {
  const { data: companiesResponse } = useCompanies();

  const pathname = usePathname();
  const params = useSearchParams();
  const companyId = params.get('company') || params.get('companyId');
  const companyName = params.get('companyName');

  const {
    state: isPopoverOpen,
    setState: setPopoverState,
    setFalse: closePopover,
  } = useBooleanStateControl();

  useRouteChangeEvent(() => closePopover());

  const hasSwitcher = React.useMemo(
    () =>
      pathsWithCompanySwitcher.some(page => pathname.includes(page)),
    [pathname]
  );

  const hasNoSwitcher = React.useMemo(
    () =>
      pathsWithoutCompanySwitcher.includes(pathname),
    [pathname]
  );

  const shouldShowSwitcher = hasSwitcher && !hasNoSwitcher;

  const { results: companies } = companiesResponse || {};
  const sortedCompanies = companies?.sort((a, b) => {
    if (a.default && !b.default) return -1; // a with default true comes before b with default false
    if (!a.default && b.default) return 1;  // b with default true comes before a with default false
    if (a.id === companyId) return -1;       // a with matching id comes before b
    if (b.id === companyId) return 1;        // b with matching id comes before a
    return 0;                               // No change in order for other cases
  });

  const companyNameAlt = companyName || companies?.find(company => company.id === companyId)?.company_name

  return !!companies && shouldShowSwitcher ? (
    <Popover open={isPopoverOpen} onOpenChange={setPopoverState}>
      <PopoverTrigger className="rounded-10 bg-white py-1 text-sm">
        <span className="rounded-lg bg-blue-700/10 px-3 py-1.5 text-xs text-main-solid md:bg-main-bg">
          Switch Company: <span className='uppercase font-semibold'>{companyNameAlt}</span>
        </span>

      </PopoverTrigger>
      <PopoverContent
        align="start"
        className="mt-2 w-full rounded-10 border-none p-0 pb-2 text-xs [@media(min-width:408px)]:max-w-[18.375rem]"
      >
        <div className='py-[11px] px-4 bg-[#EBF1FF] mt-3 flex items-center justify-between gap-2 w-full'>
          <p className='text-main-solid text-xxs'>Tap to <span className='font-bold'>change</span> default company</p>

          <LinkButton className='bg-[#DAE3FF] rounded-xl py-[5px] px-4 text-main-solid text-xxs' href={'/stock/set-company-branch-stock'}>Change</LinkButton>
        </div>
        <p className="px-4 pb-2.5 pt-4">Select company to change view.</p>
        <ul className="space-y-1 px-2">
          {sortedCompanies?.map(({ id, company_name, default: isDefaultCompany, token_balance_info }) => (
            <li key={id}>
              <LinkButton
                className="flex flex-col rounded-[.75rem] px-6 py-2.5 text-left text-label-text transition ease-in-out hover:bg-[#F8FAFF] !items-start"
                href={`${pathname}?companyId=${id}&companyName=${company_name || ''}`}
                size="lg"
                variant="white"
              >
                <div className='flex justify-between items-center gap-2 '>
                  <span>{company_name}</span>
                  {isDefaultCompany && <span className='bg-main-bg text-main-solid py-1 px-2 rounded-xl'>default</span>}

                </div>
                {/* Token Balance Display */}
                {token_balance_info && (
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center gap-1">
                      <Coins className="h-3 w-3 text-[#032282]" />
                      <span className="text-xs font-medium text-[#032282]">
                        {addCommasToNumber(token_balance_info.current_balance)} tokens
                      </span>
                    </div>
                    <span className="text-xxs text-gray-500">
                      ({convertNumberToNaira(token_balance_info.current_balance * 10)})
                    </span>

                  </div>
                )}
              </LinkButton>
            </li>
          ))}
        </ul>
      </PopoverContent>
    </Popover>
  ) : null;
}
