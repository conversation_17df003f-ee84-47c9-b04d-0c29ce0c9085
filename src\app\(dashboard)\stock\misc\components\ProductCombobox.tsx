'use client';

import * as React from 'react';

import {
  Button,
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/core';
import { useDebounce } from '@/hooks';
import { cn } from '@/utils/classNames';

import { useGetProductsByCompanyAndCategory } from '../api';
import { StockCategories, Product } from '../types';

interface CategoriesComboboxProps {
  id?: string;
  defaultCategoryId?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onProductSelect?: (selectedProduct: Product | null) => void;
  initialCategoriesResponse?: StockCategories;
  companyId?: string;
  categoryId?: string;
}

export function ProductCombobox({
  id,
  companyId,
  categoryId,
  placeholder,
  value: rhfValue,
  onChange,
  onProductSelect,
}: CategoriesComboboxProps) {
  const [open, setOpen] = React.useState(false);

  const [selectedCategory, setSelectedCategory] = React.useState<{
    label: string;
    value: string;
  } | null>();

  const [value, setValue] = React.useState('');
  const [search, setSearch] = React.useState('');

  const debouncedSearch = useDebounce(search, 500);

  const fetchOptions = {
    companyId: companyId || '',
    categoryId: categoryId || '',
    pageIndex: 1,
    pageSize: 100,
    search: debouncedSearch,
  };

  //   const { data: categoriesResponse, isFetching: areUsersFetching } =
  //     useGetStockCategoriesByCompany(fetchOptions, initialCategoriesResponse);

  //   const { categories } = categoriesResponse?.data || {};

  const { data: products, isFetching: areProductsFetching } =
    useGetProductsByCompanyAndCategory(fetchOptions);

  const productsList = products?.data.products;

  const options = productsList?.map(({ id, name }) => {
    return {
      value: id,
      label: `${name || 'Name unavailable'}`,
    };
  });

  const selectedOption = options?.find(option => option.value === value);

  const prevCategoryIdRef = React.useRef(categoryId);

  React.useEffect(() => {
    if (prevCategoryIdRef.current !== categoryId) {
      setValue(''); // Clear the subcategory value when categoryId actually changes
      onChange?.(''); // Also update the parent state if onChange is provided
      prevCategoryIdRef.current = categoryId;
    }
  }, [categoryId, onChange]);

  return (
    <>
      <Popover open={open} modal onOpenChange={setOpen}>
        <PopoverTrigger disabled={areProductsFetching} asChild>
          <Button
            className={cn(
              'flex h-10 w-full items-center justify-between gap-2 rounded-md border border-[#E8E8E8]/70 bg-white px-3 py-2 text-left text-xs transition duration-300 hover:opacity-100 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 active:scale-100 disabled:cursor-not-allowed disabled:opacity-50',
              !(value || rhfValue) && 'text-muted-foreground'
            )}
            id={id}
            type="button"
            variant="unstyled"
          >
            {value || selectedCategory?.value || rhfValue ? (
              <span className="flex items-center gap-2">
                <span className="min-w-0 max-w-[2rem] truncate capitalize [@media(min-width:356px)]:max-w-[8rem]">
                  {selectedOption?.label || value}
                </span>
              </span>
            ) : (
              <span className="font-normal text-input-placeholder">
                {placeholder || 'Select an option'}
              </span>
            )}

            <span>
              <svg
                fill="none"
                height={7}
                viewBox="0 0 12 7"
                width={12}
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  className="fill-label-text"
                  clipRule="evenodd"
                  d="M8.357 5.522a3.333 3.333 0 0 1-4.581.126l-.133-.126L.41 2.089A.833.833 0 0 1 1.51.84l.078.07L4.82 4.342c.617.617 1.597.65 2.251.098l.106-.098L10.411.91a.833.833 0 0 1 1.248 1.1l-.07.079-3.232 3.433Z"
                  fillRule="evenodd"
                />
              </svg>
            </span>
          </Button>
        </PopoverTrigger>

        <PopoverContent align="end" className="max-h-none p-0">
          <Command
            /* 🔍  make cmdk search the *label* instead of the UUID  */
            filter={(value, search) => {
              // `options` is in the same scope as this component
              const label = options?.find(o => o.value === value)?.label ?? '';
              return label.toLowerCase().includes(search.toLowerCase()) ? 1 : 0;
            }}
          >
            <CommandInput
              isLoading={areProductsFetching}
              placeholder={'Search for new product'}
              value={search}
              onValueChange={setSearch}
            />
            <CommandEmpty className="p-1">
              <p className="px-2 py-3 pl-6 pt-1 text-xs">No matches found.</p>
            </CommandEmpty>
            <CommandList className="max-h-60 overflow-y-auto">
              {options?.map(option => {
                return (
                  <CommandItem
                    className="py-3 text-xs"
                    key={option.value}
                    value={option.value} // changed from option.label
                    onSelect={currentValue => {
                      setValue(currentValue === value ? '' : currentValue);
                      const category = options.find(option => option.value === currentValue);
                      setSelectedCategory(category);

                      onChange?.(
                        category?.value === value ? '' : category?.value || ''
                      );
                      setOpen(false);
                      const selectedProduct = productsList?.find(p => p.id === currentValue);
                      onProductSelect?.(selectedProduct || null);
                    }}
                  >
                    <svg
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === option.value ? 'opacity-100' : 'opacity-0'
                      )}
                      fill="none"
                      height={16}
                      viewBox="0 0 16 16"
                      width={16}
                      xmlns="http://www.w3.org/2000/svg"
                      aria-hidden
                    >
                      <path
                        d="m14.53 5.03-8 8a.751.751 0 0 1-1.062 0l-3.5-3.5a.751.751 0 1 1 1.063-1.062L6 11.438l7.47-7.469a.751.751 0 0 1 1.062 1.063l-.001-.002Z"
                        fill="#4E4E4E"
                      />
                    </svg>

                    <span className="relative flex items-center justify-between gap-3.5">
                      <span className="relative flex flex-col">
                        <span
                          className={cn(
                            'text-xxs capitalize text-[#646464] text-opacity-80'
                          )}
                        >
                          {option.label.toLowerCase()}
                        </span>
                      </span>
                    </span>
                  </CommandItem>
                );
              })}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  );
}
