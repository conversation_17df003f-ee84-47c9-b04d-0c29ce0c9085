'use client';

import React, { useEffect } from 'react';

import { StockOutDashboardStats, StockOutTable, ViewBranchesDialog } from '../misc/components';
import { useCompaniesList } from '../../instant-web/misc/api';
import { LoaderModal } from '@/components/core';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';

const Page = () => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const branch = searchParams.get('branch') || '';
  const company = searchParams.get('company') || '';

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  useEffect(() => {
    if (!company && salesUserDetails?.data) {
      router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])

  return (
    <>
      <LoaderModal isOpen={isCompanyLoading || isDefaultLoading || !company} />
      {branch ?
        <div className="md:px-7 md:py-3 lg:px-11">
          <StockOutDashboardStats
          // branch={branch}
          // company={company}
          // stockOutDashboardStats={stockOutDashboardStats}
          />
          <div className="requisitions__table mt-4">
            <StockOutTable />
          </div>
        </div>
        :
        <ViewBranchesDialog company={company}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          companyList={companies?.results} link={'/stock/stock-out'} />
      }
    </>
  );
};

export default Page;
