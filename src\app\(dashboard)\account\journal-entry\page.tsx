'use client'

import { Button, DataTable2, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, LinkButton } from '@/components/core';
import { ColumnDef } from '@tanstack/react-table';
import React, { useState } from 'react'
import { JournalEntry, UseGetJournalEntry } from '../misc/api/getJournalEntry';
import { useSearchParams } from 'next/navigation';
import ExportChartModal from '../misc/components/modals/CreateExportChart';
import { convertNumberToNaira } from '@/utils/currency';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';
import DebounceInput from '../../instant-web/util/DebounceInput';
import { UseGetAllAccount } from '../misc/api/getAllAccount';


const Page = () => {
    const [globalFilter, setGlobalFilter] = useState<string>("");

    const { data: salesUserDetails } = useDefaultCompanyBranch();

    React.useEffect(() => {
        //
    }, [])

    const params = useSearchParams();

    const companyId = params.get('company') || salesUserDetails?.data.company_id;

    const { data: accounts, isFetching, isLoading } = UseGetJournalEntry(companyId as string)

    const { data: getAccType } = UseGetAllAccount(companyId as string)

    const all_account_type = getAccType?.map(({ id, name }: { id: string; name: string }) => ({
        id,
        name,
    }));



    const [isExportJournal, setExportJournal] = useState(false)

    const columns: ColumnDef<JournalEntry>[] = [
        {
            accessorKey: 'sn',
            header: 'S/N',
            cell: ({ row }) => <div>{row.index + 1}</div>,
        },

        {
            accessorKey: '',
            header: 'Accounts',
            cell: ({ row }) => {
                const allItems = row?.original?.lines || []
                const itemsToDisplay = allItems?.slice(0, 2) || '';

                return (
                    <div>
                        {itemsToDisplay?.map(({ account }, index) => (
                            <span key={index}>

                                {
                                    all_account_type?.find((type) => type.id == account)?.name
                                }
                                {
                                    itemsToDisplay.length > index + 1 && ", "
                                }
                            </span>
                        ))}
                        {
                            allItems.length > 2 && `+ ${allItems.length - 2} more`
                        }

                    </div>
                )
            },
        },

        {
            accessorKey: 'credit_amount',
            header: 'Credit Amount',
            cell: ({ row }) => {
                const allcreditamt = row?.original?.lines || []
                const CrItemsToDisplay = allcreditamt?.slice(0, 2) || '';

                return (
                    <div>
                        {CrItemsToDisplay?.map(({ credit_amount }, index) => (
                            <span key={index}>
                                {convertNumberToNaira(credit_amount)}
                                {
                                    CrItemsToDisplay.length > index + 1 && ", "
                                }
                            </span>
                        ))}
                        {
                            allcreditamt.length > 2 && `+ ${allcreditamt.length - 2} more`
                        }

                    </div>
                )
            }
        },

        {
            accessorKey: 'debit_amount',
            header: 'Debit Amount',
            cell: ({ row }) => {
                const allDrAmt = row?.original?.lines || []
                const DrItemsToDisplay = allDrAmt?.slice(0, 2) || '';

                return (
                    <div>
                        {DrItemsToDisplay?.map(({ debit_amount }, index) => (
                            <span key={index}>
                                {convertNumberToNaira(debit_amount)}
                                {
                                    DrItemsToDisplay.length > index + 1 && ", "
                                }
                            </span>
                        ))}
                        {
                            allDrAmt.length > 2 && `+ ${allDrAmt.length - 2} more`
                        }

                    </div>
                )
            }
        },

        {
            accessorKey: 'journal_number',
            header: 'Journal Number',
            cell: ({ row }) => {
                return (
                    <div>{row?.original?.journal_number}</div>
                )
            }
        },

        {
            accessorKey: 'Reference',
            header: 'Reference',
            cell: ({ row }) => {
                return (
                    <div>{row?.original?.references}</div>
                )
            }
        },

        {
            accessorKey: 'status',
            header: 'Status',
            cell: ({ row }) => {
                const isDraft = row.original.is_draft;
                return (
                    <span className={`px-2 py-1 rounded text-xs font-medium ${isDraft ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                        {isDraft ? 'Draft' : 'Published'}
                    </span>
                );
            },
        },

    ];


    const filteredData = accounts?.filter((entry) => {
        const searchTerm = (globalFilter || '').toLowerCase();
        return (
            (entry.journal_number?.toLowerCase() ?? '').includes(searchTerm) ||
            (entry.transaction_type?.toLowerCase() ?? '').includes(searchTerm) ||
            (entry.references?.toLowerCase() ?? '').includes(searchTerm) ||
            entry.lines?.some(({ customer, account }) =>
                (customer?.toLowerCase() ?? '').includes(searchTerm) ||
                (all_account_type?.find((type) => type.id === account)?.name?.toLowerCase() ?? '').includes(searchTerm)
            )
        );
    });

    return (
        <main className='mt-1'>
            <div className='lg:flex lg:justify-between px-10 bg-white pt-8 pb-5'>
                <div className='flex items-center gap-3 text-[#242424] xl:pb-5'>
                    <h1 className='text-2xl font-medium font-sans'>Journal Entry</h1>
                    <DropdownMenu>
                        <DropdownMenuTrigger className='bg-[#F2F5FF] px-2 py-1.5 flex items-center gap-1.5'>All Journals
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem>account</DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
                <div className='lg:flex gap-2 mt-2 items-center'>
                    <div>
                        <DebounceInput
                            className='bg-white rounded-md mt-2 '
                            value={globalFilter ?? ''}
                            onChange={(value) => setGlobalFilter(String(value))}
                        />
                    </div>
                    <div className='flex gap-2 mt-3 lg:mt-0'>
                        <div>
                            <Button
                                className='py-2 border-[#7E92D0] border-[.0187rem] bg-transparent text-[#667085] whitespace-nowrap'
                                onClick={() => setExportJournal(true)}
                            >Upload Journal</Button>
                        </div>
                        <div>
                            <LinkButton
                                className=' flex items-center gap-1.5 text-sm whitespace-nowrap'
                                href={`/account/create-journal`}
                            >New Journal
                            </LinkButton>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <DataTable2
                    columns={columns}
                    isFetching={isFetching}
                    isLoading={isLoading}
                    pageCount={1}
                    pageIndex={0}
                    pageSize={100}
                    rows={filteredData || []}
                    // eslint-disable-next-line @typescript-eslint/no-empty-function
                    setPagination={() => { }}
                />
            </div>

            {
                isExportJournal &&
                <ExportChartModal
                    heading='Import Journals'
                    isExportChartModal={isExportJournal}
                    setIsExportChartModal={setExportJournal}
                />
            }
            {/* {
                isJournalDetailsModals &&
                <JournalDetailsModal
                    heading='Journal Details'
                    isJournalDetailsModal={isJournalDetailsModals}
                    journal_id={JournaId}
                    setJournalDetailsModal={setJournalDetailsModal}

                />
            } */}
        </main>
    )
}

export default Page