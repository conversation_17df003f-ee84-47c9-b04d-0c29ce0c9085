const TOKEN_STORAGE_PREFIX = 'MANAGEMENT_PAYBOX';

export const tokenStorage = {
  getToken: () => {
    if (typeof window === 'undefined') return null;
    const token = window.localStorage.getItem(`${TOKEN_STORAGE_PREFIX}TOKEN`);
    return token ? JSON.parse(token) : null;
  },

  setToken: (token: string) => {
    if (typeof window === 'undefined') return;
    window.localStorage.setItem(
      `${TOKEN_STORAGE_PREFIX}TOKEN`,
      JSON.stringify(token),
    );
  },

  clearToken: () => {
    if (typeof window === 'undefined') return;
    window.localStorage.removeItem(`${TOKEN_STORAGE_PREFIX}TOKEN`);
  },
};

const REFRESH_TOKEN_STORAGE_PREFIX = 'MANAGEMENT_REFRESH_PAYBOX';

export const refreshTokenStorage = {
  getToken: () => {
    if (typeof window === 'undefined') return null;
    const token = window.localStorage.getItem(`${REFRESH_TOKEN_STORAGE_PREFIX}TOKEN`);
    return token ? JSON.parse(token) : null;
  },

  setToken: (token: string) => {
    if (typeof window === 'undefined') return;
    window.localStorage.setItem(
      `${REFRESH_TOKEN_STORAGE_PREFIX}TOKEN`,
      JSON.stringify(token),
    );
  },

  clearToken: () => {
    if (typeof window === 'undefined') return;
    window.localStorage.removeItem(`${REFRESH_TOKEN_STORAGE_PREFIX}TOKEN`);
  },
};
