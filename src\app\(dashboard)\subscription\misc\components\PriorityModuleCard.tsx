'use client';
import React from 'react';
import { Button, Checkbox, Input } from '@/components/core';
import PayboxModuleIcon from '@/components/icons/PayboxModuleIcon';
import { SubScriptionModulePropTypes, Plan } from '../api/getSubscriptionPlan';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';

interface PriorityModuleCardProps {
  module: SubScriptionModulePropTypes;
  isSelected: boolean;
  selectedPlan: Plan | null;
  onModuleSelection: (module: SubScriptionModulePropTypes) => void;
  onSubscribe: (module: SubScriptionModulePropTypes) => void;
  isPriority?: boolean;
  isPromotional?: boolean;
  disabled?: boolean;
  tokenUnits: number;
  onTokenUnitsChange: (moduleId: number, units: number) => void;
}

const PriorityModuleCard: React.FC<PriorityModuleCardProps> = ({
  module,
  isSelected,
  // selectedPlan,
  onModuleSelection,
  onSubscribe,
  isPriority = false,
  isPromotional = false,
  disabled = false,
  tokenUnits,
  onTokenUnitsChange
}) => {
  const _formatDuration = (months: number) => {
    if (months === 1) return 'month';
    if (months === 12) return 'year';
    return `${months} months`;
  };

  const handleTokenUnitsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    if (value >= 0) {
      onTokenUnitsChange(module.id, value);
    }
  };

  const totalAmount = tokenUnits * 10; // 1 token = ₦10

  const getPriorityBadge = () => {
    if (isPriority) {
      return (
        <div className="absolute -top-2 -right-2 bg-[#032282] text-white text-xxs font-medium px-2 py-1 rounded-md shadow-sm">
          PRIORITY
        </div>
      );
    }
    return null;
  };

  const getPromotionalBadge = () => {
    if (isPromotional) {
      return (
        <div className="absolute -top-2 -left-2 bg-green-600 text-white text-xs font-medium px-2 py-1 rounded-md shadow-sm">
          FREE
        </div>
      );
    }
    return null;
  };

  const cardClasses = `
    relative border rounded-lg p-6 transition-all duration-200 hover:shadow-md bg-white
    ${isPriority
      ? 'border-[#0323825f] bg-[#032282] bg-opacity-5 shadow-sm'
      : 'border-gray-200 bg-white hover:border-gray-300'
    }
    ${isSelected ? ' ring-opacity-30' : ''}
    ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
  `;

  return (
    <div className={cardClasses}>
      {getPriorityBadge()}
      {getPromotionalBadge()}

      {/* Header with checkbox and title */}
      <div className="flex items-start gap-3 mb-4">
        <Checkbox
          checked={isSelected}
          className={`w-4 h-4 border-2 rounded-md mt-1 ${isPriority ? 'border-[#032282]' : 'border-gray-400'
            }`}
          disabled={disabled}
          id={module.id.toString()}
          onCheckedChange={() => onModuleSelection(module)}
        />
        <div className="flex-1">
          <label
            className="font-bold text-lg cursor-pointer block"
            htmlFor={module.id.toString()}
          >
            <p className={`${isPriority ? 'text-blue-700' : 'text-gray-800'} leading-tight`}>
              {module.name}
            </p>
          </label>
          {isPriority && (
            <p className="text-blue-600 text-sm font-medium mt-1">
              🚀 Recommended for business growth
            </p>
          )}
        </div>
      </div>

      {/* Features list */}
      <div className="mb-6">
        <p className={`font-semibold text-sm mb-3 ${isPriority ? 'text-blue-700' : 'text-gray-600'}`}>
          Access to:
        </p>
        <div className="space-y-2">
          {module.description?.split(',')?.map((item, idx) => (
            <div className="flex items-start gap-3" key={idx}>
              <span className="mt-1 shrink-0">
                <PayboxModuleIcon />
              </span>
              <span className="text-sm font-medium text-gray-700 leading-relaxed">
                {item.trim()}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Token Input section */}
      <div className={`p-4 rounded-lg mb-4 border ${isPriority
        ? 'bg-[#fdfdfd] bg-opacity-5 border-[#03238271] border-opacity-20'
        : 'bg-white border-gray-50'
        }`}>
        <div className="space-y-3">
          <div>
            <label className="block text-[13px] font-medium text-gray-700 mb-2">
              Number of Token Units
            </label>
            <Input
              className="w-full text-center text-sm font-medium border border-gray-300 rounded-lg focus:border-[#032282] focus:ring-1 focus:ring-[#032282]"
              disabled={disabled || !isSelected}
              min="0"
              placeholder={isSelected ? "Enter token units" : "Select module first"}
              type="number"
              value={tokenUnits || ''}
              onChange={handleTokenUnitsChange}
            />
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600 mb-1">Total Amount for this module</p>
            <p className={`text-xl font-bold ${isPriority ? 'text-[#032282]' : 'text-gray-800'}`}>
              {convertNumberToNaira(totalAmount)}
            </p>
            <p className="text-xs text-gray-500">
              {addCommasToNumber(tokenUnits)} tokens × ₦10 each
            </p>
          </div>
        </div>
      </div>

      {/* Action button */}
      <Button
        className={`w-full py-3 font-medium transition-all duration-200 ${isPriority
          ? 'bg-[#032282] hover:bg-[#032282]/90 text-white disabled:bg-gray-400'
          : 'bg-[#032282] hover:bg-[#032282]/90 text-white disabled:bg-gray-400'
          }`}
        disabled={disabled || !isSelected || tokenUnits <= 0}
        onClick={() => onSubscribe(module)}
      >
        {!isSelected ? 'Select Module First' : tokenUnits <= 0 ? 'Enter Token Units' : isPriority ? 'Subscribe Now' : 'Subscribe'}
      </Button>

      {/* Additional info for priority modules */}
      {isPriority && (
        <div className="mt-3 text-center">
          <p className="text-xs text-[#032282] font-medium">
            Recommended for business growth
          </p>
        </div>
      )}
    </div>
  );
};

export default PriorityModuleCard;
