'use client';
import React from 'react';
import { But<PERSON>, Checkbox } from '@/components/core';
import PayboxModuleIcon from '@/components/icons/PayboxModuleIcon';
import { SubScriptionModulePropTypes, Plan } from '../api/getSubscriptionPlan';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';

interface PriorityModuleCardProps {
  module: SubScriptionModulePropTypes;
  isSelected: boolean;
  selectedPlan: Plan | null;
  onModuleSelection: (module: SubScriptionModulePropTypes) => void;
  onSubscribe: (module: SubScriptionModulePropTypes) => void;
  isPriority?: boolean;
  isPromotional?: boolean;
  disabled?: boolean;
}

const PriorityModuleCard: React.FC<PriorityModuleCardProps> = ({
  module,
  isSelected,
  selectedPlan,
  onModuleSelection,
  onSubscribe,
  isPriority = false,
  isPromotional = false,
  disabled = false
}) => {
  const formatDuration = (months: number) => {
    if (months === 1) return 'month';
    if (months === 12) return 'year';
    return `${months} months`;
  };

  const getPriorityBadge = () => {
    if (isPriority) {
      return (
        <div className="absolute -top-2 -right-2 bg-[#032282] text-white text-xs font-medium px-2 py-1 rounded-md shadow-sm">
          PRIORITY
        </div>
      );
    }
    return null;
  };

  const getPromotionalBadge = () => {
    if (isPromotional) {
      return (
        <div className="absolute -top-2 -left-2 bg-green-600 text-white text-xs font-medium px-2 py-1 rounded-md shadow-sm">
          FREE
        </div>
      );
    }
    return null;
  };

  const cardClasses = `
    relative border rounded-lg p-6 transition-all duration-200 hover:shadow-md
    ${isPriority
      ? 'border-[#032282] bg-[#032282] bg-opacity-5 shadow-sm'
      : 'border-gray-200 bg-white hover:border-gray-300'
    }
    ${isSelected ? 'ring-2 ring-[#032282] ring-opacity-30' : ''}
    ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
  `;

  return (
    <div className={cardClasses}>
      {getPriorityBadge()}
      {getPromotionalBadge()}

      {/* Header with checkbox and title */}
      <div className="flex items-start gap-3 mb-4">
        <Checkbox
          checked={isSelected}
          className={`w-5 h-5 border-2 rounded-md mt-1 ${isPriority ? 'border-blue-500' : 'border-gray-400'
            }`}
          id={module.id.toString()}
          onCheckedChange={() => onModuleSelection(module)}
          disabled={disabled}
        />
        <div className="flex-1">
          <label
            className="font-bold text-lg cursor-pointer block"
            htmlFor={module.id.toString()}
          >
            <p className={`${isPriority ? 'text-blue-700' : 'text-gray-800'} leading-tight`}>
              {module.name}
            </p>
          </label>
          {isPriority && (
            <p className="text-blue-600 text-sm font-medium mt-1">
              🚀 Recommended for business growth
            </p>
          )}
        </div>
      </div>

      {/* Features list */}
      <div className="mb-6">
        <p className={`font-semibold text-sm mb-3 ${isPriority ? 'text-blue-700' : 'text-gray-600'}`}>
          Access to:
        </p>
        <div className="space-y-2">
          {module.description?.split(',')?.map((item, idx) => (
            <div className="flex items-start gap-3" key={idx}>
              <span className="mt-1 flex-shrink-0">
                <PayboxModuleIcon />
              </span>
              <span className="text-sm font-medium text-gray-700 leading-relaxed">
                {item.trim()}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Pricing section */}
      <div className={`p-4 rounded-lg mb-4 border ${isPriority
        ? 'bg-[#032282] bg-opacity-5 border-[#032282] border-opacity-20'
        : 'bg-gray-50 border-gray-200'
        }`}>
        <div className="text-center">
          <p className="text-sm font-medium text-gray-600 mb-1">Token-based pricing</p>
          <div className="flex items-center justify-center gap-2">
            <span className={`text-xl font-bold ${isPriority ? 'text-[#032282]' : 'text-gray-800'}`}>
              {selectedPlan?.price ? convertNumberToNaira(selectedPlan.price) : 'Select plan'}
            </span>
            {selectedPlan?.duration_months && (
              <span className="text-sm text-gray-500">
                /{formatDuration(selectedPlan.duration_months)}
              </span>
            )}
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Pay with tokens (1 token = ₦10)
          </p>
        </div>
      </div>

      {/* Action button */}
      <Button
        onClick={() => onSubscribe(module)}
        disabled={disabled}
        className={`w-full py-3 font-medium transition-all duration-200 ${isPriority
          ? 'bg-[#032282] hover:bg-[#032282]/90 text-white'
          : 'bg-[#032282] hover:bg-[#032282]/90 text-white'
          }`}
      >
        {isPriority ? 'Subscribe Now' : 'Subscribe'}
      </Button>

      {/* Additional info for priority modules */}
      {isPriority && (
        <div className="mt-3 text-center">
          <p className="text-xs text-[#032282] font-medium">
            Recommended for business growth
          </p>
        </div>
      )}
    </div>
  );
};

export default PriorityModuleCard;
