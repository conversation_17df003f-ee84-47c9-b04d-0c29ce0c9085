'use client';
import React from 'react';
import { But<PERSON>, Checkbox } from '@/components/core';
import PayboxModuleIcon from '@/components/icons/PayboxModuleIcon';
import { SubScriptionModulePropTypes, Plan } from '../api/getSubscriptionPlan';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';

interface PriorityModuleCardProps {
  module: SubScriptionModulePropTypes;
  isSelected: boolean;
  selectedPlan: Plan | null;
  onModuleSelection: (module: SubScriptionModulePropTypes) => void;
  onSubscribe: (module: SubScriptionModulePropTypes) => void;
  isPriority?: boolean;
  isPromotional?: boolean;
  disabled?: boolean;
}

const PriorityModuleCard: React.FC<PriorityModuleCardProps> = ({
  module,
  isSelected,
  selectedPlan,
  onModuleSelection,
  onSubscribe,
  isPriority = false,
  isPromotional = false,
  disabled = false
}) => {
  const formatDuration = (months: number) => {
    if (months === 1) return 'month';
    if (months === 12) return 'year';
    return `${months} months`;
  };

  const getPriorityBadge = () => {
    if (isPriority) {
      return (
        <div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg transform rotate-12">
          ⭐ PRIORITY
        </div>
      );
    }
    return null;
  };

  const getPromotionalBadge = () => {
    if (isPromotional) {
      return (
        <div className="absolute -top-2 -left-2 bg-gradient-to-r from-emerald-400 to-teal-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg transform -rotate-12">
          🎁 BONUS
        </div>
      );
    }
    return null;
  };

  const cardClasses = `
    relative border-2 rounded-xl p-6 transition-all duration-300 transform hover:scale-105 hover:shadow-xl
    ${isPriority 
      ? 'border-gradient-to-r from-blue-500 to-purple-600 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 shadow-lg' 
      : 'border-gray-200 bg-white hover:border-blue-300'
    }
    ${isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
    ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
  `;

  return (
    <div className={cardClasses}>
      {getPriorityBadge()}
      {getPromotionalBadge()}
      
      {/* Header with checkbox and title */}
      <div className="flex items-start gap-3 mb-4">
        <Checkbox
          checked={isSelected}
          className={`w-5 h-5 border-2 rounded-md mt-1 ${
            isPriority ? 'border-blue-500' : 'border-gray-400'
          }`}
          id={module.id.toString()}
          onCheckedChange={() => onModuleSelection(module)}
          disabled={disabled}
        />
        <div className="flex-1">
          <label 
            className="font-bold text-lg cursor-pointer block" 
            htmlFor={module.id.toString()}
          >
            <p className={`${isPriority ? 'text-blue-700' : 'text-gray-800'} leading-tight`}>
              {module.name}
            </p>
          </label>
          {isPriority && (
            <p className="text-blue-600 text-sm font-medium mt-1">
              🚀 Recommended for business growth
            </p>
          )}
        </div>
      </div>

      {/* Features list */}
      <div className="mb-6">
        <p className={`font-semibold text-sm mb-3 ${isPriority ? 'text-blue-700' : 'text-gray-600'}`}>
          Access to:
        </p>
        <div className="space-y-2">
          {module.description?.split(',')?.map((item, idx) => (
            <div className="flex items-start gap-3" key={idx}>
              <span className="mt-1 flex-shrink-0">
                <PayboxModuleIcon />
              </span>
              <span className="text-sm font-medium text-gray-700 leading-relaxed">
                {item.trim()}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Pricing section */}
      <div className={`p-4 rounded-lg mb-4 ${
        isPriority 
          ? 'bg-gradient-to-r from-blue-100 to-purple-100 border border-blue-200' 
          : 'bg-gray-50 border border-gray-200'
      }`}>
        <div className="text-center">
          <p className="text-sm font-medium text-gray-600 mb-1">Token-based pricing</p>
          <div className="flex items-center justify-center gap-2">
            <span className={`text-2xl font-bold ${isPriority ? 'text-blue-700' : 'text-gray-800'}`}>
              {selectedPlan?.price ? convertNumberToNaira(selectedPlan.price) : 'Select plan'}
            </span>
            {selectedPlan?.duration_months && (
              <span className="text-sm text-gray-500">
                /{formatDuration(selectedPlan.duration_months)}
              </span>
            )}
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Pay with tokens (1 token = ₦10)
          </p>
        </div>
      </div>

      {/* Action button */}
      <Button
        onClick={() => onSubscribe(module)}
        disabled={disabled}
        className={`w-full py-3 font-semibold transition-all duration-200 ${
          isPriority
            ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg'
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        }`}
      >
        {isPriority ? '🚀 Subscribe Now' : 'Subscribe'}
      </Button>

      {/* Additional info for priority modules */}
      {isPriority && (
        <div className="mt-3 text-center">
          <p className="text-xs text-blue-600 font-medium">
            ⚡ Most popular choice for growing businesses
          </p>
        </div>
      )}
    </div>
  );
};

export default PriorityModuleCard;
