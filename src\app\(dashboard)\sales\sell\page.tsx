/* eslint-disable @typescript-eslint/no-explicit-any */
'use client'

import React, { useRef, useEffect, useState, KeyboardEvent } from 'react';
// import { Eye, LockIcon } from 'lucide-react';

import { useCartStore } from '@/stores';
// import { CameraIcon, LogoutIcon, Paybox360, ScanIcon } from '@/components/icons';
// import { Button, ConfirmActionModal, LinkButton } from '@/components/ui';

// import { UseGetCategories, UseGetProducts } from './misc/api';
import { Summary, Cart, Checkout, ProductContainer, CategoriesList, SearchBox } from './misc/components';
// import { TProduct } from './misc/types';
// import { TFetchProductsOptions } from './misc/api/getProducts';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useGetPriceTags, useGetStockCategoriesByCompany, useGetStockSubCategoriesByCompany } from '../../stock/misc/api';
import { useGetBranchDetails, useGetPendingOrders, useGetSalesProductForSell } from '../misc/api';
import { SalesProductDto } from '../misc/types';
import { SmallSpinner } from '@/icons/core';
import { PendingOrdersModal } from '../misc/components/modals';
import { useBooleanStateControl, useDebounce } from '@/hooks';
import { Button, LoaderModal } from '@/components/core';
import { LinkButton, Button as Buttons } from '@/components/ui';
import { CameraIcon, Eye, LockIcon, ScanIcon, } from 'lucide-react';
import { useCompaniesList } from '../../instant-web/misc/api';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';
import { ViewBranchesDialog } from '../../stock/misc/components';
import { Itag, PriceTag } from './misc/components/Pricetag';
import useInfiniteScroll from 'react-infinite-scroll-hook';
import SubCategoriesList from './misc/components/ProductSubCategoriesList';
// import SubscriptionErrorModal from '../../spend-management/misc/components/subscription/ErrorSubscriptionModal';


const SalesDashboard: React.FC = () => {

  const {
    state: isPendingOrdersModalOpen,
    setTrue: openPendingOrdersModal,
    setState: setPendingOrdersModalState,
  } = useBooleanStateControl();



  const searchRef = useRef<HTMLInputElement>(null);
  const cartRef = useRef<HTMLDivElement>(null);
  const summaryRef = useRef<HTMLDivElement>(null);
  const checkoutRef = useRef<HTMLDivElement>(null);
  const productsContainerRef = useRef<HTMLUListElement>(null);
  const categoriesListRef = useRef<HTMLDivElement>(null);
  const subcategoriesListRef = useRef<HTMLDivElement>(null);
  const { active_cart, clear_cart, update_item_quantity, delete_item, add_item, remove_item } = useCartStore()
  const [paymentMethod, SetPaymentMethod] = useState("CASH")
  const [selectedCategory, setSelectedCategory] = useState<{ name: string, id: string } | null>(null);
  const [selectedSubCategory, setSelectedSubCategory] = useState<{ name: string, id: string } | null>(null);
  const [discountValue, setDiscountValue] = React.useState(0);
  const [overallPrice, setOverallPrice] = React.useState(0);
  const [selectedPriceTag, setSelectedPriceTag] = React.useState<Itag>({ name: "Selling Price", id: "" });
  const [additionalCharges, setAdditionalCharges] = React.useState(0);

  useEffect(() => {
    let lastKey = '';
    let lastKeyTime = 0;
    const pressedKeys = new Set<string>();


    const handleKeyDown = (event: KeyboardEvent) => {
      const currentTime = new Date().getTime();
      pressedKeys.add(event.key.toLowerCase());
      if (event.ctrlKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            searchRef.current?.focus();
            break;
          case 'c':
            event.preventDefault();
            cartRef.current?.focus();
            break;
          case 'd':
            event.preventDefault();
            productsContainerRef.current?.focus();
            break;
          case 'l':
            event.preventDefault();
            categoriesListRef.current?.focus();
            break;
          case 'm':
            event.preventDefault();
            checkoutRef.current?.focus();
            break;
        }
      }


      if (event.key === 'Escape') {
        event.preventDefault();
        (document.activeElement as HTMLElement)?.blur();
      }

      if (event.key === 'c' && lastKey === 'c' && currentTime - lastKeyTime < 1000) {
        event.preventDefault();
        cartRef.current?.focus()
      }

      if (event.key === 'p' && lastKey === 'p' && currentTime - lastKeyTime < 1000) {
        productsContainerRef.current?.focus();
      }

      if (event.key === 's' && lastKey === 's' && currentTime - lastKeyTime < 1000) {
        event.preventDefault();
        searchRef.current?.focus();
      }

      if (event.key === 'a' && lastKey === 'a' && currentTime - lastKeyTime < 1000) {
        // event.preventDefault();
        checkoutRef.current?.focus();
      }
      if (event.key === 'c' && lastKey === 'h' && currentTime - lastKeyTime < 1000) {
        // event.preventDefault();
        checkoutRef.current?.focus();
      }

      if (pressedKeys.has('c') && pressedKeys.has('h')) {
        checkoutRef.current?.focus();
      }



      lastKey = event.key;
      lastKeyTime = currentTime;
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      pressedKeys.delete(event.key.toLowerCase());
    };

    window.addEventListener('keydown', handleKeyDown as any);
    window.addEventListener('keyup', handleKeyUp as any);

    return () => {
      window.removeEventListener('keydown', handleKeyDown as any);
      window.removeEventListener('keyup', handleKeyUp as any);
    };
  }, []);

  const router = useRouter()
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const companyId = searchParams.get('company') as string;
  const branchId = searchParams.get('branch') as string;
  // const companyName = searchParams.get('companyName') as string;

  useEffect(() => {
    if (branchId) {
      active_cart.map(v => {
        if (v.branch !== branchId) {
          clear_cart()
        }
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [branchId])
  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();
  const [searchFilter, setSearchFilter] = useState("")
  const debouncedSearchFilter = useDebounce(searchFilter, 500);

  useEffect(() => {
    if (!companyId && salesUserDetails?.data) {
      router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])



  // const { data: categories, isLoading: isFetchingCategories } = UseGetCategories(company)
  const fetchCategoryOptions = {
    companyId,
    pageIndex: 1,
    pageSize: 100,
    // search: debouncedSearchFilter,
  };

  const { data: categories, isLoading: isFetchingCategories } =
    useGetStockCategoriesByCompany(
      fetchCategoryOptions
      //   initialCategoriesResponse
    );
  const { data: subcategories, isLoading: isFetchingSubCategories } =
    useGetStockSubCategoriesByCompany(
      fetchCategoryOptions
      //   initialCategoriesResponse
    );
  const { data: priceTags, } =
    useGetPriceTags(
      fetchCategoryOptions
      //   initialCategoriesResponse
    );

  const { data, isLoading: isFetchingProducts,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetSalesProductForSell({
    companyId,
    branchId,
    categoryId: selectedCategory?.id || '',
    subcategoryId: selectedSubCategory?.id || '',
    pageIndex: 1,
    pageSize: 100,
    price_tag: selectedPriceTag.name === "Selling Price" ? "" : selectedPriceTag.id,
    search: debouncedSearchFilter,
  });

  const [productsToDisplay, setProductsToDisplay] = useState(data?.pages.flatMap(page => page?.data?.stock as SalesProductDto[]) || []);
  useEffect(() => {
    if (!isFetchingProducts && data) {
      setProductsToDisplay(data?.pages.flatMap(page => page?.data?.stock as SalesProductDto[]) || [])
    }
  }, [isFetchingProducts, data])




  const handleCategorySelect = (category: { name: string, id: string }) => {
    setSelectedCategory({ ...category });

  }
  const handleSubCategorySelect = (category: { name: string, id: string }) => {
    setSelectedSubCategory({ ...category });

  }
  const fetchOptions = {
    companyId,
    branchId,
    pageIndex: 1,
    pageSize: 100,
    startDate: undefined,
    endDate: undefined,
    search: '',
  };
  const { data: pendingOrdersResponse, isLoading: isPendingOrdersLoading } =
    useGetPendingOrders(fetchOptions);

  const { count: numberOfPendingOrders } = pendingOrdersResponse?.data || {};

  const { data: branchDetails } = useGetBranchDetails(companyId, branchId);
  const branchInfo = branchDetails?.data?.branch;

  const closeModal = () => {
    // setSuccessModalState(false);
    // setConfirmPendModalState(false);
  };

  const [sentryRef] = useInfiniteScroll({
    loading: isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    onLoadMore: fetchNextPage,
    disabled: !!error,
    rootMargin: '0px 0px 800px 0px',
  });






  return (
    <>
      <LoaderModal isOpen={isDefaultLoading || !companyId} />
      {/* {
        (!salesUserDetails?.data?.is_subscription && !isDefaultLoading) && <SubscriptionErrorModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          message={"Access Forbidden"}
        />
      } */}

      {branchId ?
        <div className='grid grid-rows-[max-content,1fr] flex-col w-full h-screen overflow-hidden'>
          <header className='flex items-center justify-between gap-10 p-4 h-[8.5vh] my-2 bg-[#FFFFFF]'>

            <SearchBox
              cartItems={active_cart}
              isFetchingProducts={isFetchingProducts}
              products={data?.pages.flatMap(page => page?.data?.stock as SalesProductDto[]) ?? []}
              ref={searchRef}
              setSearchFilter={setSearchFilter}
              onAddToCart={add_item}
              onRemoveItem={remove_item}
            />

            <div className='flex items-center gap-4'>
              {/* <Select value={selectedPriceTag} onValueChange={(e) => { setSelectedPriceTag(e) }}>
                <SelectTrigger className='capitalize'>
                  <SelectValue className='capitalize' placeholder="Selling Price" />
                </SelectTrigger>
                <SelectContent>
                  {((priceTags?.data?.price_tags as any)?.concat([{ name: 'Selling Price' }]) ?? []).map(({ name, id }: { name: string, id: string },) => {
                    return (
                      <SelectItem className='capitalize' key={name} value={id}>
                        {name}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select> */}
              <PriceTag
                tags={((priceTags?.data?.price_tags as any)?.concat([{ name: 'Selling Price' }]) ?? [])}
                value={selectedPriceTag} onValueChange={(e) => { setSelectedPriceTag(e) }}
              />


              <Button className='bg-transparent min-w-max flex items-center gap-2 border-[#D6D6D6] border rounded-lg' onClick={openPendingOrdersModal}>
                <span className="flex h-6 w-6 items-center justify-center rounded-full bg-[#FDB9B9] text-xs">
                  <span className="inline-block font-[800] text-[#EF4444]">
                    {isPendingOrdersLoading ? (
                      <SmallSpinner />
                    ) : (
                      numberOfPendingOrders
                    )}
                  </span>
                </span>
                <span className='text-[#EF4444]'>Saved {(numberOfPendingOrders as number) > 0 ? "Orders" : "Order"} </span>


              </Button>

              <LinkButton className='flex items-center w-12 bg-[#F8FAFF]' href='#' size="icon" variant="secondary">
                <ScanIcon />
              </LinkButton>

              <Buttons className='flex items-center w-12 bg-[#F8FAFF]' size="icon" variant="secondary">
                <CameraIcon />
              </Buttons>

              <LinkButton className='flex items-center gap-2' href="#" size='thin' variant="secondary">
                Orders
                <Eye size={20} />
              </LinkButton>

              <LinkButton className='flex items-center w-12 bg-[#F8FAFF]' href="#" size="icon" variant="secondary">
                <LockIcon />
              </LinkButton>
            </div>
          </header>



          <main className='w-full grow grid grid-cols-[1fr,0.45fr] 2xl:grid-cols-[1fr,480px] h-[91.5vh]'>
            <div className='overflow-y-scroll [scrollbar-width:none_!important]'>
              <CategoriesList
                categories={categories?.data.categories.map((category) => ({ name: category.name, id: category.id })) || []}
                isLoading={isFetchingCategories}
                ref={categoriesListRef}
                selectedCategory={selectedCategory}
                onCategorySelect={handleCategorySelect}
              />
              <SubCategoriesList
                categories={subcategories?.data.subcategories.map((subcategory: { name: any; id: any; }) => ({ name: subcategory.name, id: subcategory.id })) || []}
                isLoading={isFetchingSubCategories}
                ref={subcategoriesListRef}
                selectedCategory={selectedSubCategory}
                onCategorySelect={handleSubCategorySelect}
              />
              <ProductContainer
                cartItems={active_cart}
                isFetchingProducts={isFetchingProducts}
                loadMoreDataObserver={<div
                  ref={sentryRef}
                />}
                products={productsToDisplay || []}
                ref={productsContainerRef}
                sell_without_inventory={branchInfo?.sell_without_inventory}
                onAddToCart={add_item}
                onRemoveFromCart={remove_item}
              />

              {isFetchingNextPage && (
                <div className="flex justify-center my-4">
                  <SmallSpinner color='#032282' />
                </div>
              )}

            </div>

            <aside className="grid grid-rows-[80%_20%] gap-2.5 pl-1 pr-5 pt-0 pb-4 overflow-y-scroll  max-h-[72.5vh]">
              <div className='flex flex-col gap-2.5 max-h-full overflow-y-scroll'>
                <PendingOrdersModal
                  closeModal={closeModal}
                  isPendingOrdersModalOpen={isPendingOrdersModalOpen}
                  setDiscountValue={setDiscountValue}
                  setPendingOrdersModalState={setPendingOrdersModalState}
                />

                <Cart
                  items={active_cart}
                  ref={cartRef}
                  onRemoveItem={delete_item}
                  onUpdateQuantity={update_item_quantity}
                />

                <Summary
                  additionalCharges={additionalCharges}
                  cart={active_cart}
                  discountValue={discountValue}
                  overallPrice={overallPrice}
                  paymentMethod={paymentMethod}
                  ref={summaryRef}
                  setAdditionalCharges={setAdditionalCharges}
                  setDiscountValue={setDiscountValue}
                  setOverallPrice={setOverallPrice}
                  setPendingOrdersModalState={setPendingOrdersModalState}
                />
              </div>

              <Checkout
                discountValue={discountValue}
                overallPrice={overallPrice}
                paymentMethod={paymentMethod}
                ref={checkoutRef}
                setDiscountValue={setDiscountValue}
                SetPaymentMethod={SetPaymentMethod}
              />
            </aside>
          </main>


          {/* <SuccessModal
        heading="Order created"
        isSuccessModalOpen={isSuccessModalOpen}
        setSuccessModalState={setSuccessModalState}
        subheading=""
      >
        <h2 className="-mt-2 text-base ">
          Order <span className="font-[700] text-[#073D9F]"> {batchId} </span>{' '}
          created.
        </h2>
        <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-6 pb-8 pt-4">
          <Button
            className="grow basis-1/2 text-sm"
            size="lg"
            onClick={() => {
              setSuccessModalState(false);
              closeConfirmPendModal();
            }}
          >
            Done
          </Button>

          <Button
            className="grow basis-1/2 text-sm"
            size="lg"
            variant={'outlined'}
            onClick={() => {
              setPendingOrdersModalState(true);
            }}
          >
            View Pending Orders
          </Button>
        </div>
      </SuccessModal> */}
        </div>
        :
        <>
          {!isCompanyLoading && <ViewBranchesDialog company={companyId}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            companyList={companies?.results} link={'/sales/sell'} />}
        </>
      }
    </>
  );
};




export default SalesDashboard;