import { useAuth } from '@/contexts/authentication';
import { authAxios, managementAxios, savingsAxios, setAxiosDefaultToken } from '@/lib/axios';

import { useMutation } from '@tanstack/react-query';

import type { AxiosResponse } from 'axios';

import { LoginPhoneNumberDto } from '../types';
import { tokenStorage } from '../utils';

import { getUserDetails } from './index';

interface TokenResponse {
  access: string;
  refresh: string;
}

const login = (loginPhoneNumberDto: LoginPhoneNumberDto): Promise<AxiosResponse<TokenResponse>> =>
  authAxios.post('/user/phone_login/create/', loginPhoneNumberDto);

export const usePhoneNumberLogin = () => {
  const { authDispatch } = useAuth();

  return useMutation(login, {
    onSuccess: async ({ data }) => {
      const { access: token } = data;
      tokenStorage.setToken(token);
      setAxiosDefaultToken(token, authAxios);
      setAxiosDefaultToken(token, savingsAxios);
      setAxiosDefaultToken(token, managementAxios);

      const user = await getUserDetails();

      if (authDispatch) {
        authDispatch({ type: 'LOGIN', payload: user });

        authDispatch({ type: 'STOP_LOADING' });
      }
    },
  });
};

// export const useLogin = () => {
//   const { authDispatch } = useAuth();
//   useMutation({});
//   return useMutation({
//     mutationFn: login,
//     onSuccess: async ({ data }) => {
//       const { access: token } = data;

//       tokenStorage.setToken(token);
//       setAxiosDefaultToken(token, authAxios);
//       setAxiosDefaultToken(token, managementAxios);

//       const user = await getUserDetails();

//       if (authDispatch) {
//         authDispatch({ type: 'LOGIN', payload: user });

//         authDispatch({ type: 'STOP_LOADING' });
//       }
//     },
//   });
// };
