"use client"

import type React from "react"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"

import { Input } from "@/components/ui/input"

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

import { format as formatDateFns } from "date-fns"
import { Megaphone, CalendarIcon as CalendarEventIcon, CalendarIcon, } from "lucide-react"
import { Calendar, ErrorModal, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Textarea } from "@/components/core"
import { cn } from "@/utils/classNames"
import { Form } from "@/components/core"
import { usePostAnnouncement } from "../../../api/employee-hris/admin-employee-hris/postAnnouncement"
import { useErrorModalState } from "@/hooks"
import toast from "react-hot-toast"

// Zod schema for form validation
const announcementSchema = z.object({
    announcement_type: z.enum(["ANNOUNCEMENT", "EVENT", "BIRTHDAY", "WORK_ANNIVERSARY"], {
        required_error: "Please select an announcement type.",
    }),
    announcement_title: z.string().min(1, "Title is required.").max(100, "Title must be 100 characters or less."),
    announcement_body: z
        .string()
        .min(1, "Description is required.")
        .max(500, "Description must be 500 characters or less."),
    announcement_date: z.date({
        required_error: "Please select a date.",
    }),
    announcement_time: z.string().min(1, "Time is required."),
})

type AnnouncementFormValues = z.infer<typeof announcementSchema>

interface CreateAnnouncementModalProps {
    isCreatennouncementDialogOpen: boolean;
    setCreatennouncementDialogState: React.Dispatch<React.SetStateAction<boolean>>;
    company_id: string;
    refetch: () => void;
}

const announcementTypes = [
    { value: "ANNOUNCEMENT", label: "Announcement", icon: Megaphone, color: "text-blue-500" },
    { value: "EVENT", label: "Event", icon: CalendarEventIcon, color: "text-green-500" },
    // { value: "BIRTHDAY", label: "Birthday", icon: Gift, color: "text-orange-500" },
    // { value: "WORK_ANNIVERSARY", label: "Work Anniversary", icon: Trophy, color: "text-purple-500" },
] as const

export default function CreateAnnouncementModal({ isCreatennouncementDialogOpen, setCreatennouncementDialogState, company_id, refetch }: CreateAnnouncementModalProps) {

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();


    const form = useForm<AnnouncementFormValues>({
        resolver: zodResolver(announcementSchema),
        defaultValues: {
            announcement_type: undefined,
            announcement_title: "",
            announcement_body: "",
            announcement_date: undefined,
            announcement_time: "",
        },
    })

    const { mutate: postPostAnnouncement, isLoading: isPostPostAnnouncementLoading } =
        usePostAnnouncement();

    const handleSubmit = async (values: AnnouncementFormValues) => {
        // Combine date and time
        const date = values.announcement_date;
        const [hours, minutes] = values.announcement_time.split(":");
        const combinedDate = new Date(date);
        combinedDate.setHours(Number(hours));
        combinedDate.setMinutes(Number(minutes));

        // Format as "DD-MM-YYYY hh:mm"
        const formatted = formatDateFns(combinedDate, "dd-MM-yyyy HH:mm");

        // Send formatted date string to backend
        postPostAnnouncement(
            {
                dataDto: {
                    ...values,
                    announcement_date: formatted, // send as string
                },
                company_id: company_id,
            },
            {
                onSuccess: () => {
                    toast.success("Announcement created syccessfully")
                    refetch()
                    setCreatennouncementDialogState(false)
                },

                onError: error => {
                    const errorMessage =
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        error.response.data.error;
                    openErrorModalWithMessage(errorMessage as string);
                },
            }
        );
    }

    const watchedValues = form.watch()
    const selectedType = announcementTypes.find((type) => type.value === watchedValues.announcement_type)

    return (
        <Dialog
            open={isCreatennouncementDialogOpen}
            onOpenChange={setCreatennouncementDialogState}
        >
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <DialogHeader className="space-y-3">
                    <DialogTitle className="text-xl font-bold flex items-center gap-2">
                        <div className="p-2 bg-blue-100 rounded-lg">
                            <Megaphone className="h-5 w-5 text-blue-600" />
                        </div>
                        Create New Announcement
                    </DialogTitle>
                    <DialogDescription className="text-sm text-slate-600">
                        Share important updates, events, and news with your team. All employees will be notified of this
                        announcement.
                    </DialogDescription>
                </DialogHeader>

                <Form {...form}>
                    <form className="space-y-6 py-4" onSubmit={form.handleSubmit(handleSubmit)}>
                        {/* Announcement Type */}
                        <FormField
                            control={form.control}
                            name="announcement_type"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-xs font-semibold text-slate-700">Announcement Type</FormLabel>
                                    <Select defaultValue={field.value} onValueChange={field.onChange}>
                                        <FormControl>
                                            <SelectTrigger className="h-12">
                                                <SelectValue placeholder="Select announcement type">
                                                    {selectedType && (
                                                        <div className="flex items-center gap-2">
                                                            <selectedType.icon className={cn("h-4 w-4", selectedType.color)} />
                                                            <span>{selectedType.label}</span>
                                                        </div>
                                                    )}
                                                </SelectValue>
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {announcementTypes.map((type) => (
                                                <SelectItem key={type.value} value={type.value}>
                                                    <div className="flex items-center gap-2">
                                                        <type.icon className={cn("h-4 w-4", type.color)} />
                                                        <span>{type.label}</span>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Title */}
                        <FormField
                            control={form.control}
                            name="announcement_title"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-xs font-semibold text-slate-700">Title</FormLabel>
                                    <FormControl>
                                        <Input className="h-12 text-xs" placeholder="Enter announcement title..." {...field} />
                                    </FormControl>
                                    <FormDescription className="text-xs text-slate-500 text-right">
                                        {field.value?.length || 0}/100 characters
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Body */}
                        <FormField
                            control={form.control}
                            name="announcement_body"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-xs font-semibold text-slate-700">Description</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            className="min-h-[120px] text-xs resize-none"
                                            placeholder="Write your announcement details here..."
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormDescription className="text-xs text-slate-500 text-right">
                                        {field.value?.length || 0}/500 characters
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Date */}
                        <FormField
                            control={form.control}
                            name="announcement_date"
                            render={({ field }) => (
                                <FormItem className="flex flex-col">
                                    <FormLabel className="text-xs font-semibold text-slate-700">Date</FormLabel>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <FormControl>
                                                <Button
                                                    className={cn(
                                                        "h-12 w-full justify-start text-left font-normal text-xs",
                                                        !field.value && "text-muted-foreground",
                                                    )}
                                                    variant="outline"
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {field.value ? formatDateFns(field.value, "PPP") : <span>Pick a date</span>}
                                                </Button>
                                            </FormControl>
                                        </PopoverTrigger>
                                        <PopoverContent align="start" className="w-auto p-0">
                                            <Calendar
                                                disabled={(date) => date < new Date("1900-01-01")}
                                                mode="single"
                                                selected={field.value}
                                                initialFocus
                                                onSelect={field.onChange}
                                            />
                                        </PopoverContent>
                                    </Popover>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Time */}
                        <FormField
                            control={form.control}
                            name="announcement_time"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="text-xs font-semibold text-slate-700">Time</FormLabel>
                                    <FormControl>
                                        <Input
                                            className="h-12 text-xs"
                                            type="time"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Preview Card */}
                        {(watchedValues.announcement_title || watchedValues.announcement_body) && (
                            <div className="space-y-2">
                                <FormLabel className="text-xs font-semibold text-slate-700">Preview</FormLabel>
                                <div className="p-4 border border-slate-200 rounded-lg bg-slate-50">
                                    <div className="flex items-start gap-3">
                                        {selectedType && (
                                            <div
                                                className={cn(
                                                    "p-2 rounded-full bg-white",
                                                    selectedType.color.replace("text-", "bg-").replace("500", "100"),
                                                )}
                                            >
                                                <selectedType.icon className={cn("h-4 w-4", selectedType.color)} />
                                            </div>
                                        )}
                                        <div className="flex-1">
                                            <h4 className="font-semibold text-slate-800 mb-1">
                                                {watchedValues.announcement_title || "Announcement Title"}
                                            </h4>
                                            <p className="text-sm text-slate-600 mb-2">
                                                {watchedValues.announcement_body || "Announcement description will appear here..."}
                                            </p>
                                            <div className="flex items-center gap-4 text-xs text-slate-500">
                                                <span>By You</span>
                                                {watchedValues.announcement_date && watchedValues.announcement_time && (
                                                    <span>
                                                        {(() => {
                                                            const date = new Date(watchedValues.announcement_date);
                                                            const [hours, minutes] = watchedValues.announcement_time.split(":").map(Number);
                                                            date.setHours(hours);
                                                            date.setMinutes(minutes);
                                                            return formatDateFns(date, "dd-MM-yyyy HH:mm");
                                                        })()}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </form>
                </Form>

                <DialogFooter className="gap-2 pt-4 border-t">
                    <Button
                        disabled={isPostPostAnnouncementLoading}
                        type="button"
                        variant="outline"
                        onClick={() => {
                            form.reset()
                            setCreatennouncementDialogState(false)
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        className="bg-blue-600 hover:bg-blue-700 text-white min-w-[100px]"
                        disabled={!form.formState.isValid || isPostPostAnnouncementLoading}
                        onClick={form.handleSubmit(handleSubmit)}
                    >
                        {isPostPostAnnouncementLoading ? "Creating..." : "Create Announcement"}
                    </Button>
                </DialogFooter>
            </DialogContent>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={errorModalMessage || 'Please check your inputs and try again.'}
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 text-base"
                        size="lg"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </Dialog>
    )
}
