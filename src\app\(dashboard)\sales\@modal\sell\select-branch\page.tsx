"use client"
// import { redirect } from 'next/navigation';
import React, { useEffect } from 'react';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';
// import { Companies, CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';


import SelectBranchDialog from '../../../misc/components/modals/SelectBranchDialog';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { LoaderModal } from '@/components/core';
import { redirect, useSearchParams } from 'next/navigation';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';

const Page = () => {
  const searchParams = useSearchParams();

  const company = searchParams.get('company') || '';

  const { data: defaultData, isLoading } = useDefaultCompanyBranch()

  const { data } = useCompaniesList()

  useEffect(() => {
    if (defaultData?.data.company_id) {
      if (!company || (defaultData?.data.company_id === company && defaultData?.data.branch_id)) {
        return redirect(`/sales/sell?company=${defaultData?.data.company_id}&companyName=${defaultData?.data.company}&branch=${defaultData?.data.branch_id}&branchName=${defaultData?.data.branch}`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultData?.data.company_id])


  return (
    <>
      {isLoading ? <LoaderModal isOpen={isLoading} /> : <SelectBranchDialog
        company={company}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={data?.results as CompaniesResultsEntity[]}
        link={'/sales/sell'}
      // subLink="sales"
      />}
    </>
  );
};

export default Page;
