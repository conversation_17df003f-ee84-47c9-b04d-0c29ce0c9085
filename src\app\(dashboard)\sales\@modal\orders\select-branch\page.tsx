"use client"
// import { redirect } from 'next/navigation';
import React from 'react';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';
// import { Companies, CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';


import SelectBranchDialog from '../../../misc/components/modals/SelectBranchDialog';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';


const Page = () => {

  const { data } = useCompaniesList()

  return (
    <>
      <SelectBranchDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={data?.results as CompaniesResultsEntity[]}
        queryParametersOnly={false}
        subLink="orders"
      />
    </>
  );
};

export default Page;
