// 'use client';

// import { Label } from '@radix-ui/react-label';
// import { useQueryClient } from '@tanstack/react-query';
// import { signIn, signOut } from 'next-auth/react';
// import { useRouter, useSearchParams } from 'next/navigation';
// import * as React from 'react';
// import { Controller, useForm } from 'react-hook-form';

// import { Button } from '@/components/core/Button';
// import { ErrorModal } from '@/components/core/ErrorModal';
// import { Input } from '@/components/core/Input';
// import { LinkButton } from '@/components/core/LinkButton';
// import { LoaderModal } from '@/components/core/LoaderModal';
// import { useBooleanStateControl, useErrorModalState } from '@/hooks';
// import { formatErrorObject } from '@/utils/errors';
// // import { getInputValueFromForm } from '@/utils/forms';
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core';
// import { cn } from '@/utils/classNames';
// import { countries } from '@/app/(dashboard)/stock/misc/utils/countries';

// interface LoginFormData {
//   country_code: string;
//   phone_number: string;
//   password: string;
// }

// const PasswordInput: React.FunctionComponent<{ onChange: (value: string) => void, value: string }> = ({ onChange, value }) => {
//   const { state: isShown, toggle: toggleShow } = useBooleanStateControl();

//   return (
//     <div className="mt-3 flex items-center overflow-hidden rounded-lg !bg-white/30 transition duration-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 focus-within:ring-offset-[#403C3A]">
//       <input
//         className="login-autofill-text login-no-chrome-autofill-bg h-auto min-w-0 grow !bg-transparent py-3.5 pl-6 text-base font-medium text-white placeholder:text-white focus-visible:outline-none"
//         id="password"
//         inputMode="numeric"
//         name="password"
//         pattern="[0-9]*"
//         placeholder="Passcode"
//         type={isShown ? 'text' : 'password'}
//         value={value}
//         required
//         onChange={(e) => onChange(e.target.value)}
//       />
//       <Button
//         className="bg-transparent px-6 py-3.5"
//         size="unstyled"
//         type="button"
//         variant="unstyled"
//         onClick={toggleShow}
//       >
//         <svg
//           fill="none"
//           height={20}
//           viewBox="0 0 20 20"
//           width={20}
//           xmlns="http://www.w3.org/2000/svg"
//         >
//           <path
//             d="M.833 9.63S4.167 3.21 10 3.21s9.167 6.42 9.167 6.42-3.334 6.42-9.167 6.42S.833 9.63.833 9.63Z"
//             stroke="#fff"
//             strokeLinecap="round"
//             strokeLinejoin="round"
//           />
//           <path
//             d="M10 12.037c1.38 0 2.5-1.078 2.5-2.407 0-1.33-1.12-2.408-2.5-2.408S7.5 8.3 7.5 9.63c0 1.33 1.12 2.407 2.5 2.407Z"
//             stroke="#fff"
//             strokeLinecap="round"
//             strokeLinejoin="round"
//           />
//         </svg>
//       </Button>
//     </div>
//   );
// };

// export function NewLoginPhoneNumberForm() {
//   const router = useRouter();
//   const searchParams = useSearchParams();
//   const queryClient = useQueryClient();

//   const { state: isLoaderModalOpen, setTrue: openLoaderModal } =
//     useBooleanStateControl();
//   const {
//     isErrorModalOpen,
//     setErrorModalState,
//     closeErrorModal,
//     openErrorModalWithMessage,
//     errorModalMessage,
//   } = useErrorModalState();

//   const [isLoading, setIsLoading] = React.useState<boolean>(false);

//   const callbackUrl = searchParams.get('callbackUrl');

//   // Ensure old cookies get cleared out when users get redirected to login.
//   React.useEffect(() => {
//     const clearCookies = async () => {
//       await fetch(`${location.origin}/api/auth/cookies`);
//     };

//     clearCookies();
//     signOut({
//       redirect: false,
//     });
//   }, []);

//   const [countryList, setCountryList] = React.useState<{ name: string, code: string, phoneCode: string }[]>();
//   React.useEffect(() => {
//     setCountryList(countries);
//   }, []);

//   const countryOptions = countryList?.map(({ name, phoneCode, code }) => ({
//     value: phoneCode,
//     label: name,
//     code: code,
//   }));

//   const { control, handleSubmit } = useForm<LoginFormData>({
//     defaultValues: {
//       country_code: '+234', // Default to Nigeria's country code
//       phone_number: '',
//       password: ''
//     }
//   });

//   async function handleSignIn(formData: LoginFormData) {
//     setIsLoading(true);

//     const formattedPhoneNumber = formData.phone_number.startsWith('0')
//       ? `${formData.country_code}${formData.phone_number.substring(1)}`
//       : `${formData.country_code}${formData.phone_number}`;

//     console.log('Attempting phone login with:', {
//       phone_number: formattedPhoneNumber,
//       password: formData.password
//     });
//     const signInResult = await signIn('phone-login', {
//       phone_number: formattedPhoneNumber,
//       password: formData.password,
//       redirect: false,
//     });


//     console.log('Sign in result:', signInResult);
//     setIsLoading(false);

//     if (!!signInResult?.error) {
//       try {
//         const errorObject = JSON.parse(signInResult.error);
//         const errorMessage = formatErrorObject(errorObject);
//         openErrorModalWithMessage(errorMessage);
//       } catch (error) {
//         openErrorModalWithMessage(
//           'There was an error processing that. Please contact support.'
//         );
//         console.error('Error parsing JSON:', error);
//       }
//     } else {
//       queryClient.invalidateQueries({
//         queryKey: ['user-details'],
//       });
//       // Show a loader because routing takes a short while.
//       openLoaderModal();
//       router.push(callbackUrl || '/dashboard');
//     }
//   }

//   return (
//     <>
//       <LoaderModal isOpen={isLoaderModalOpen} />

//       <form className="relative z-10" onSubmit={handleSubmit(handleSignIn)}>
//         <Label className="sr-only" htmlFor="phone">
//           Phone Number
//         </Label>
//         <div className='flex gap-3  mt-4 items-center'>
//           <div>
//             <Controller
//               control={control}
//               name="country_code"
//               render={({ field }) => (
//                 <Select defaultValue={field.value} onValueChange={field.onChange}>
//                   <SelectTrigger
//                     className={cn(
//                       'flex h-10 w-full items-center justify-between gap-2 rounded-md !bg-white/30 px-3 text-base font-medium ring-offset-white transition duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
//                       'border !z-[99999999999] bg-transparent !whitespace-nowrap text-xs pr-0.5 pl-4 login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg px-6 py-3.5 font-medium text-white placeholder:text-white focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70'
//                     )}
//                   >
//                     <SelectValue className='!whitespace-nowrap !text-white !placeholder:text-white' placeholder="Select country code" />
//                   </SelectTrigger>
//                   <SelectContent>
//                     {countryOptions?.map((option) => (
//                       <SelectItem key={option.label} value={option.value}>
//                         {option.label} {option.value}
//                       </SelectItem>
//                     ))}
//                   </SelectContent>
//                 </Select>
//               )}
//             />
//           </div>
//           <div className="relative">
//             <Controller
//               control={control}
//               name="phone_number"
//               render={({ field }) => (
//                 <Input
//                   {...field}
//                   className="login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
//                   maxLength={20}
//                   placeholder="Phone Number"
//                   type="tel"
//                   required
//                   onInput={(e: React.FormEvent<HTMLInputElement>) => {
//                     const target = e.target as HTMLInputElement;
//                     const value = target.value;
//                     target.value = value.replace(/[^0-9+]/g, '');
//                     field.onChange(target.value);
//                   }}
//                 />
//               )}
//             />
//           </div>
//         </div>
//         <Label className="sr-only" htmlFor="password">
//           Password
//         </Label>
//         <Controller
//           control={control}
//           name="password"
//           render={({ field }) => (
//             <PasswordInput {...field} />
//           )}
//         />

//         <div className="mt-3 flex items-center justify-between gap-4">
//           <label className="flex items-center" htmlFor="logged-in">
//             <input
//               className="peer sr-only"
//               id="logged-in"
//               name="loggedIn"
//               type="checkbox"
//             />
//             <span className="mr-2 block h-4 w-4 rounded border border-white p-2 peer-checked:bg-marketing-light-text"></span>
//             <span className="block text-xs text-white">Keep me logged in</span>
//           </label>

//           <LinkButton
//             className="text-right text-white"
//             href={"/forgot-password"}
//             size="unstyled"
//             type="button"
//             variant="unstyled"
//           >
//             Forgot Passcode?
//           </LinkButton>
//         </div>

//         <Button
//           className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
//           disabled={isLoading}
//           type="submit"
//           variant="white"
//         >
//           {isLoading ? 'Loading' : 'Login'}
//         </Button>

//         <LinkButton
//           className="mt-6 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
//           href="/sign-up/get-started"
//           type="button"
//           variant="outlined"
//         >
//           <span className="text-xxs font-normal md:text-xs">
//             Don&apos;t have an account?
//           </span>
//           <span className="text-sm md:text-base">Sign up</span>
//         </LinkButton>
//       </form>

//       <ErrorModal
//         isErrorModalOpen={isErrorModalOpen}
//         setErrorModalState={setErrorModalState}
//         subheading={
//           errorModalMessage || 'Please check your inputs and try again.'
//         }
//       >
//         <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
//           <Button
//             className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
//             size="lg"
//             type="button"
//             onClick={closeErrorModal}
//           >
//             Okay
//           </Button>
//         </div>
//       </ErrorModal>
//     </>
//   );
// }

'use client';

import { Label } from '@radix-ui/react-label';
import { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import * as React from 'react';
// import { AES } from 'crypto-js';
import { Controller, useForm } from 'react-hook-form';

import { Button } from '@/components/core/Button';
import { ErrorModal } from '@/components/core/ErrorModal';
import { Input } from '@/components/core/Input';
import { LinkButton } from '@/components/core/LinkButton';
import { LoaderModal } from '@/components/core/LoaderModal';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
// import { useUserCredentialsDetails } from '@/stores';
import { formatAxiosErrorMessage } from '@/utils/errors';
// import { getInputValueFromForm } from '@/utils/forms';
import { usePhoneNumberLogin } from '../../../misc';
import { cn } from '@/utils/classNames';
import { countries } from '@/app/(dashboard)/stock/misc/utils/countries';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core';


interface LoginFormData {
  country_code: string;
  phone_number: string;
  password: string;
}

const _ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'fallback-key';

if (!process.env.NEXT_PUBLIC_ENCRYPTION_KEY) {
  throw new Error('NEXT_PUBLIC_ENCRYPTION_KEY is required');
}

const PasswordInput: React.FunctionComponent<{ onChange: (value: string) => void, value: string }> = ({ onChange, value }) => {
  const { state: isShown, toggle: toggleShow } = useBooleanStateControl();

  return (
    <div className="mt-3 flex items-center overflow-hidden rounded-lg !bg-white/30 transition duration-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 focus-within:ring-offset-[#403C3A]">
      <input
        className="login-autofill-text login-no-chrome-autofill-bg h-auto min-w-0 grow !bg-transparent py-3.5 pl-6 text-base font-medium text-white placeholder:text-white focus-visible:outline-none"
        id="password"
        inputMode="numeric"
        name="password"
        pattern="[0-9]*"
        placeholder="Passcode"
        type={isShown ? 'text' : 'password'}
        value={value}
        required
        onChange={(e) => onChange(e.target.value)}
      />
      <Button
        className="bg-transparent px-6 py-3.5"
        size="unstyled"
        type="button"
        variant="unstyled"
        onClick={toggleShow}
      >
        <svg
          fill="none"
          height={20}
          viewBox="0 0 20 20"
          width={20}
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M.833 9.63S4.167 3.21 10 3.21s9.167 6.42 9.167 6.42-3.334 6.42-9.167 6.42S.833 9.63.833 9.63Z"
            stroke="#fff"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M10 12.037c1.38 0 2.5-1.078 2.5-2.407 0-1.33-1.12-2.408-2.5-2.408S7.5 8.3 7.5 9.63c0 1.33 1.12 2.407 2.5 2.407Z"
            stroke="#fff"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </Button>
    </div>
  );
};

export function NewLoginPhoneNumberForm() {
  const router = useRouter();
  // const { setEmail, setPassword } = useUserCredentialsDetails();

  const { state: isLoaderModalOpen, setTrue: _openLoaderModal } =
    useBooleanStateControl();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const [countryList, setCountryList] = React.useState<{ name: string, code: string, phoneCode: string }[]>();
  React.useEffect(() => {
    setCountryList(countries);
  }, []);

  const countryOptions = countryList?.map(({ name, phoneCode, code }) => ({
    value: phoneCode,
    label: name,
    code: code,
  }));

  // Inside your component
  const [searchQuery, setSearchQuery] = React.useState('');

  const filteredOptions = countryOptions?.filter(option =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const { control, handleSubmit } = useForm<LoginFormData>({});

  const { mutate: postPhoneNumberLogIn, isLoading: isLoginLoading } = usePhoneNumberLogin();

  const _missingPinResponse =
    'Error. You do not have any pin yet. please create a transaction pin with link below. Https://backend.libertypayng.com/agency/user/create_transaction_pin/';

  async function handleSignIn(formData: LoginFormData) {
    const formattedPhoneNumber = formData.phone_number.startsWith('0')
      ? `${formData.country_code}${formData.phone_number.substring(1)}`
      : `${formData.country_code}${formData.phone_number}`;

    const updatedData = {
      phone_number: formattedPhoneNumber,
      password: formData.password,
      device_type: 'MOBILE',
    };

    postPhoneNumberLogIn(updatedData, {
      onSuccess: () => {
        // setEmail(encryptedEmail);
        // setPassword(encryptedPassword);

        router.push('/dashboard');
      },
      onError: error => {
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        if (errorMessage === "You do not have any pin yet. Please create a transaction pin with link below") {
          router.push('/transaction-pin');
        }
        openErrorModalWithMessage(errorMessage as string);
        // if (errorMessage === missingPinResponse) {
        //   router.push('/transaction-pin');
        // }
      },
    });
  }

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen} />

      <form className="relative z-10" onSubmit={handleSubmit(handleSignIn)}>
        <Label className="sr-only" htmlFor="phone">
          Phone Number
        </Label>
        <div className='flex gap-3  mt-4 items-center'>
          <div>
            <Controller
              control={control}
              name="country_code"
              render={({ field }) => (
                <Select defaultValue={field.value} onValueChange={field.onChange}>
                  <SelectTrigger
                    className={cn(
                      'flex h-10 w-full items-center justify-between gap-2 rounded-md !bg-white/30 px-3 text-base font-medium ring-offset-white transition duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
                      'border !z-[99999999999] bg-transparent !whitespace-nowrap text-xs pr-0.5 pl-4 login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg px-6 py-3.5 font-medium text-white placeholder:text-white focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70'
                    )}
                  >
                    <SelectValue className='!whitespace-nowrap !text-white !placeholder:text-white' placeholder="Select country code" />
                  </SelectTrigger>
                  <SelectContent>
                    <Input
                      className="p-2 mb-2 rounded-md" // Add your styling here
                      placeholder="Search..."
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    {filteredOptions?.map((option) => (
                      <SelectItem key={option.label} value={option.value}>
                        {option.label} {option.value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
          <div className="relative w-full">
            <Controller
              control={control}
              name="phone_number"
              render={({ field }) => (
                <Input
                  {...field}
                  className="login-autofill-text w-full login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
                  maxLength={20}
                  placeholder="Phone Number"
                  type="tel"
                  required
                  onInput={(e: React.FormEvent<HTMLInputElement>) => {
                    const target = e.target as HTMLInputElement;
                    const value = target.value;
                    target.value = value.replace(/[^0-9+]/g, '');
                    field.onChange(target.value);
                  }}
                />
              )}
            />
          </div>
        </div>
        <Label className="sr-only" htmlFor="password">
          Password
        </Label>
        <Controller
          control={control}
          name="password"
          render={({ field }) => (
            <PasswordInput {...field} />
          )}
        />

        <div className="mt-3 flex items-center justify-between gap-4">
          <label className="flex items-center" htmlFor="logged-in">
            <input
              className="peer sr-only"
              id="logged-in"
              name="loggedIn"
              type="checkbox"
            />
            <span className="mr-2 block h-4 w-4 rounded border border-white p-2 peer-checked:bg-marketing-light-text"></span>
            <span className="block text-xs text-white">Keep me logged in</span>
          </label>

          <LinkButton
            className="text-right text-white"
            href={"/forgot-password"}
            size="unstyled"
            type="button"
            variant="unstyled"
          >
            Forgot Passcode?
          </LinkButton>
        </div>

        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isLoginLoading}
          type="submit"
          variant="white"
        >
          {isLoginLoading ? 'Loading' : 'Login'}
        </Button>

        <LinkButton
          className="mt-6 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
          href="/sign-up/get-started"
          type="button"
          variant="outlined"
        >
          <span className="text-xxs font-normal md:text-xs">
            Don&apos;t have an account?
          </span>
          <span className="text-sm md:text-base">Sign up</span>
        </LinkButton>
      </form>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={errorModalMessage || 'Please check your inputs and try again.'}
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
