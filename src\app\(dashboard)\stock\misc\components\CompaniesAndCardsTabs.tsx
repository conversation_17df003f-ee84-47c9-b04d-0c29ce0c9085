'use client';

import * as React from 'react';

import { LinkButton, LoaderModal, Dialog, DialogContent, DialogHeader, DialogTitle, Button, DialogClose, ErrorModal, } from '@/components/core';
import { useGetAllCompaniesInfo } from '../api';
import { CompanyListItemDropdownMenu } from './CompanyListItemDropdownMenu';
import { addCommasToNumber } from '@/utils/numbers';
import { useRouter } from 'next/navigation';
import { useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { useCreateInvoice } from '@/app/(dashboard)/subscription/misc/api/createSubscribe';
import { CheckCircle2, ChevronRight, Clock, CreditCard, XCircle } from 'lucide-react';
import { cn } from '@/utils/classNames';
import TokenBalanceCard from '@/app/(dashboard)/subscription/misc/components/TokenBalanceCard';
import { TokenBalanceInfo } from '../types';
import TokenInsufficientModal from '@/app/(dashboard)/sales/misc/components/modals/TokenInsufficientModal';

interface CompanyListItemProps {
  id: string;
  company_name: string;
  branches: number;
  stock_value: number;
  total_stock: number;
  is_subscription: boolean;
  is_free_trial: boolean;
  is_paid_subscription: boolean;
  active_subscriptions: Array<{
    id: number;
    status: string;
    access_type: string;
    plan: string | null;
    overall_sub_days: string;
    created_at: string;
    pos_included: boolean;
    modules: Array<{
      module_subscription_id: number;
      module: {
        id: number;
        name: string;
        code: string;
        description: string;
        is_premium: boolean;
      };
      plan_name: string;
      plan_id: number;
      start_date: string;
      end_date: string;
      status: string;
      is_active: boolean;
      is_not_started: boolean;
    }>;
    has_active_modules: boolean;
    has_not_started_modules: boolean;
  }>;
  not_started_subscriptions: Array<{
    id: number;
    status: string;
    access_type: string;
    plan: string | null;
    overall_sub_days: string;
    created_at: string;
    pos_included: boolean;
    modules: Array<{
      module_subscription_id: number;
      module: {
        id: number;
        name: string;
        code: string;
        description: string;
        is_premium: boolean;
      };
      plan_name: string;
      plan_id: number;
      start_date: string;
      end_date: string;
      status: string;
      is_active: boolean;
      is_not_started: boolean;
    }>;
    has_active_modules: boolean;
    has_not_started_modules: boolean;
  }>;
  token_balance_info?: TokenBalanceInfo;
}

function ModuleSubscriptionModal({ isOpen, onClose, company }: { isOpen: boolean; onClose: () => void; company: CompanyListItemProps }): JSX.Element {
  const router = useRouter();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const allModules = [
    ...company.active_subscriptions.flatMap(sub =>
      sub.modules.map(module => ({
        ...module,
        subscription: sub,
        plan_id: module.plan_id
      }))
    ),
    ...company.not_started_subscriptions.flatMap(sub =>
      sub.modules.map(module => ({
        ...module,
        subscription: sub,
        plan_id: module.plan_id
      }))
    )
  ];

  const hasStockModule = allModules.some(module =>
    module.module.code === "INVENTORY" && module.is_active
  );

  const { mutate: handlePaystackInvoice, isLoading: isLoadingPaystack } = useCreateInvoice();

  const handlePaystack = (moduleSubscriptionId: number, planId: number) => {

    if (!planId) {
      openErrorModalWithMessage('No plan selected for this module');
      return;
    }

    handlePaystackInvoice(
      {
        company_id: String(company.id),
        //@ts-ignore
        requestData: [
          {
            module_id: moduleSubscriptionId,
            plan: planId
          }
        ]
      },
      {
        onSuccess: (data) => {
          if (data) {
            // router.replace(data?.payment_link);
            window.open(data?.payment_link, '_blank');
          }
        },
        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        }
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-xl h-[650px] flex flex-col">
        <DialogHeader className="px-6 py-4">
          <DialogTitle className="text-sm font-semibold flex items-center gap-2">
            Module Subscription Details
          </DialogTitle>
          <DialogClose>Close</DialogClose>
        </DialogHeader>

        {/* Scrollable content area */}
        <div className="p-6 overflow-y-auto flex-1">
          {!hasStockModule && (
            <div className="mb-6 p-4 bg-red-50 border border-red-100 rounded-lg flex items-start gap-3">
              <XCircle className="h-5 w-5 text-red-500 mt-0.5 shrink-0" />
              <div>
                <p className="font-medium text-red-700">Stock and Inventory is not included</p>
                <p className="text-sm text-red-600 mt-1">
                  Contact your account manager to add this module to your subscription.
                </p>
              </div>
            </div>
          )}

          <h3 className="text-sm font-medium text-slate-700 mb-3">Your current subscription includes:</h3>

          <div className="space-y-3 mb-6">
            {allModules.map((module) => (
              <div className="p-3 border rounded-lg hover:bg-slate-50 transition-colors" key={module.module_subscription_id}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <CheckCircle2 className="h-5 w-5 text-emerald-500 shrink-0" />
                    <span className="font-medium">{module.module.name}</span>
                  </div>
                  <div className={cn(
                    "flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium",
                    module.is_not_started && "bg-amber-100 text-amber-700",
                    module.status === "active" && !module.is_not_started && "bg-emerald-100 text-emerald-700",
                    module.status === "pending" && !module.is_not_started && "bg-amber-100 text-amber-700",
                    module.status === "expired" && !module.is_not_started && "bg-red-100 text-red-700"
                  )}>
                    {module.is_not_started ? (
                      <>
                        <Clock className="h-3 w-3" />
                        <span>Pending Payment</span>
                      </>
                    ) : module.status === "active" ? (
                      <>
                        <CheckCircle2 className="h-3 w-3" />
                        <span>Active</span>
                      </>
                    ) : module.status === "pending" ? (
                      <>
                        <Clock className="h-3 w-3" />
                        <span>Pending</span>
                      </>
                    ) : (
                      <>
                        <XCircle className="h-3 w-3" />
                        <span>Expired</span>
                      </>
                    )}
                  </div>
                </div>
                {module.module.description && <p className="text-sm text-slate-500 mt-1 ml-8">{module.module.description}</p>}
                {module.is_not_started && (
                  <div className="mt-3 ml-8">
                    <Button
                      className="gap-1.5"
                      disabled={isLoadingPaystack}
                      size="default"
                      variant="outlined"
                      onClick={() => handlePaystack(module.module.id, module.plan_id)}
                    >
                      <CreditCard className="h-3.5 w-3.5" />
                      Complete Payment
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Fixed button container at the bottom */}
        <div className="p-4 border-t sticky bottom-0 bg-white">
          <Button
            className="gap-1 w-full sm:w-auto"
            onClick={() => {
              router.push(`/subscription?companyId=${company.id}&companyName=${company.company_name}`)
            }}
          >
            Manage Subscription <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

      </DialogContent>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={errorModalMessage || 'Please check your inputs and try again.'}
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button className="grow bg-red-950 text-base" size="lg" onClick={closeErrorModal}>
            Okay
          </Button>
        </div>
      </ErrorModal>
    </Dialog>
  );
}

function CompanyListItem({
  id,
  company_name,
  branches,
  stock_value,
  total_stock,
  is_subscription,
  is_free_trial,
  is_paid_subscription,
  active_subscriptions,
  not_started_subscriptions,
  token_balance_info
}: CompanyListItemProps) {
  const router = useRouter();
  const [showModuleModal, setShowModuleModal] = React.useState(false);
  const [showTokenModal, setShowTokenModal] = React.useState(false);
  const [isHovered, setIsHovered] = React.useState(false);
  const hasNotStartedSubscriptions = not_started_subscriptions.length > 0;
  const hasStockModule = active_subscriptions.some(sub =>
    sub.modules.some(module => module.module.code === "INVENTORY" && module.is_active)
  );

  // Check if user has sufficient tokens
  const hasTokens = token_balance_info && token_balance_info.current_balance > 0;



  const handleViewDetails = () => {
    // COMMENT FOR NOW
    // if (!hasStockModule) {
    //   setShowModuleModal(true);
    //   return;
    // }

    // Check if user has tokens for token-based subscriptions
    if (!hasTokens) {
      setShowTokenModal(true);
      return;
    }

    router.push(`/stock/company-details/?company=${id}`);
  };

  const handleCreateBranch = () => {
    if (!hasStockModule) {
      setShowModuleModal(true);
      return;
    }

    router.push(`/stock/create-branch?company=${id}`);
  };

  return (
    <li
      className="w-full rounded-10 border border-card-border p-3.5 shadow-card sm:p-5 xl:px-8 transition-all duration-300 hover:shadow-lg"
      key={id}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className='flex items-center mb-2'>
        <dd className="text-xs">
          {hasNotStartedSubscriptions ? (
            <span className="text-amber-600">Pending Payment</span>
          ) : is_free_trial ? (
            <span className="text-amber-600">Free Trial</span>
          ) : is_paid_subscription ? (
            <span className="text-green-600">Paid Subscription</span>
          ) : is_subscription ? (
            <span className="text-green-600">Subscribed</span>
          ) : (
            <span className="text-red-600">Not Subscribed</span>
          )}
        </dd>
        <CompanyListItemDropdownMenu id={id} />
      </div>

      <dl className="mb-4 grid grid-cols-3 gap-3">
        <div className="flex flex-col-reverse gap-1.5">
          <dt className="text-xxs text-label-text">Company</dt>
          <dd className="text-xs text-[#373737]">{company_name}</dd>
        </div>

        {(is_subscription || is_free_trial || hasNotStartedSubscriptions) && (
          <>
            <div className="flex flex-col-reverse gap-1.5">
              <dt className="text-xxs text-label-text">Branches</dt>
              <dd className="text-xs text-[#373737]">{branches}</dd>
            </div>
            <div className="flex flex-col-reverse gap-1.5">
              <dt className="text-xxs text-label-text">Total stock</dt>
              <dd className="text-xs text-[#373737]">{total_stock}</dd>
            </div>
          </>
        )}
      </dl>

      {/* Token Balance Section - Shows on Hover */}
      {token_balance_info && (
        <div
          className={`overflow-hidden transition-all duration-300 ease-in-out ${isHovered
            ? 'max-h-32 opacity-100 mb-4'
            : 'max-h-0 opacity-0 mb-0'
            }`}
        >
          <div className="pt-2">
            <TokenBalanceCard
              tokenBalanceInfo={token_balance_info}
              variant="compact"
            />
          </div>
        </div>
      )}

      <div className="flex flex-wrap gap-1.5 sm:grid sm:grid-cols-3 sm:gap-3">
        {(is_subscription || is_free_trial || hasNotStartedSubscriptions) ? (
          <>
            <div className="flex flex-col-reverse gap-1.5">
              <dt className="text-xxs text-label-text">Stock value</dt>
              <dd className="text-xs text-[#373737]">{addCommasToNumber(Number(stock_value))}</dd>
            </div>
            <Button
              className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
              variant="light"
              onClick={handleViewDetails}
            >
              <span className="text-main-solid">View details</span>
            </Button>
            <Button
              className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
              variant="light"
              onClick={handleCreateBranch}
            >
              <span className="text-main-solid">Create Branch</span>
            </Button>
          </>
        ) : (
          <LinkButton
            className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
            href={`/subscription?company_id=${id}`}
            variant="light"
          >
            <span className="text-main-solid">Subscribe</span>
          </LinkButton>
        )}
      </div>

      <ModuleSubscriptionModal
        company={{
          id,
          company_name,
          is_subscription,
          is_free_trial,
          is_paid_subscription,
          active_subscriptions,
          not_started_subscriptions,
          branches,
          stock_value,
          total_stock
        }}
        isOpen={showModuleModal}
        onClose={() => setShowModuleModal(false)}
      />

      <TokenInsufficientModal
        isOpen={showTokenModal}
        onClose={() => setShowTokenModal(false)}
        companyId={id}
        companyName={company_name}
        currentBalance={token_balance_info?.current_balance || 0}
        module='Stock'
      />

    </li>
  );
}

export function CompaniesList() {
  const fetchOptions = {
    pageIndex: 1,
  };
  const { data, isLoading } = useGetAllCompaniesInfo(fetchOptions);

  return (
    <div className="md:px-0" defaultValue="companies">
      <LoaderModal isOpen={isLoading} />
      <div className="mt-1 rounded-10 bg-white p-6 lg:py-8">
        <ul className="grid grid-cols-1 justify-start gap-4 [@media(min-width:1154px)]:grid-cols-2 [@media(min-width:1614px)]:grid-cols-3">
          {data?.companies?.map(
            ({ id, company_name, branches, stock_value, total_stock, is_subscription, is_free_trial, is_paid_subscription, active_subscriptions, not_started_subscriptions, token_balance_info }) => (
              <CompanyListItem
                active_subscriptions={active_subscriptions}
                branches={branches}
                company_name={company_name}
                id={id}
                is_free_trial={is_free_trial}
                is_paid_subscription={is_paid_subscription}
                is_subscription={is_subscription}
                key={id}
                not_started_subscriptions={not_started_subscriptions}
                stock_value={stock_value}
                total_stock={total_stock}
                token_balance_info={token_balance_info}
              />
            )
          )}
        </ul>
      </div>
    </div>
  );
}
