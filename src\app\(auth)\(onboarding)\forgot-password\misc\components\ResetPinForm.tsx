'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import type { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/core/Button';
import { ErrorModal } from '@/components/core/ErrorModal';
import { LoaderModal } from '@/components/core/LoaderModal';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import PinInput from 'react-pin-input';
import { useRequestResetPinOtp } from '../api/resetPinOtp';
import { useRequestResetPassword } from '../api/forgotPassword'
import ResendOTP from './ResendOTP';

interface EmailOTPFormProps {
  email: string;
  phone: string
}


const ResetPinFormSchema = z.object({
  otp: z
    .string({ required_error: 'Please enter a pin.' })
    .trim()
    .min(6, { message: 'Your pin must be 6 numeric characters.' })
    .max(6, { message: 'Your pin must be 6 numeric characters.' }),
});

export type ResetPinFormValues = z.infer<
  typeof ResetPinFormSchema
>;

export function ResetPinForm({ email, phone }: EmailOTPFormProps) {
  const router = useRouter();

  const [passcode, setPasscode] = React.useState('');

  const { state: isLoaderModalOpen, setTrue: _openLoaderModal } =
    useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    formState: { },
  } = useForm<ResetPinFormValues>({
    resolver: zodResolver(ResetPinFormSchema),
  });

  const { mutate: verifyResetPasscodeOtp, isLoading: isVerifyResetPasscodeOtpLoading } =
    useRequestResetPinOtp();
  const { mutate: requestResetPassword, isLoading: isRequestResetPasswordLoading, } = useRequestResetPassword()


  const handleOTPValidation = (passcode: string) => {
    verifyResetPasscodeOtp(
      {
        otp: passcode,
        phone_number: phone,
      },
      {
        onSuccess: () => {
          router.push(`/forgot-password/reset?email=${email}&phone=${phone}&otp=${passcode}`)
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );
  };


  const handleComplete = (passcode: string) => {
    setPasscode(passcode);
    handleOTPValidation(passcode);
  };

  const handleContinueClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault;
    handleOTPValidation(passcode);
  };

  async function handleResendOTP(event: React.MouseEvent<HTMLButtonElement>) {
    event.preventDefault();
    const updatedData = {
      identifier: email,
      entry_type: "email"
    }
    requestResetPassword(updatedData)
  }

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen} />

      <form className="relative">
        <PinInput
          autoSelect={false}
          initialValue="o"
          inputFocusStyle={{
            border: '3px solid #403C3A',
            borderRadius: '.625rem',
            padding: '0.5rem',
            outline: 'none',
            color: 'white',
            background: 'rgba(255, 255, 255, 0.3)',
            boxShadow: '0 0 0 1px rgb(255, 255, 255)',
          }}
          inputMode="number"
          inputStyle={{
            marginRight: '.3125rem',
            marginLeft: '.3125rem',
            background: '#F5F7F9',
            borderRadius: '14px',
            fontSize: '15px',
            fontWeight: '500',
            width: 'calc(16.5% - 10px)',
            height: 'unset',
            aspectRatio: '1',
            transitionDuration: '300ms',
            transitionProperty:
              'color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter',
            transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          length={6}
          style={{ maxWidth: '25rem' }}
          type="numeric"
          onComplete={handleComplete}
        />

        <Button
          className="my-11 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isVerifyResetPasscodeOtpLoading}
          type="button"
          variant="white"
          onClick={handleContinueClick}
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>{isVerifyResetPasscodeOtpLoading ? 'Loading' : 'Continue'}</span>

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>
        <ResendOTP
          handleResendOTP={handleResendOTP}
          isRequestResetPasswordLoading={isRequestResetPasswordLoading}
          minutes={0}
          seconds={59}
        />
      </form>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
