'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

import { CustomersActivitySummaryStats } from '@/app/(dashboard)/sales/misc/components/CustomersActivitySummaryStats';
import { CustomersDashboardStats } from '@/app/(dashboard)/sales/misc/components/CustomersDashboardStats';
import { AllCustomersTable } from '@/app/(dashboard)/sales/misc/components/tables';
import ActiveCustomersTransactionsTable from '@/app/(dashboard)/sales/misc/components/tables/ActiveCustomersTransactions';
import InActiveCustomersTransactionsTable from '@/app/(dashboard)/sales/misc/components/tables/InActiveCustomersTransactions';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core';
import { ViewBranchesDialog } from '../../stock/misc/components';
import { LoaderModal } from '@/components/core';
import { useCompaniesList } from '../../instant-web/misc/api';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';
import { CompaniesResultsEntity } from '../../spend-management/misc/types';
// import SubscriptionErrorModal from '../../spend-management/misc/components/subscription/ErrorSubscriptionModal';

const Page = () => {
    const searchParams = useSearchParams();
    const pathname = usePathname();
    const router = useRouter();

    const branchId = searchParams.get('branch') || '';
    const companyId = searchParams.get('company') || '';

    const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
    const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

    useEffect(() => {
        if (!companyId && salesUserDetails?.data) {
            router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [salesUserDetails?.data.company_id])


    return (
        <>
            <LoaderModal isOpen={isDefaultLoading || !companyId} />

            {branchId ? <div>
                <CustomersDashboardStats branchId={branchId} companies={companies?.results as unknown as CompaniesResultsEntity[]} />

                <div className="sales__customers__table md:px-7 md:py-3 lg:px-11">
                    <CustomersActivitySummaryStats branchId={branchId} />

                    <Tabs className="w-full" defaultValue="all__customers">
                        <TabsList className="my-4 grid grid-cols-1 gap-4 border-none bg-white md:grid-cols-2 lg:grid-cols-5">
                            <TabsTrigger
                                className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                                value="all__customers"
                            >
                                <p>All Customers</p>
                            </TabsTrigger>
                            <TabsTrigger
                                className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                                value="active__customers"
                            >
                                <p>Active Customers</p>
                            </TabsTrigger>

                            <TabsTrigger
                                className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                                value="inactive__customers"
                            >
                                <p>Inactive Customers </p>
                            </TabsTrigger>
                        </TabsList>
                        <div className="mt-[10px] w-full "></div>
                        <TabsContent
                            className="mt-2 h-screen w-full rounded-sm bg-white"
                            value="all__customers"
                        >
                            <AllCustomersTable branchId={branchId} companyId={companyId} />
                        </TabsContent>
                        <TabsContent
                            className="mt-2 h-screen w-full rounded-sm bg-white"
                            value="active__customers"
                        >
                            <ActiveCustomersTransactionsTable branchId={branchId} />
                        </TabsContent>

                        <TabsContent
                            className="mt-2 h-screen w-full rounded-sm bg-white"
                            value="inactive__customers"
                        >
                            <InActiveCustomersTransactionsTable branchId={branchId} />
                        </TabsContent>
                    </Tabs>
                </div>
            </div> :
                <>
                    {!isCompanyLoading && <ViewBranchesDialog company={companyId}
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        companyList={companies?.results} link={'/sales/customers'} />}
                </>
            }

            {/* {
                (!salesUserDetails?.data?.is_subscription && !isDefaultLoading) && <SubscriptionErrorModal
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    message={"Access Forbidden"}
                />
            } */}

        </>

    );
};

export default Page;