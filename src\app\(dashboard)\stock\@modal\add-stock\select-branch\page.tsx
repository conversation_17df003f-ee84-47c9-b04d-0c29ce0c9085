'use client'

import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';

import { ViewBranchesDialog } from '../../../misc/components';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { LoaderModal } from '@/components/core';

const Page = ({
  searchParams,
}: {
  searchParams: {
    company: string;
    branch: string;
  };
}) => {
  const { company } = searchParams;

  const router = useRouter()

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  const isLoading = isCompanyLoading || isDefaultLoading;

  useEffect(() => {
    if (salesUserDetails?.data.company_id) {
      if (!company || (salesUserDetails?.data.company_id === company && salesUserDetails?.data.branch_id)) {
        return router.replace(`/stock/add-stock/select-option?company=${salesUserDetails?.data.company_id}&companyName=${salesUserDetails?.data.company}&branch=${salesUserDetails?.data.branch_id}`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])

  return (
    <>
      <LoaderModal />
      {!isLoading && (
        <ViewBranchesDialog
          company={company}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          companyList={companies?.results}
          link={'/stock/add-stock/select-option'}
        />
      )}
    </>
  );
};

export default Page;
