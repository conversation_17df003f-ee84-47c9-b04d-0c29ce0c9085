import React, { useEffect, useRef, useState } from 'react';
import JsBarcode from 'jsbarcode';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui';
import { useSearchParams } from 'next/navigation';
import { useGetBarcode, useRegisterBarcode } from '../../api';

// Barcode component
interface BarcodeProps {
    value: string;
}

const Barcode: React.FC<BarcodeProps> = ({ value }) => {
    const canvasRef = useRef<HTMLCanvasElement | null>(null);

    useEffect(() => {
        if (canvasRef.current) {
            JsBarcode(canvasRef.current, value, {
                format: 'CODE128',
                displayValue: true,
                width: 2,
                height: 40,
                margin: 15,
            });
        }
    }, [value]);

    return <canvas data-value={value} ref={canvasRef} />;
};

// Label size options
const labelSizes = [
    { label: '50mm × 25mm (Small Barcode)', width: 50, height: 25 },
    { label: '62mm × 29mm (Brother DK)', width: 62, height: 29 },
    { label: '70mm × 36mm (General)', width: 70, height: 36 },
    { label: '89mm × 36mm (Address)', width: 89, height: 36 },
    { label: '100mm × 50mm', width: 100, height: 50 },
    { label: '100mm × 150mm (Shipping)', width: 100, height: 150 },
    { label: '105mm × 74mm (A6)', width: 105, height: 74 },
];

const BarcodeGenerator: React.FC = () => {
    const searchParams = useSearchParams();
    const company = searchParams.get('company') || '';

    const { data: barcodeData } = useGetBarcode(company);
    const { mutate: registerBarcode } = useRegisterBarcode();

    const [amount, setAmount] = useState<number>(1);
    const [barcodes, setBarcodes] = useState<string[]>([]);
    const [selectedSize, setSelectedSize] = useState(labelSizes[0]);

    const handleGenerate = () => {
        const lastBarcode = barcodeData?.data?.barcode || '0';
        const newBarcodes = Array.from({ length: amount }, (_, i) => {
            const newNumber = Number(lastBarcode) + i + 1;
            return `${newNumber.toString()}`;
        });

        setBarcodes(newBarcodes);

        if (newBarcodes.length > 0) {
            registerBarcode({
                company,
                product_barcode: newBarcodes[newBarcodes.length - 1],
            });
        }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = parseInt(e.target.value, 10);
        setAmount(isNaN(value) ? 1 : Math.min(value, 50));
    };

    const handlePrint = () => {
        const printWindow = window.open('', '_blank');
        if (!printWindow) return;

        const content = barcodes.map(value => {
            const canvas = document.querySelector(`canvas[data-value="${value}"]`) as HTMLCanvasElement;
            const dataUrl = canvas?.toDataURL() || '';

            return `
                <div class="barcode-page">
                    <img src="${dataUrl}" alt="Barcode" />
                </div>
            `;
        }).join('');

        const printContent = `
        <html>
            <head>
                <style>
                    @page {
                        size: ${selectedSize.width}mm ${selectedSize.height}mm;
                        margin: 0;
                    }
                    body {
                        margin: 0;
                        font-family: Arial, sans-serif;
                    }
                    .barcode-page {
                        width: ${selectedSize.width}mm;
                        height: ${selectedSize.height}mm;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        box-sizing: border-box;
                        page-break-after: always;
                    }
                    img {
                        max-width: 95%;
                        max-height: 100%;
                    }
                </style>
            </head>
            <body>
                ${content}
                <script>
                    window.onload = () => window.print();
                </script>
            </body>
        </html>
        `;

        printWindow.document.write(printContent);
        printWindow.document.close();
    };

    return (
        <Dialog
            onOpenChange={(open) => {
                if (!open) {
                    setBarcodes([]);
                    setAmount(1);
                }
            }}
        >
            <DialogTrigger asChild>
                <button className="px-4 py-2 bg-primary w-full text-white rounded-md hover:bg-primary/90">
                    Generate Barcodes
                </button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[800px]">
                <DialogHeader>
                    <DialogTitle>Generate Barcodes</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                    <div className="flex flex-col sm:flex-row gap-4 mb-6">
                        <input
                            className="flex-1 px-3 py-2 border rounded-md"
                            max={50}
                            min={1}
                            placeholder="Enter number of barcodes (max: 50)"
                            type="number"
                            value={amount}
                            onChange={handleChange}
                        />

                        <select
                            className="flex-1 px-3 py-2 border rounded-md"
                            value={JSON.stringify(selectedSize)}
                            onChange={(e) => {
                                const size = JSON.parse(e.target.value);
                                setSelectedSize(size);
                            }}
                        >
                            {labelSizes.map((size, i) => (
                                <option key={i} value={JSON.stringify(size)}>
                                    {size.label}
                                </option>
                            ))}
                        </select>

                        <button
                            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
                            onClick={handleGenerate}
                        >
                            Generate
                        </button>
                    </div>

                    <div className="grid grid-cols-2 gap-4 max-h-[400px] overflow-y-auto">
                        {barcodes.map((value, index) => (
                            <div className="p-4 border rounded-md flex flex-col items-center w-full" key={index}>
                                <div data-value={value}>
                                    <Barcode value={value} />
                                </div>
                                <p className="text-center mt-2">{value}</p>
                            </div>
                        ))}
                    </div>
                    {barcodes.length > 0 && (
                        <div className="mt-4">
                            <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                                <p className="text-sm text-blue-800">
                                    <strong>Print Instructions:</strong> When the print dialog opens, make sure to select <strong>&quot;Portrait&quot;</strong> orientation and the right paper size for proper horizontal barcode printing.
                                </p>
                            </div>
                            <div className="flex justify-end">
                                <button
                                    className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
                                    onClick={handlePrint}
                                >
                                    Print Barcodes
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default BarcodeGenerator;
