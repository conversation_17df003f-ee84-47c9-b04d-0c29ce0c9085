'use client';
import React, { useState, useEffect } from 'react';
import { Input, Button } from '@/components/core';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';

interface TokenInputProps {
  onTokenChange: (tokens: number, totalAmount: number) => void;
  initialTokens?: number;
  className?: string;
  disabled?: boolean;
}

const TokenInput: React.FC<TokenInputProps> = ({
  onTokenChange,
  initialTokens = 0,
  className = '',
  disabled = false
}) => {
  const [tokens, setTokens] = useState<number>(initialTokens);
  const TOKEN_RATE = 10; // 1 token = 10 naira

  const totalAmount = tokens * TOKEN_RATE;

  useEffect(() => {
    onTokenChange(tokens, totalAmount);
  }, [tokens, totalAmount, onTokenChange]);

  const handleTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    if (value >= 0) {
      setTokens(value);
    }
  };

  const incrementTokens = () => {
    setTokens(prev => prev + 1);
  };

  const decrementTokens = () => {
    setTokens(prev => Math.max(0, prev - 1));
  };

  const addPresetTokens = (amount: number) => {
    setTokens(prev => prev + amount);
  };

  return (
    <div className={`bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200 shadow-lg ${className}`}>
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-gray-800 mb-2">Purchase Tokens</h3>
        <p className="text-sm text-gray-600">1 Token = ₦10</p>
      </div>

      {/* Token Input Section */}
      <div className="mb-6">
        <label className="block text-sm font-semibold text-gray-700 mb-3">
          Number of Tokens
        </label>
        <div className="flex items-center justify-center space-x-3">
          <Button
            type="button"
            onClick={decrementTokens}
            disabled={disabled || tokens <= 0}
            className="w-10 h-10 rounded-full bg-blue-600 hover:bg-blue-700 text-white font-bold text-lg flex items-center justify-center disabled:opacity-50"
          >
            -
          </Button>
          
          <div className="relative">
            <Input
              type="number"
              value={tokens}
              onChange={handleTokenChange}
              disabled={disabled}
              min="0"
              className="w-32 text-center text-lg font-bold border-2 border-blue-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
              placeholder="0"
            />
          </div>
          
          <Button
            type="button"
            onClick={incrementTokens}
            disabled={disabled}
            className="w-10 h-10 rounded-full bg-blue-600 hover:bg-blue-700 text-white font-bold text-lg flex items-center justify-center"
          >
            +
          </Button>
        </div>
      </div>

      {/* Quick Add Buttons */}
      <div className="mb-6">
        <p className="text-sm font-medium text-gray-700 mb-3 text-center">Quick Add</p>
        <div className="flex justify-center space-x-2">
          {[10, 50, 100, 500].map((amount) => (
            <Button
              key={amount}
              type="button"
              onClick={() => addPresetTokens(amount)}
              disabled={disabled}
              className="px-3 py-1 text-xs bg-white border border-blue-300 text-blue-600 hover:bg-blue-50 rounded-md font-medium"
            >
              +{amount}
            </Button>
          ))}
        </div>
      </div>

      {/* Total Amount Display */}
      <div className="bg-white rounded-lg p-4 border-2 border-blue-300 shadow-inner">
        <div className="text-center">
          <p className="text-sm text-gray-600 mb-1">Total Amount</p>
          <p className="text-2xl font-bold text-blue-600">
            {convertNumberToNaira(totalAmount)}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            {addCommasToNumber(tokens)} tokens × ₦10 each
          </p>
        </div>
      </div>

      {/* Token Benefits */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-600">
          Tokens can be used across all modules and never expire
        </p>
      </div>
    </div>
  );
};

export default TokenInput;
