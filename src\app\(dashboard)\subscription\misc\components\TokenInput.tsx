'use client';
import React, { useState, useEffect } from 'react';
import { Input, Button } from '@/components/core';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';

interface TokenInputProps {
  onTokenChange: (tokens: number, totalAmount: number) => void;
  initialTokens?: number;
  className?: string;
  disabled?: boolean;
}

const TokenInput: React.FC<TokenInputProps> = ({
  onTokenChange,
  initialTokens = 0,
  className = '',
  disabled = false
}) => {
  const [tokens, setTokens] = useState<number>(initialTokens);
  const TOKEN_RATE = 10; // 1 token = 10 naira

  const totalAmount = tokens * TOKEN_RATE;

  useEffect(() => {
    onTokenChange(tokens, totalAmount);
  }, [tokens, totalAmount, onTokenChange]);

  const handleTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    if (value >= 0) {
      setTokens(value);
    }
  };

  const incrementTokens = () => {
    setTokens(prev => prev + 1);
  };

  const decrementTokens = () => {
    setTokens(prev => Math.max(0, prev - 1));
  };

  const addPresetTokens = (amount: number) => {
    setTokens(prev => prev + amount);
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 shadow-sm ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-[#032282] mb-2">Token Purchase</h3>
        <p className="text-sm text-gray-600">1 Token = ₦10</p>
      </div>

      {/* Token Input Section */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Number of Tokens
        </label>
        <Input
          type="number"
          value={tokens || ''}
          onChange={handleTokenChange}
          disabled={disabled}
          min="0"
          className="w-full text-lg font-medium border border-gray-300 rounded-lg focus:border-[#032282] focus:ring-1 focus:ring-[#032282]"
          placeholder="Enter token amount"
        />
      </div>

      {/* Total Amount Display */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-600">Total Amount:</span>
          <span className="text-xl font-bold text-[#032282]">
            {convertNumberToNaira(totalAmount)}
          </span>
        </div>
        <p className="text-xs text-gray-500 mt-1 text-right">
          {addCommasToNumber(tokens)} tokens × ₦10 each
        </p>
      </div>
    </div>
  );
};

export default TokenInput;
