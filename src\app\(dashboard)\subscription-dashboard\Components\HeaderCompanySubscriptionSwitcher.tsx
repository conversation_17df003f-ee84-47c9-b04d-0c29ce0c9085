'use client';

import { usePathname, useSearchParams } from 'next/navigation';
import * as React from 'react';

import {
    LinkButton,
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/core';
import { useBooleanStateControl, useRouteChangeEvent } from '@/hooks';
import { useCompanies } from '@/app/(dashboard)/stock/misc/api';
import { Coins } from 'lucide-react';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';


const pathsWithCompanySwitcher = ['/SubscriptionDashboard'];
const pathsWithoutCompanySwitcher = [
    '/stock/companies',
];

export function HeaderCompanySubscriptionSwitcher() {
    const { data: companiesResponse } = useCompanies();

    const pathname = usePathname();
    const params = useSearchParams();
    const companyId = params.get('company');
    const companyName = params.get('companyName');

    const {
        state: isPopoverOpen,
        setState: setPopoverState,
        setFalse: closePopover,
    } = useBooleanStateControl();

    useRouteChangeEvent(() => closePopover());

    const hasSwitcher = React.useMemo(
        () =>
            pathsWithCompanySwitcher.some(page => pathname.includes(page)),
        [pathname]
    );

    const hasNoSwitcher = React.useMemo(
        () =>
            pathsWithoutCompanySwitcher.includes(pathname),
        [pathname]
    );

    const shouldShowSwitcher = hasSwitcher && !hasNoSwitcher;

    const { results: companies } = companiesResponse || {};
    const sortedCompanies = companies?.sort((a, b) => {
        if (a.default && !b.default) return -1;
        if (!a.default && b.default) return 1;
        if (a.id === companyId) return -1;
        if (b.id === companyId) return 1;
        return 0;
    });

    const derivedCompanyName = React.useMemo(() => {
        if (companyName) return companyName;

        const company = companies?.find(company => company.id === companyId);
        if (company) return company.company_name;

        const defaultCompany = companies?.find(company => company.default);
        return defaultCompany?.company_name || 'No Company Selected';
    }, [companyId, companyName, companies]);

    // const companyNameAlt = companyName || companies?.find(company => company.id === companyId)?.company_name

    return !!companies && shouldShowSwitcher ? (
        <Popover open={isPopoverOpen} onOpenChange={setPopoverState}>
            <PopoverTrigger className="rounded-10 bg-white py-1 text-sm">
                <span className="rounded-lg bg-blue-700/10 px-3 py-1.5 text-xs text-main-solid md:bg-main-bg">
                    Switch Company: <span className='uppercase font-semibold'>{derivedCompanyName}</span>
                </span>

            </PopoverTrigger>
            <PopoverContent
                align="start"
                className="mt-2 w-full rounded-10 border-none p-0 pb-2 text-xs [@media(min-width:408px)]:max-w-[18.375rem]"
            >
                <div className='py-[11px] px-4 bg-[#EBF1FF] mt-3 flex items-center justify-between gap-2 w-full'>
                    <p className='text-main-solid text-xxs'>Tap to <span className='font-bold'>change</span> default company</p>

                    <LinkButton className='bg-[#DAE3FF] rounded-xl py-[5px] px-4 text-main-solid text-xxs' href={'/stock/set-company-branch-stock'}>Change</LinkButton>
                </div>
                <p className="px-4 pb-2.5 pt-4">Select company to change view.</p>
                <ul className="space-y-1 px-2">
                    {sortedCompanies?.map(({ id, company_name, default: isDefaultCompany, token_balance_info }) => (
                        <li key={id}>
                            <LinkButton
                                className="flex rounded-[.75rem] px-6 py-2.5 text-left text-label-text transition ease-in-out hover:bg-[#F8FAFF] justify-between"
                                href={`${pathname}?company=${id}&companyName=${company_name || ''}`}
                                size="lg"
                                variant="white"
                            >
                                <span>{company_name} </span>
                                {isDefaultCompany && <span className='bg-main-bg text-main-solid py-1 px-2 rounded-xl'>default</span>}
                            </LinkButton>
                            {/* Token Balance Display */}
                            {token_balance_info && (
                                <div className="flex items-center gap-2 mt-1">
                                    <div className="flex items-center gap-1">
                                        <Coins className="h-3 w-3 text-[#032282]" />
                                        <span className="text-xs font-medium text-[#032282]">
                                            {addCommasToNumber(token_balance_info.current_balance)} tokens
                                        </span>
                                    </div>
                                    <span className="text-xs text-gray-500">
                                        ({convertNumberToNaira(token_balance_info.current_balance * 10)})
                                    </span>

                                </div>
                            )}

                        </li>
                    ))}
                </ul>
            </PopoverContent>
        </Popover>
    ) : null;
}
