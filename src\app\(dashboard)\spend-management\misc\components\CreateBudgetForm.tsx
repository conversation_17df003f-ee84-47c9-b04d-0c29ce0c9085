'use client';

import { Label } from '@radix-ui/react-label';
import { PaginationState } from '@tanstack/react-table';
import type { AxiosError } from 'axios';
import { format as formatDate } from 'date-fns';
import { useSearchParams } from 'next/navigation';
import React, { useState } from 'react';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import {
  Button,
  ErrorModal,
  FormError,
  Input,
  LinkButton,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SingleDatePicker,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { SmallSpinner } from '@/icons/core';
import { formatAxiosErrorMessage } from '@/utils/errors';

import { useCompanies, useCreateBudget } from '../api';
import { Companies } from '../types';
import { SuccessModal } from './SuccessModal';
import { zodResolver } from '@hookform/resolvers/zod';

interface CreateBudgetFormProps {
  companiesResponse: Companies;
}


interface Data {
  id: number;
  team: string;
  company: string;
  budget_name: string;
  budget_amount: number;
  budget_type: string;
  start_date: string;
  end_date: string;
}
const dateFormat = 'yyyy-MM-dd';

function dateTransformer(value: Date) {
  const parsedDate = new Date(value);
  return formatDate(parsedDate, dateFormat);
}

// ALLOW TODAYS DATE FLY
const yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);

const CreateBudgetFormSchema = z.object({
  company: z
    .string({ required_error: 'Please select a company.' })
    .trim()
    .min(1, { message: 'Please select a company.' }),
  team: z.string({ required_error: 'Please select a team.' }).trim().optional(),
  budget_name: z
    .string({ required_error: 'Please enter a budget name.' })
    .trim()
    .min(1, { message: 'Please enter a budget name.' }),
  budget_amount: z
    .string({ required_error: 'Please enter a budget amount.' })
    .regex(/^\d+$/, { message: 'This must be a number.' })
    .trim()
    .min(1, { message: 'Please enter a budget amount.' }),
  start_date: z.coerce
    .date({
      required_error: 'The start date is required.',
      invalid_type_error: 'The start date must be a valid date.',
    })
    .refine(data => data > yesterday, {
      message: 'The start date must be in the future.',
    })
    .transform(dateTransformer),
  end_date: z.coerce
    .date({
      required_error: 'The end date is required.',
      invalid_type_error: 'The end date must be a valid date.',
    })
    .refine(data => data > new Date(), {
      message: 'The end date must be in the future.',
    })
    .transform(dateTransformer),
});

export type CreateBudgetFormValues = z.infer<typeof CreateBudgetFormSchema>;

export function CreateBudgetForm({ companiesResponse }: CreateBudgetFormProps) {
  const [{ pageIndex, pageSize }, _setPagination] =
    React.useState<PaginationState>({
      pageIndex: 1,
      pageSize: 100,
    });

  const fetchOptions = {
    pageIndex,
    pageSize,
  };

  const { data } = useCompanies(fetchOptions, companiesResponse);

  const { results: companies, count: companiesCount } = data || {};

  const noCompaniesAvailable =
    !companiesCount || (!!companiesCount && companiesCount < 1);

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const { mutate: createBudget, isLoading: isCreateBudgetLoading } =
    useCreateBudget();

  // const router = useRouter();
  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
  } = useBooleanStateControl();

  const searchParams = useSearchParams();
  const urlCompanyId = searchParams.get('companyId') || undefined;
  const urlCompanyName = searchParams.get('companyName') || undefined;
  const companyName = decodeURIComponent(urlCompanyName || '');

  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<CreateBudgetFormValues>({
    resolver: zodResolver(CreateBudgetFormSchema),
    defaultValues: {
      company: urlCompanyId,
    },
  });

  const [budgetData, setBudgetData] = useState<Data>()
  const selectedCompanyId = useWatch({ control, name: 'company' });
  const selectedCompanyObject = companies?.find(
    company => company.id === selectedCompanyId
  );

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  const selectedCompanyTeams = selectedCompanyObject?.teams;
  const selectedCompanyHasTeams =
    !!selectedCompanyTeams && selectedCompanyTeams?.length > 0;

  const onCreateBudgetSubmit = (submittedData: CreateBudgetFormValues) => {
    createBudget(
      { ...submittedData, budget_type: 'COMPANY' },
      {
        onSuccess: ({ data }) => {
          openSuccessModal();
          setBudgetData(data as Data)

          // router.push(
          // `/spend-management/budgeting/create-company-budget/add-categories?id=${data.id}&amount=${data.budget_amount}&name=${data.budget_name}&companyId=${selectedCompanyId}`
          // );
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );
  };

  return (
    <>
      <form
        className="min-h-[calc(100vh-13.25rem)] bg-requisition-single-company-bg-gradient px-6 pt-6 md:min-h-0 md:max-w-[34rem] md:rounded-[1.125rem] md:p-10 md:pb-20"
        onSubmit={handleSubmit(onCreateBudgetSubmit)}
      >
        <div className="space-y-4">
          {noCompaniesAvailable && (
            <div className="flex gap-4 rounded-lg bg-red-100/60 p-4">
              <svg
                className="mt-1 shrink-0"
                fill="none"
                height={20}
                viewBox="0 0 20 20"
                width={20}
                xmlns="http://www.w3.org/2000/svg"
              >
                <g
                  clipPath="url(#a)"
                  stroke="#7F1D1D"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path
                    d="M10 18.333c4.583 0 8.333-3.75 8.333-8.333S14.583 1.667 10 1.667 1.667 5.417 1.667 10s3.75 8.333 8.333 8.333Zm0-11.666v4.166"
                    strokeWidth={1.5}
                  />
                  <path d="M9.995 13.333h.008" strokeWidth={2} />
                </g>
                <defs>
                  <clipPath id="a">
                    <path d="M0 0h20v20H0z" fill="#fff" />
                  </clipPath>
                </defs>
              </svg>

              <div className="space-y-2">
                <p className="w-full max-w-[70%] text-sm text-red-900">
                  You need to create a company before creating a budget.
                </p>
                <LinkButton
                  className="bg-red-800 text-xs"
                  href="/spend-management/companies/create-company"
                >
                  Create company
                </LinkButton>{' '}
              </div>
            </div>
          )}

          {!!urlCompanyId ? (
            <p className="cursor-not-allowed rounded-md bg-blue-100 px-5 py-2.5 text-xs text-main-solid">
              Company name: <span className="font-bold">{companyName}</span>
            </p>
          ) : (
            <div>
              <Label
                className="mb-1 block text-xs text-label-text"
                htmlFor="company_id"
              >
                Company
              </Label>

              <Controller
                control={control}
                name="company"
                render={({ field: { onChange, value, ref } }) => (
                  <Select
                    disabled={noCompaniesAvailable}
                    value={value}
                    onValueChange={onChange}
                  >
                    <SelectTrigger
                      className="bg-white"
                      id="company_id"
                      ref={ref}
                    >
                      <SelectValue placeholder="Select company" />
                    </SelectTrigger>
                    <SelectContent>
                      {companies?.map(({ company_name, id }) => {
                        return (
                          <SelectItem key={id} value={id}>
                            {company_name}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                )}
              />

              {errors?.company && (
                <FormError errorMessage={errors.company.message} />
              )}
            </div>
          )}

          {!!selectedCompanyId && (
            <div>
              <Label
                className="mb-1 block text-xs text-label-text"
                htmlFor="team_id"
              >
                Team
              </Label>

              {selectedCompanyHasTeams ? (
                <Controller
                  control={control}
                  name="team"
                  render={({ field: { onChange, value, ref } }) => (
                    <Select value={value} onValueChange={onChange}>
                      <SelectTrigger
                        className="bg-white"
                        id="team_id"
                        ref={ref}
                      >
                        <SelectValue placeholder="Select team" />
                      </SelectTrigger>

                      <SelectContent>
                        {
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          selectedCompanyTeams?.map(({ team_name, id }) => {
                            return (
                              <SelectItem key={id} value={id}>
                                {team_name}
                              </SelectItem>
                            );
                          })}
                      </SelectContent>
                    </Select>
                  )}
                />
              ) : (
                <div className="flex flex-wrap items-center justify-between gap-2 rounded-md bg-red-100/60 px-5 py-2.5 text-xs text-main-solid">
                  <p className="inline text-red-900">
                    The company you selected has no teams.
                  </p>{' '}
                  <LinkButton
                    className="bg-red-800 px-3 py-1.5 text-xxs text-white"
                    href={`/spend-management/teams/create-team?companyId=${selectedCompanyObject?.id}&companyName=${selectedCompanyObject?.company_name}`}
                    size="unstyled"
                  >
                    Add a team
                  </LinkButton>
                </div>
              )}

              {errors?.team && selectedCompanyHasTeams && (
                <FormError errorMessage={errors.team.message} />
              )}
            </div>
          )}

          <div>
            <Label
              className="mb-1 block text-xs text-label-text"
              htmlFor="budget_name"
            >
              Budget name
            </Label>

            <Input
              autoCapitalize="none"
              autoComplete="off"
              autoCorrect="off"
              className="bg-white"
              id="budget_name"
              placeholder="Enter budget name"
              type="text"
              {...register('budget_name')}
            />

            {errors?.budget_name && (
              <FormError errorMessage={errors.budget_name.message} />
            )}
          </div>

          <div>
            <Label
              className="mb-1 block text-xs text-label-text"
              htmlFor="budget_amount"
            >
              Amount
            </Label>

            <Input
              autoCapitalize="none"
              autoComplete="off"
              autoCorrect="off"
              className="bg-white"
              id="budget_amount"
              placeholder="Enter amount"
              type="number"
              {...register('budget_amount')}
            />

            {errors?.budget_amount && (
              <FormError errorMessage={errors.budget_amount.message} />
            )}
          </div>

          <div>
            <Label
              className="mb-1 block text-xs text-label-text"
              htmlFor="startDate"
            >
              Start and end dates
            </Label>

            <div className="flex gap-3">
              <Controller
                control={control}
                name="start_date"
                render={({ field: { onChange, value } }) => (
                  <SingleDatePicker
                    className="bg-white py-2.5"
                    id="startDate"
                    placeholder="Start date"
                    value={value}
                    onChange={onChange}
                  />
                )}
              />

              <Controller
                control={control}
                name="end_date"
                render={({ field: { onChange, value } }) => (
                  <SingleDatePicker
                    className="bg-white py-2.5"
                    id="endDate"
                    placeholder="End date"
                    value={value}
                    onChange={onChange}
                  />
                )}
              />
            </div>

            {errors?.start_date && (
              <FormError errorMessage={errors.start_date.message} />
            )}

            {errors?.end_date && (
              <FormError errorMessage={errors.end_date.message} />
            )}
          </div>
        </div>

        <Button
          className="mt-12 flex py-3 text-sm"
          disabled={isCreateBudgetLoading}
          size="fullWidth"
        >
          {isCreateBudgetLoading && (
            <SmallSpinner className="mr-2 animate-spin" />
          )}

          <span>Proceed</span>
        </Button>
      </form>

      <SuccessModal
        heading="Budget created successfully"
        isSuccessModalOpen={isSuccessModalOpen}
        setSuccessModalState={setSuccessModalState}
        subheading="Your team budget have been created successfully."
      >
        <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
          <LinkButton
            className="grow px-1.5 sm:text-sm md:px-6"
            href={`/spend-management/budgeting/edit-categories?id=${budgetData?.id}&amount=${budgetData?.budget_amount}&name=${budgetData?.budget_name}&companyId=${budgetData?.company}`}
            size="lg"
            variant="outlined"
          // onClick={() => setShowCreateSpendTeam(false)}
          // /spend-management/budgeting/edit-categories?id=191&amount=1300000.00&name=Test%20Budget&companyId=b603d5c3-6767-4a62-954b-38ebb97645bd
          >
            Allocate budget
          </LinkButton>

          <LinkButton
            className="grow px-1.5 sm:text-sm md:px-6"
            href={`/spend-management/budgeting/create-company-budget/add-categories?id=${budgetData?.id}&amount=${budgetData?.budget_amount}&name=${budgetData?.budget_name}&companyId=${budgetData?.company} `}
            size="lg"
          // onClick={() => setShowCreateSpendTeam(false)}
          >
            View Budget
          </LinkButton>
        </div>
      </SuccessModal>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
