'use client';

import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { format as formatDate, parseISO, subMonths } from 'date-fns';
import { useSearchParams } from 'next/navigation';
import React from 'react';
import { DateRange } from 'react-day-picker';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { Select } from '@radix-ui/react-select';
import {
  DataTable,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/core';
import { useDebounce } from '@/hooks';
import { cn } from '@/utils/classNames';
import { addCommasToNumber } from '@/utils/numbers';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import { useGetInvoiceItems } from '../../api';
import { InvoiceItemsDto, SingleSaleHistory } from '../../types';
// import { useReactToPrint } from "react-to-print";
// import { useRef } from "react";
import { CSVLink } from 'react-csv';
import { RangeAndCustomDatePicker } from '@/components/core/DatePicker';
const today = new Date();
const aYearAgo = subMonths(new Date(), 12); // literally get all data 

const SalesSingleHistoryTable = ({ }) => {
  const [filterStatus, setFilterStatus] = React.useState("");



  // const componentRef = useRef<HTMLDivElement>(null);
  // const handlePrint = useReactToPrint({
  //   content: () => componentRef.current,
  //   documentTitle: `Sales_History_${Date.now()}`,
  // });

  const columns: ColumnDef<SingleSaleHistory | InvoiceItemsDto>[] = [
    {
      accessorKey: 'id',
      header: 'S/N',
      cell: ({ row }) => {
        return <span>{row.index + 1}</span>;
      },
    },

    {
      accessorKey: 'created_at',
      header: 'Date',
      cell: ({ row }) => {
        const date = parseISO(row.getValue('created_at'));
        const formatted = formatDate(date, 'd/M/yyyy - hh:mma');

        return <span>{formatted}</span>;
      },
    },
    {
      accessorKey: 'item_description',
      header: 'Product Name',
      cell: ({ row }) => {
        const ProductName = row.getValue('item_description') as string;
        return <span>{ProductName}</span>;
      },
    },

    {
      accessorKey: 'cashier',
      header: 'Cashier',
      cell: ({ row }) => {
        const cashier = row.getValue('cashier') as string;
        return <span>{cashier}</span>;
      },
    },

    {
      accessorKey: 'amount',
      header: 'Amount',
      cell: ({ row }) => {
        const amount = row.getValue('amount') as string;
        return <span>₦{addCommasToNumber(Number(amount))}</span>;
      },
    },
    {
      accessorKey: 'quantity',
      header: 'Quantity',
      cell: ({ row }) => {
        const quantity = row.getValue('quantity') as string;
        return <p className="">{quantity}</p>;
      },
    },
    {
      accessorKey: 'payment_method',
      header: 'Payment method',
      cell: ({ row }) => {
        const payment_method = row.getValue('payment_method') as string;
        return <p className="capitalize">{payment_method?.toLowerCase()}</p>;
      },
    },

    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = String(row.getValue('status'));
        const formatted = convertKebabAndSnakeToTitleCase(status);

        return (
          <span
            className={cn(
              (status === 'PENDING' || status === 'IN_PROGRESS' || status === 'SAVED') &&
              'text-[#FF9F2E]',
              status === 'SUCCESSFUL' && 'text-[#00A37D]',
              status === 'NOT_COMPLETED' && 'text-[#073D9F]',
              (status === 'FAILED') && 'text-[#FF0000]'
            )}
          >
            {formatted}
          </span>
        );
      },
    },

    // {
    //   id: 'actions',
    //   header: 'Action',
    //   cell: ({ row }) => {
    //     return (
    //       <Popover>
    //         <PopoverTrigger className="whitespace-nowrap px-4">
    //           <div className="print:hidden flex items-center justify-center rounded-md border-[0.7px] border-[#D6D6D6] px-4 py-2">
    //             <svg
    //               fill="none"
    //               height={20}
    //               viewBox="0 0 4 20"
    //               width={4}
    //               xmlns="http://www.w3.org/2000/svg"
    //             >
    //               <circle cx={2} cy={2} fill="#032282" r={2} />
    //               <circle cx={2} cy={10} fill="#032282" r={2} />
    //               <circle cx={2} cy={18} fill="#032282" r={2} />
    //             </svg>
    //           </div>
    //         </PopoverTrigger>
    //         <PopoverContent className="h-[105px] w-[134px] bg-white px-1 py-2">
    //           <div className="flex w-full flex-col gap-[5px]">
    //             <button
    //               className="flex flex-row items-center rounded-xl px-4 py-2 hover:bg-[#F8FAFF]"
    //               onClick={() => {
    //                 openSingleTxOpen()
    //                 setSingleInvoice(row.original)
    //               }}>
    //               <svg
    //                 fill="none"
    //                 height={20}
    //                 viewBox="0 0 20 20"
    //                 width={20}
    //                 xmlns="http://www.w3.org/2000/svg"
    //               >
    //                 <path
    //                   d="M9.99993 13.6083C8.00827 13.6083 6.3916 11.9916 6.3916 9.99993C6.3916 8.00827 8.00827 6.3916 9.99993 6.3916C11.9916 6.3916 13.6083 8.00827 13.6083 9.99993C13.6083 11.9916 11.9916 13.6083 9.99993 13.6083ZM9.99993 7.6416C8.69993 7.6416 7.6416 8.69993 7.6416 9.99993C7.6416 11.2999 8.69993 12.3583 9.99993 12.3583C11.2999 12.3583 12.3583 11.2999 12.3583 9.99993C12.3583 8.69993 11.2999 7.6416 9.99993 7.6416Z"
    //                   fill="#818181"
    //                 />
    //                 <path
    //                   d="M9.9999 17.5167C6.86657 17.5167 3.90824 15.6834 1.8749 12.5001C0.991569 11.1251 0.991569 8.8834 1.8749 7.50006C3.91657 4.31673 6.8749 2.4834 9.9999 2.4834C13.1249 2.4834 16.0832 4.31673 18.1166 7.50006C18.9999 8.87506 18.9999 11.1167 18.1166 12.5001C16.0832 15.6834 13.1249 17.5167 9.9999 17.5167ZM9.9999 3.7334C7.30824 3.7334 4.73324 5.35007 2.93324 8.17507C2.30824 9.15007 2.30824 10.8501 2.93324 11.8251C4.73324 14.6501 7.30824 16.2667 9.9999 16.2667C12.6916 16.2667 15.2666 14.6501 17.0666 11.8251C17.6916 10.8501 17.6916 9.15007 17.0666 8.17507C15.2666 5.35007 12.6916 3.7334 9.9999 3.7334Z"
    //                   fill="#818181"
    //                 />
    //               </svg>
    //               <p className="ml-2 whitespace-nowrap text-xs text-[#4E4E4E]">
    //                 View more
    //               </p>
    //             </button>
    //             <Link className="flex flex-row items-center rounded-xl px-4 py-2 hover:bg-[#F8FAFF]" href='/sales/return-refund/'>
    //               <svg
    //                 fill="none"
    //                 height={20}
    //                 viewBox="0 0 20 20"
    //                 width={20}
    //                 xmlns="http://www.w3.org/2000/svg"
    //               >
    //                 <path
    //                   d="M9.99984 18.9587C5.05817 18.9587 1.0415 14.942 1.0415 10.0003C1.0415 5.05866 5.05817 1.04199 9.99984 1.04199C14.9415 1.04199 18.9582 5.05866 18.9582 10.0003C18.9582 14.942 14.9415 18.9587 9.99984 18.9587ZM9.99984 2.29199C5.74984 2.29199 2.2915 5.75033 2.2915 10.0003C2.2915 14.2503 5.74984 17.7087 9.99984 17.7087C14.2498 17.7087 17.7082 14.2503 17.7082 10.0003C17.7082 5.75033 14.2498 2.29199 9.99984 2.29199Z"
    //                   fill="#818181"
    //                 />
    //                 <path
    //                   d="M8.8167 12.9837C8.65003 12.9837 8.4917 12.917 8.37503 12.8003L6.0167 10.442C5.77503 10.2003 5.77503 9.80033 6.0167 9.55866C6.25837 9.31699 6.65837 9.31699 6.90003 9.55866L8.8167 11.4753L13.1 7.19199C13.3417 6.95033 13.7417 6.95033 13.9834 7.19199C14.225 7.43366 14.225 7.83366 13.9834 8.07533L9.25837 12.8003C9.1417 12.917 8.98337 12.9837 8.8167 12.9837Z"
    //                   fill="#818181"
    //                 />
    //               </svg>
    //               <p className="ml-2 whitespace-nowrap text-xs text-[#4E4E4E]">
    //                 Return/Refund
    //               </p>
    //             </Link>
    //           </div>
    //         </PopoverContent>
    //       </Popover>
    //     );
    //   },
    // },
  ];
  const { control, } = useForm<{
    searchFilter: string;
    dateFilter: { dateType: string } & DateRange;
  }>({
    defaultValues: {
      searchFilter: '',
      dateFilter: {
        from: aYearAgo,
        to: today,
        dateType: 'CUSTOM',
      },
    },
  });


  const { searchFilter, dateFilter } = useWatch({ control });
  const debouncedSearchFilter = useDebounce(searchFilter, 500);

  const { from, to } = dateFilter || {};
  const startDate = from ? formatDate(from, 'yyyy-MM-dd') : '';
  const endDate = to ? formatDate(to, 'yyyy-MM-dd') : '';

  const [{ pageIndex, pageSize }, setPagination] =
    React.useState<PaginationState>({
      pageIndex: 1,
      pageSize: 200,
    });

  const searchParams = useSearchParams();

  const companyId = searchParams.get('company') as string;
  const branchId = searchParams.get('branch') as string;
  const fetchOptions = {
    companyId,
    branchId,
    pageIndex,
    pageSize,
    startDate,
    endDate,
    result_type: dateFilter?.dateType as string,
    transaction_status: filterStatus,
    search: debouncedSearchFilter,
  };


  // const fetchingOptions = {
  //   companyId: company,
  //   branchId: branch,
  //   pageIndex: 1,
  //   pageSize: 100,
  // }
  const { data, isFetching, isLoading } = useGetInvoiceItems(fetchOptions)
  // console.log(data);


  const { transaction_items: rows, total_count } = data?.data || {};

  const pageCount = React.useMemo(() => {
    if (!total_count) {
      return -1;
    }
    const result = Math.ceil(total_count / pageSize);
    const remainingItems = total_count % pageSize;

    if (remainingItems > 0) {
      return result + 1;
    }

    return result;
  }, [total_count, pageSize]);

  return (
    <>
      <div className="mb-4 flex w-full flex-wrap items-center justify-between  gap-4 rounded-10 bg-[#ECF1FF] p-6 lg:py-2">
        <div className='flex'>

          <div className="">
            <Controller
              control={control}
              name="dateFilter"
              render={({ field: { onChange, value } }) => (
                <RangeAndCustomDatePicker
                  // allDataFilter={dateFilter}
                  className="max-w-[16.1875rem] border border-[#d6d6d6]/50 bg-white px-4 py-3 text-sm"
                  id="dateFilter"
                  placeholder="Select a date range"
                  placeholderClassName="text-[#556575]"
                  value={value}
                  onChange={onChange}
                />
              )}
            />
          </div>
        </div>

        <div className='flex items-center space-x-2'>
          <Select
            value={filterStatus}
            onValueChange={e => {
              setFilterStatus(e);
            }}

          >
            <SelectTrigger
              className="bg-[#FFFFFF] text-sm !font-semibold capitalize !text-[#032282]"
              id="status"
            >
              <div className="flex items-center">
                <p className="text-xs text-[#566AAA]">Status</p>

                <SelectValue className='!text-sm !font-normal ml-2' placeholder="Select status" />
              </div>
            </SelectTrigger>
            <SelectContent className="">
              {[
                { key: "Successful", value: "SUCCESSFUL" },
                { key: "In Progress", value: "IN_PROGRESS" },
                { key: "Pending", value: "PENDING" },
                { key: "Credit", value: "CREDIT" },
                { key: "Not Completed", value: "NOT_COMPLETED" },
                { key: "Cancelled", value: "CANCELLED" },
                { key: "Failed", value: "FAILED" },
                { key: "All", value: "" },
              ]?.map((v, id) => {
                return (
                  <SelectItem key={id} value={v.value}>
                    {v.key}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
          <CSVLink
            className="rounded-md border border-[#063898] w-40 whitespace-nowrap px-4 py-2 text-sm text-[#063898]"
            data={rows ?? []}
            filename={`Sales_History_${new Date()}`}

          >  Export Data</CSVLink>
        </div>
      </div>



      <div
      // ref={componentRef}
      >
        <DataTable
          columns={columns}
          isFetching={isFetching}
          isLoading={isLoading}
          pageCount={pageCount}
          pageIndex={pageIndex}
          pageSize={pageSize}
          rows={rows}
          setPagination={setPagination}
        />
      </div>
    </>
  );
};

export default SalesSingleHistoryTable;
