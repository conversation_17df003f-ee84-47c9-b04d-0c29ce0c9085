import { useQuery } from '@tanstack/react-query';

import { managementAxios } from '@/lib/axios';

import { TeamsResponse } from '../types';

// THIS NEEDS TO BE CHECKEF
interface FetchTeamsOptions {
  companyId: string;
  //   branchId: string | null;
  pageIndex: number;
  pageSize: number;
  startDate: string | undefined;
  endDate: string | undefined;
  search?: string;
}

export const getTeams = async ({
  companyId,
  //   branchId,
  pageIndex,
  pageSize,
  startDate,
  endDate,
  search,
}: FetchTeamsOptions): Promise<TeamsResponse> => {
  const response = await managementAxios.get(
    `/req/get-teams/?company=${companyId}&page=${pageIndex}&size=${pageSize}&timestamp_gte=${startDate}&timestamp_lte=${endDate}&search=${search}`
  );

  return response.data; //as StockTableResponse;
};

export const useGetTeams = (fetchOptions: FetchTeamsOptions) => {
  return useQuery({
    queryKey: ['all-teams', fetchOptions],
    queryFn: () => {
      if (fetchOptions) return getTeams(fetchOptions);
    },
    // FINALLY FOUND YOU 
    // DISABLED UNTIL USE IS FOUND
    enabled: true,
    keepPreviousData: true,
    refetchOnWindowFocus: false,
  });
};
