'use client'

import { redirect } from 'next/navigation';
import React from 'react';

import { CreateTeamModal } from '../../misc/components/modals';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { LoaderModal } from '@/components/core';

export default function CreateTeam() {
  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();

  if (companies?.count as number < 1) {
    redirect('/spend-management/companies/create-company');
  }

  return (
    <>
      <LoaderModal isOpen={isCompanyLoading} />
      <CreateTeamModal
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies?.results || []}
      />
    </>
  );
}
