'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ErrorModal, Input, ScrollArea } from '@/components/core';
import toast from 'react-hot-toast';
import { usePostCreateLeaveType } from '../../api/postCreateLeaveType';
import { useErrorModalState } from '@/hooks';
import { SmallSpinner } from '@/icons/core';
import { useLeaveType } from '../../api/getLeaveType';
import { useUpdateCreateLeaveType } from '../../api/updateCreateLeaveType';
import { useDeleteCreateLeaveType } from '../../api/deleteCreateLeaveType';

interface LeaveType {
    id: string;
    company: string;
    title: string;
    created_by: string | null;
}

export type LeaveTypesProps = {
    userId: string;
    company_id: string;
    company_name: string;
};

function LeaveTypes({ company_id }: LeaveTypesProps) {
    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();

    const [leaveName, setLeaveName] = useState('');
    // const [duration, setDuration] = useState('');
    // const [description, setDescription] = useState('');
    const [editingId, setEditingId] = useState<string | null>(null);

    const { mutate: postCreateLeaveType, isLoading: isPostCreateLeaveTypeLoading } =
        usePostCreateLeaveType();

    const { mutate: updateCreateLeaveType, isLoading: isUpdateCreateLeaveTypeLoading } =
        useUpdateCreateLeaveType();

    const { mutate: deleteCreateLeaveType, isLoading: _isDeleteCreateLeaveTypeLoading } =
        useDeleteCreateLeaveType();

    const { data: getLeaveTypesData, isLoading: isGetLeaveTypesLoading } = useLeaveType({
        companyid: company_id,
    });

    const leaveTypes = getLeaveTypesData?.results || [];

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!leaveName.trim()) {
            toast.error("Please enter a leave name");
            return;
        }

        if (editingId) {
            // Update existing leave type
            updateCreateLeaveType(
                {
                    dataDto: { title: leaveName },
                    company_id: company_id,
                    leave_type_id: editingId,
                },
                {
                    onSuccess: () => {
                        toast.success("Leave type updated successfully");
                        setLeaveName('');
                        // setDuration('');
                        // setDescription('');
                        setEditingId(null);
                    },
                    onError: error => {
                        const errorMessage =
                            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                            //@ts-ignore
                            error.response?.data?.error || "An error occurred";
                        openErrorModalWithMessage(errorMessage);
                    },
                }
            );
        } else {
            // Create new leave type
            postCreateLeaveType(
                {
                    dataDto: { title: leaveName },
                    company_id: company_id,
                },
                {
                    onSuccess: () => {
                        toast.success("Leave type created successfully");
                        setLeaveName('');
                        // setDuration('');
                        // setDescription('');
                    },
                    onError: error => {
                        const errorMessage =
                            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                            //@ts-ignore
                            error.response?.data?.error || "An error occurred";
                        openErrorModalWithMessage(errorMessage);
                    },
                }
            );
        }
    };

    const handleEdit = (leave: LeaveType) => {
        setEditingId(leave.id);
        setLeaveName(leave.title);
        // setDuration(''); // Reset duration as it's not in the API response
        // setDescription(''); // Reset description as it's not in the API response
    };

    // Note: You'll need to implement the delete API endpoint and integration
    const handleDelete = (id: string) => {
        // TODO: Implement delete functionality with API

        deleteCreateLeaveType(
            {
                leave_type_id: id,
                company_id: company_id,
            },
            {
                onSuccess: () => {
                    toast.success("Leave type deleted successfully");
                    setLeaveName('');
                    // setDuration('');
                    // setDescription('');
                },
                onError: error => {
                    const errorMessage =
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        error.response?.data?.error || "An error occurred";
                    openErrorModalWithMessage(errorMessage);
                },
            }
        );
    };

    return (
        <div className="max-w-7xl mx-auto px-4 py-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Form Section */}
                <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                    <h2 className="text-2xl font-semibold mb-1">
                        {editingId ? 'Edit leave type' : 'Create leave type'}
                    </h2>
                    <p className="text-gray-500 text-sm mb-6">
                        Enter leave name, description to give employee more detail of this leave type and provide a duration
                    </p>

                    <form className="space-y-6" onSubmit={handleSubmit}>
                        <div>
                            <label className="text-sm font-medium mb-2 block">Leave name</label>
                            <Input
                                className="text-[#818181] bg-white border-[#E9E9E9] border-[0.8px] h-[48px]"
                                placeholder="Enter leave name"
                                value={leaveName}
                                onChange={(e) => setLeaveName(e.target.value)}
                            />
                        </div>

                        {/* <div>
                            <label className="text-sm font-medium mb-2 block">Duration</label>
                            <div className="flex gap-4">
                                <Select value={duration} onValueChange={setDuration}>
                                    <SelectTrigger
                                        className="text-[#818181] bg-white border-[#E9E9E9] border-[0.8px] h-[48px] w-[140px]">
                                        <SelectValue placeholder="Select" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="1">1 day</SelectItem>
                                        <SelectItem value="2">2 days</SelectItem>
                                        <SelectItem value="3">3 days</SelectItem>
                                        <SelectItem value="5">5 days</SelectItem>
                                        <SelectItem value="7">7 days</SelectItem>
                                    </SelectContent>
                                </Select>
                                <Input
                                    className="text-[#818181] bg-white border-[#E9E9E9] border-[0.8px] h-[48px]"
                                    placeholder="Enter number of days"
                                    type="number"
                                    value={duration}
                                    onChange={(e) => setDuration(e.target.value)}
                                />
                            </div>
                        </div>

                        <div>
                            <label className="text-sm font-medium mb-2 block">Description</label>
                            <Textarea
                                className="text-[#818181] bg-white border-[#E9E9E9] border-[0.8px] min-h-[120px]"
                                placeholder="Enter description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                            />
                        </div> */}

                        <Button
                            className="w-full bg-primary hover:bg-primary/90 text-white leave-button"
                            disabled={isPostCreateLeaveTypeLoading || isUpdateCreateLeaveTypeLoading}
                            type="submit"
                        >
                            <span className="inline-block">
                                {isPostCreateLeaveTypeLoading || isUpdateCreateLeaveTypeLoading && (
                                    <SmallSpinner className="mr-2 animate-spin" />
                                )}
                            </span>
                            <span className="inline-block">{editingId ? 'Update' : 'Save'}</span>
                        </Button>

                        {editingId && (
                            <Button
                                className="w-full leave-button"
                                type="button"
                                variant="ghost"
                                onClick={() => {
                                    setEditingId(null);
                                    setLeaveName('');
                                    // setDuration('');
                                    // setDescription('');
                                }}
                            >
                                Cancel Edit
                            </Button>
                        )}
                    </form>
                </div>

                {/* List Section */}
                <div className="bg-[#f8f9fbe0] rounded-xl shadow-sm p-6 border-[0.3px] border-[#0323824c] border-dashed">
                    <h2 className="text-2xl font-semibold mb-1">Created leaves</h2>
                    <p className="text-gray-500 text-sm mb-6">
                        Your saved settings will appear here which you can edit or delete
                    </p>

                    <ScrollArea className="h-[400px] pr-4">
                        <div className="space-y-4">
                            {isGetLeaveTypesLoading ? (
                                <div className="flex justify-center items-center h-32">
                                    <SmallSpinner className="animate-spin" />
                                </div>
                            ) : (
                                leaveTypes.map((leave) => (
                                    <div
                                        className={`leave-item flex items-center justify-between p-4 rounded-[10px] ${editingId === leave.id
                                            ? 'border-[#5879fd84] border-[0.5px] bg-white'
                                            : ' bg-white'
                                            }`}
                                        key={leave.id}
                                    >
                                        <div className="flex items-center gap-3">
                                            <div className='flex items-center gap-3'>
                                                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                                                    <svg fill="none" height="32" viewBox="0 0 32 32" width="32" xmlns="http://www.w3.org/2000/svg">
                                                        <circle cx="16" cy="16" fill="#3E8E7B" fillOpacity="0.1" r="16" />
                                                        <path d="M15.2073 19.3017C15.0807 19.3017 14.954 19.2551 14.854 19.1551L13.854 18.1551C13.6607 17.9617 13.6607 17.6417 13.854 17.4484C14.0473 17.2551 14.3673 17.2551 14.5607 17.4484L15.2073 18.0951L17.5207 15.7817C17.714 15.5884 18.034 15.5884 18.2273 15.7817C18.4207 15.9751 18.4207 16.2951 18.2273 16.4884L15.5607 19.1551C15.4673 19.2551 15.334 19.3017 15.2073 19.3017Z" fill="#032282" />
                                                        <path d="M17.334 12.4987H14.6673C14.0273 12.4987 12.834 12.4987 12.834 10.6654C12.834 8.83203 14.0273 8.83203 14.6673 8.83203H17.334C17.974 8.83203 19.1673 8.83203 19.1673 10.6654C19.1673 11.3054 19.1673 12.4987 17.334 12.4987ZM14.6673 9.83203C14.0073 9.83203 13.834 9.83203 13.834 10.6654C13.834 11.4987 14.0073 11.4987 14.6673 11.4987H17.334C18.1673 11.4987 18.1673 11.3254 18.1673 10.6654C18.1673 9.83203 17.994 9.83203 17.334 9.83203H14.6673Z" fill="#032282" />
                                                        <path d="M18 23.1669H14C10.2533 23.1669 9.5 21.4469 9.5 18.6669V14.6669C9.5 11.6269 10.6 10.3269 13.3067 10.1869C13.58 10.1736 13.82 10.3803 13.8333 10.6603C13.8467 10.9403 13.6333 11.1669 13.36 11.1803C11.4667 11.2869 10.5 11.8536 10.5 14.6669V18.6669C10.5 21.1336 10.9867 22.1669 14 22.1669H18C21.0133 22.1669 21.5 21.1336 21.5 18.6669V14.6669C21.5 11.8536 20.5333 11.2869 18.64 11.1803C18.3667 11.1669 18.1533 10.9269 18.1667 10.6536C18.18 10.3803 18.4133 10.1669 18.6933 10.1803C21.4 10.3269 22.5 11.6269 22.5 14.6603V18.6603C22.5 21.4469 21.7467 23.1669 18 23.1669Z" fill="#032282" />
                                                    </svg>
                                                </div>
                                                <h3 className="font-medium text-sm text-[#0E0E2C] capitalize max-w-[100px] truncate">

                                                    {
                                                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                                        //@ts-ignore
                                                        leave.title}
                                                </h3>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Button
                                                className="hover:bg-gray-100 leave-button"
                                                size="icon"
                                                variant="ghost"
                                                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                                //@ts-ignore
                                                onClick={() => handleEdit(leave)}
                                            >
                                                <svg fill="none" height="30" viewBox="0 0 30 30" width="30" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="15" cy="15" r="14.85" stroke="#E4E4E4" strokeWidth="0.3" />
                                                    <path d="M14.334 8.33203H13.0007C9.66732 8.33203 8.33398 9.66536 8.33398 12.9987V16.9987C8.33398 20.332 9.66732 21.6654 13.0007 21.6654H17.0007C20.334 21.6654 21.6673 20.332 21.6673 16.9987V15.6654" stroke="#292D32" strokeLinecap="round" strokeLinejoin="round" strokeWidth="0.7" />
                                                    <path d="M17.6933 9.01155L12.4399 14.2649C12.2399 14.4649 12.0399 14.8582 11.9999 15.1449L11.7133 17.1515C11.6066 17.8782 12.1199 18.3849 12.8466 18.2849L14.8533 17.9982C15.1333 17.9582 15.5266 17.7582 15.7333 17.5582L20.9866 12.3049C21.8933 11.3982 22.3199 10.3449 20.9866 9.01155C19.6533 7.67822 18.5999 8.10488 17.6933 9.01155Z" stroke="#292D32" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="0.7" />
                                                    <path d="M16.9395 9.76562C17.3861 11.359 18.6328 12.6056 20.2328 13.059" stroke="#292D32" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10" strokeWidth="0.7" />
                                                </svg>
                                            </Button>
                                            <Button
                                                className="hover:bg-gray-100 leave-button"
                                                size="icon"
                                                variant="ghost"
                                                onClick={() => handleDelete(leave.id)}
                                            >
                                                <svg fill="none" height="30" viewBox="0 0 30 30" width="30" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="15" cy="15" r="14.85" stroke="#E4E4E4" strokeWidth="0.3" />
                                                    <path d="M21.0004 11.4883C20.9871 11.4883 20.9671 11.4883 20.9471 11.4883C17.4204 11.1349 13.9004 11.0016 10.4138 11.3549L9.05377 11.4883C8.77377 11.5149 8.5271 11.3149 8.50043 11.0349C8.47377 10.7549 8.67377 10.5149 8.9471 10.4883L10.3071 10.3549C13.8538 9.99495 17.4471 10.1349 21.0471 10.4883C21.3204 10.5149 21.5204 10.7616 21.4938 11.0349C21.4738 11.2949 21.2538 11.4883 21.0004 11.4883Z" fill="#292D32" />
                                                    <path d="M12.6665 10.812C12.6398 10.812 12.6132 10.812 12.5798 10.8054C12.3132 10.7587 12.1265 10.4987 12.1732 10.232L12.3198 9.3587C12.4265 8.7187 12.5732 7.83203 14.1265 7.83203H15.8732C17.4332 7.83203 17.5798 8.75203 17.6798 9.36536L17.8265 10.232C17.8732 10.5054 17.6865 10.7654 17.4198 10.8054C17.1465 10.852 16.8865 10.6654 16.8465 10.3987L16.6998 9.53203C16.6065 8.95203 16.5865 8.8387 15.8798 8.8387H14.1332C13.4265 8.8387 13.4132 8.93203 13.3132 9.52536L13.1598 10.392C13.1198 10.6387 12.9065 10.812 12.6665 10.812Z" fill="#292D32" />
                                                    <path d="M17.1396 22.1677H12.8596C10.5329 22.1677 10.4396 20.881 10.3663 19.841L9.93294 13.1277C9.91294 12.8544 10.1263 12.6144 10.3996 12.5944C10.6796 12.581 10.9129 12.7877 10.9329 13.061L11.3663 19.7744C11.4396 20.7877 11.4663 21.1677 12.8596 21.1677H17.1396C18.5396 21.1677 18.5663 20.7877 18.6329 19.7744L19.0663 13.061C19.0863 12.7877 19.3263 12.581 19.5996 12.5944C19.8729 12.6144 20.0863 12.8477 20.0663 13.1277L19.6329 19.841C19.5596 20.881 19.4663 22.1677 17.1396 22.1677Z" fill="#292D32" />
                                                    <path d="M16.1067 18.5H13.8867C13.6134 18.5 13.3867 18.2733 13.3867 18C13.3867 17.7267 13.6134 17.5 13.8867 17.5H16.1067C16.3801 17.5 16.6067 17.7267 16.6067 18C16.6067 18.2733 16.3801 18.5 16.1067 18.5Z" fill="#292D32" />
                                                    <path d="M16.6673 15.832H13.334C13.0607 15.832 12.834 15.6054 12.834 15.332C12.834 15.0587 13.0607 14.832 13.334 14.832H16.6673C16.9407 14.832 17.1673 15.0587 17.1673 15.332C17.1673 15.6054 16.9407 15.832 16.6673 15.832Z" fill="#292D32" />
                                                </svg>
                                            </Button>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </ScrollArea>
                </div>
            </div>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={errorModalMessage || 'Please check your inputs and try again.'}
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 text-base"
                        size="lg"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </div>
    );
}

export default LeaveTypes; 