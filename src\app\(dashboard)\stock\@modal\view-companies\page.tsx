'use client'


import React from 'react';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';


import { SelectCompanyDialog } from '../../misc/components/modals/SelectCompanyDialog';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';

const _CALLBACK_URL = '/stock';
const Page = () => {

  //   const getCompanyListsResponse = await payrollFetchClient<CompanyListData>(
  //     'payroll/company_list/',
  //     {
  //       headers: { Authorization: `Bearer ${access}` },
  //     }
  //   );

  //   if (getCompanyListsResponse === 'unauthorized') {
  //     redirect(`/login?callbackUrl=${CALLBACK_URL}`);
  //   }



  // const getSalesUserDetails =
  //   await spendManagementFetchClient<UserDefaultsData>(
  //     'api/v1/sales/get_user_details',
  //     {
  //       headers: { Authorization: `Bearer ${access}` },
  //     }
  //   );
  const { data: companiesResponse } = useCompaniesList()
  const { results: companiesList } = companiesResponse || {};

  // if (getSalesUserDetails.data.company_id !== null || getSalesUserDetails.data.branch_id !== null) {
  //   redirect(`/stock/company-details?company=${getSalesUserDetails.data.company_id}`);
  // } else {

  // }

  return (
    <>
      <SelectCompanyDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyLists={companiesList as CompaniesResultsEntity[]}
        link={'/stock/company-details/'}
      />
    </>
  );
};

export default Page;
