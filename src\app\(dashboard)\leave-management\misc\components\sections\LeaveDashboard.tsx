import React from 'react';
import { LeaveCard } from '../LeaveCard';
import { useLeaveManagementDashboard } from '../../api/getLeaveManagementDashboard';
import { LeaveResultsData, useNewLeaveRequest } from '../../api/getNewLeaveRequest';
import { useLeaveRecordList } from '../../api/getLeaveRecord';
import { LeaveRecordsTable, NewLeaveRequestOverviewTable } from '../tables';
// import { LeaveCard } from './LeaveCard';
// import { LeaveRecordsTable, NewLeaveRequestOverviewTable } from './tables';
// import { useLeaveManagementDashboard } from '../api/getLeaveManagementDashboard';
// import { LeaveResultsData, useNewLeaveRequest } from '../api/getNewLeaveRequest';
// import { useLeaveRecordList } from '../api/getLeaveRecord';

export type LeaveDashboardProps = {
    userId: string;
    company_id: string;
    company_name: string;
};

function LeaveDashboard({ company_id, company_name }: LeaveDashboardProps) {
    const fetchOptions = {
        companyid: company_id
    };

    const { data: leaveManagementDashboardData, isLoading: _isLeaveManagementDashboardLoading } = useLeaveManagementDashboard(fetchOptions);

    const { data: leaveNewRequestsData, isLoading: _isLeaveNewRequestsLoading } = useNewLeaveRequest(fetchOptions);
    const { data: leaveRecordList, isLoading: _isLeaveRecordListLoading } = useLeaveRecordList(fetchOptions);

    return (
        <main className="w-full mx-auto px-4 sm:px-6 lg:px-8 space-y-8">
            <div className="flex flex-col gap-6">
                <h1 className="text-3xl font-semibold text-neutral-800">
                    Leave Management
                </h1>
                <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-6 gap-4">
                    <LeaveCard
                        number={leaveManagementDashboardData?.total_leave as number || 0}
                        title="Total leave"
                    />
                    <LeaveCard
                        number={leaveManagementDashboardData?.active_leave as number || 0}
                        title="Active leave"
                    />
                    <LeaveCard
                        number={leaveManagementDashboardData?.approved_leave as number || 0}
                        title="Approved leave"
                    />
                    <LeaveCard
                        number={leaveManagementDashboardData?.pending_leave as number || 0}
                        title="Pending request"
                    />
                    <LeaveCard
                        number={leaveManagementDashboardData?.upcoming_leave as number || 0}
                        title="Upcoming leave"
                    />
                    <LeaveCard
                        number={leaveManagementDashboardData?.rejected_leave as number || 0}
                        title="Rejected leave"
                    />
                </section>
            </div>

            <section className="grid grid-cols-1 xl:grid-cols-[1fr] gap-6">
                <div className="bg-white border border-neutral-200/50 rounded-xl overflow-hidden shadow-sm">
                    <NewLeaveRequestOverviewTable
                        company_id={company_id}
                        company_name={company_name}
                        data={leaveNewRequestsData as LeaveResultsData}
                        pageSize={leaveNewRequestsData?.count as number}
                    />
                </div>

                <div className="bg-white border border-neutral-200/50 rounded-xl overflow-hidden shadow-sm">
                    <LeaveRecordsTable
                        company_id={company_id}
                        company_name={company_name}
                        data={leaveRecordList as LeaveResultsData}
                        overview
                    />
                </div>
            </section>
        </main>
    );
}

export default LeaveDashboard;