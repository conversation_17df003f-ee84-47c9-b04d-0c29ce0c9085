'use client';

import React from 'react';

import { PayrollEmploymentTrend, SelectDayFilter } from '../../types';
// import { PayrollHistoryChartCard } from './PayrollHistoryChartCard';
import { EmployeeTrendChartCard } from './EmployeeTrendChartCard';

const listOfYears = [
  { id: 1, filter: '', name: 'All time', unavailable: false },
  { id: 2, filter: 'today', name: 'Today', unavailable: false },
  { id: 3, filter: 'this_month', name: 'This month', unavailable: false },
  { id: 4, filter: 'this_week', name: 'This week', unavailable: false },
  { id: 5, filter: 'last_month', name: 'Last month', unavailable: false },
  { id: 6, filter: 'last_week', name: 'Last week', unavailable: false },
  { id: 7, filter: 'last_year', name: 'Last year', unavailable: false },
  { id: 8, filter: 'this_year', name: 'This year', unavailable: false },
];

interface PayrollHistoryChartProps {
  PayrollEmploymentChartResponse: PayrollEmploymentTrend;
}

export function
  EmployeeTrendContainer({
    PayrollEmploymentChartResponse,
  }: PayrollHistoryChartProps) {
  const months = PayrollEmploymentChartResponse?.chart_labels;

  const active_employees_values = PayrollEmploymentChartResponse?.active_employee_chart_data;

  const new_employees_values = PayrollEmploymentChartResponse?.new_employee_chart_data;

  const existed_employees_values = PayrollEmploymentChartResponse?.deleted_employee_chart_data;

  const [tripleBarChartFilter, setTripleBarChartFilter] =
    React.useState<SelectDayFilter>(listOfYears[0]);

  // console.log(months)
  const dashTripleBarChartData = {
    transactionComparative: {
      labels: months,
      values: [
        {
          label: 'Active employees ',
          data: active_employees_values,
        },
        {
          label: 'New',
          data: new_employees_values,
        },
        {
          label: 'Existed ',
          data: existed_employees_values,
        },
      ],
    },
  };

  return (
    <>
      <EmployeeTrendChartCard
        change={'up'}
        colors={['#032282', '#0ECCA1', '#EF4444']}
        data={dashTripleBarChartData.transactionComparative.values}
        dataTitle={'Active employees '}
        headLabel={'Employment trend'}
        labelsTitle={dashTripleBarChartData.transactionComparative.labels}
        secondDataTitle={'New'}
        selectDay={tripleBarChartFilter}
        setSelectDay={setTripleBarChartFilter}
        thirdDataTitle={'Existed '}
        year={listOfYears}
      />
    </>
  );
}
