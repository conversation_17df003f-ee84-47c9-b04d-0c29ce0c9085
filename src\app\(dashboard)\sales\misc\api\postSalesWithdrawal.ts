import { managementAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';





// export interface PostWithdrawEmployeeDto {
//     dataDto: {
//         data: WithdrawEntity[] | undefined;
//     };
// }
export interface PostWithdrawEmployeeDto {
    withdrawal_type: string
    company: string
    branch: string
    bank_name?: string
    bank_code?: string
    account_name?: string
    account_number?: string
    amount: number
    transaction_pin: string
    is_saved: boolean
}


interface PostWithdrawEmployeeParameters {
    dataDto: PostWithdrawEmployeeDto;
}

const PostWithdrawSales = async ({
    dataDto,
}: PostWithdrawEmployeeParameters) => {
    if (dataDto.withdrawal_type === "MAIN") {
        delete dataDto.account_name
        delete dataDto.account_number
        delete dataDto.bank_code
        delete dataDto.bank_name
    }
    const response = await managementAxios.post('/api/v1/sales/withdraw/', dataDto, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    return response.data as unknown;
};

export const usePostWithdrawSales = () => {
    return useMutation({
        mutationFn: PostWithdrawSales,
    });
};
