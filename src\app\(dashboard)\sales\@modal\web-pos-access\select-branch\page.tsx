'use client'

import React from 'react';

import { useCompanies } from '../../../misc/api';
import { LoaderModal } from '@/components/core';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { redirect } from 'next/navigation';
import SelectBranchDialog from '../../../misc/components/modals/SelectBranchDialog';

const WebPOSSelectBranchPage = () => {
  const { data: companies, isLoading } = useCompanies();

  const { data: getSalesUserDetails, isLoading: isLoadingGetSalesUserDetails } = useDefaultCompanyBranch();


  if (!isLoadingGetSalesUserDetails && getSalesUserDetails?.data?.company_id !== null && getSalesUserDetails?.data?.branch_id !== null) {
    redirect(`/sales/web-pos-access?company=${getSalesUserDetails?.data?.company_id}&companyName=${getSalesUserDetails?.data?.company}&branch=${getSalesUserDetails?.data?.branch_id}&branchName=${getSalesUserDetails?.data.branch}`);
  }
  if (isLoading) {
    return <LoaderModal />;
  }
  return (
    <>
      <SelectBranchDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={companies?.results || []}
        queryParametersOnly={false}
        subLink="web-pos-access"
      />
    </>
  );
};

export default WebPOSSelectBranchPage;
