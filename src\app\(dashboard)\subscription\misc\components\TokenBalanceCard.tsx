'use client';
import React from 'react';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';

interface TokenBalanceInfo {
  current_balance: number;
  previous_balance: number;
  status: 'active' | 'inactive';
  total_purchased: number;
}

interface TokenBalanceCardProps {
  tokenBalanceInfo: TokenBalanceInfo;
  className?: string;
  variant?: 'default' | 'compact';
}

const TokenBalanceCard: React.FC<TokenBalanceCardProps> = ({
  tokenBalanceInfo,
  className = '',
  variant = 'default'
}) => {
  const { current_balance, previous_balance, status, total_purchased } = tokenBalanceInfo;

  // Calculate the difference from previous balance
  const balanceChange = current_balance - previous_balance;
  const isPositiveChange = balanceChange > 0;
  const _isNegativeChange = balanceChange < 0;

  // Calculate token value in naira (1 token = ₦10)
  const currentValue = current_balance * 10;
  const totalPurchasedValue = total_purchased * 10;

  const getStatusColor = () => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'inactive':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      default:
        return 'Unknown';
    }
  };

  if (variant === 'compact') {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-xs font-semibold text-[#032282]">Token Balance</h4>
          <span className={`text-xs px-2 py-1 rounded-full border ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-600">Current:</span>
            <span className="text-sm font-bold text-[#032282]">
              {addCommasToNumber(current_balance)} tokens
            </span>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-600">Value:</span>
            <span className="text-sm font-medium text-gray-800">
              {convertNumberToNaira(currentValue)}
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 shadow-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-[#032282]">Token Balance</h3>
        <span className={`text-xs px-3 py-1 rounded-full border font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>

      {/* Current Balance */}
      <div className="mb-6">
        <div className="flex items-baseline gap-2 mb-1">
          <span className="text-2xl font-bold text-[#032282]">
            {addCommasToNumber(current_balance)}
          </span>
          <span className="text-sm text-gray-600">tokens</span>
        </div>
        <p className="text-sm text-gray-600">
          Current Value: <span className="font-semibold text-gray-800">{convertNumberToNaira(currentValue)}</span>
        </p>

        {/* Balance Change Indicator */}
        {balanceChange !== 0 && (
          <div className="flex items-center gap-1 mt-2">
            <span className={`text-xs px-2 py-1 rounded-full ${isPositiveChange
              ? 'text-green-700 bg-green-100'
              : 'text-red-700 bg-red-100'
              }`}>
              {isPositiveChange ? '+' : ''}{addCommasToNumber(balanceChange)} tokens
            </span>
            <span className="text-xs text-gray-500">from previous</span>
          </div>
        )}
      </div>

      {/* Statistics Grid */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <p className="text-xs text-gray-600 mb-1">Previous Balance</p>
          <p className="text-sm font-semibold text-gray-800">
            {addCommasToNumber(previous_balance)} tokens
          </p>
        </div>

        <div className="bg-[#032282] bg-opacity-5 rounded-lg p-3">
          <p className="text-xs text-gray-600 mb-1">Total Purchased</p>
          <p className="text-sm font-semibold text-[#032282]">
            {addCommasToNumber(total_purchased)} tokens
          </p>
          <p className="text-xs text-gray-500 mt-1">
            {convertNumberToNaira(totalPurchasedValue)}
          </p>
        </div>
      </div>

      {/* Token Rate Info */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          1 Token = ₦10 • Tokens never expire
        </p>
      </div>
    </div>
  );
};

export default TokenBalanceCard;
