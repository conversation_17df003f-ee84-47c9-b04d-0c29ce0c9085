'use client';
import React from 'react';
import { <PERSON><PERSON>, Checkbox } from '@/components/core';
import { SubScriptionModulePropTypes } from '../api/getSubscriptionPlan';
import { Users, CreditCard, Gift } from 'lucide-react';

interface AdditionalModulesCardProps {
  modules: SubScriptionModulePropTypes[];
  selectedModules: SubScriptionModulePropTypes[];
  onModuleSelection: (module: SubScriptionModulePropTypes) => void;
  onSubscribe: (module: SubScriptionModulePropTypes) => void;
  disabled?: boolean;
  hasStockAndSales?: boolean;
}

const AdditionalModulesCard: React.FC<AdditionalModulesCardProps> = ({
  modules,
  selectedModules,
  onModuleSelection,
  onSubscribe,
  disabled = false,
  hasStockAndSales = false
}) => {
  const getModuleIcon = (moduleName: string) => {
    if (moduleName.toLowerCase().includes('hr') || moduleName.toLowerCase().includes('payroll')) {
      return <Users className="h-5 w-5 text-[#032282]" />;
    }
    if (moduleName.toLowerCase().includes('spend')) {
      return <CreditCard className="h-5 w-5 text-[#032282]" />;
    }
    return <Gift className="h-5 w-5 text-[#032282]" />;
  };

  const getModuleFeatures = (moduleName: string) => {
    if (moduleName.toLowerCase().includes('hr') || moduleName.toLowerCase().includes('payroll')) {
      return [
        'Employee Management',
        'Payroll Processing',
        'Leave Management',
        'Performance Tracking'
      ];
    }
    if (moduleName.toLowerCase().includes('spend')) {
      return [
        'Expense Tracking',
        'Budget Management',
        'Procurement',
        'Financial Reports'
      ];
    }
    return [
      'Module Features',
      'Advanced Tools',
      'Analytics',
      'Reporting'
    ];
  };

  const isModuleSelected = (module: SubScriptionModulePropTypes) => {
    return selectedModules.some(selected => selected.id === module.id);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
      {/* Header */}
      <div className="mb-6">
        <h3 className="text-xl font-bold text-[#032282] mb-2">Additional Modules</h3>
        <p className="text-sm text-gray-600">
          Expand your business capabilities with these powerful add-on modules
        </p>
        
        {/* Free Trial Badge */}
        {hasStockAndSales && (
          <div className="mt-3 inline-flex items-center gap-2 bg-green-50 border border-green-200 rounded-lg px-3 py-2">
            <Gift className="h-4 w-4 text-green-600" />
            <span className="text-sm font-semibold text-green-800">
              30 Days Free Trial Available!
            </span>
          </div>
        )}
      </div>

      {/* Modules Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {modules.map((module) => {
          const isSelected = isModuleSelected(module);
          const features = getModuleFeatures(module.name);
          
          return (
            <div 
              key={module.id} 
              className={`border rounded-lg p-4 transition-all duration-200 ${
                isSelected 
                  ? 'border-[#032282] bg-[#032282] bg-opacity-5' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start gap-3 mb-4">
                <Checkbox
                  checked={isSelected}
                  onChange={() => onModuleSelection(module)}
                  disabled={disabled}
                  className="mt-1"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    {getModuleIcon(module.name)}
                    <h4 className="font-semibold text-[#032282]">{module.name}</h4>
                    {hasStockAndSales && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                        30 Days Free
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{module.description}</p>
                  
                  {/* Module Features */}
                  <div className="space-y-1 mb-4">
                    {features.map((feature, index) => (
                      <div key={index} className="text-xs text-gray-500">
                        ✓ {feature}
                      </div>
                    ))}
                  </div>

                  {/* Pricing Information */}
                  <div className="bg-gray-50 rounded-lg p-3 mb-3">
                    <div className="text-sm font-medium text-gray-800">
                      Subscription-based Pricing
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {hasStockAndSales 
                        ? 'Start with 30 days free, then standard rates apply'
                        : 'Choose from flexible monthly or annual plans'
                      }
                    </div>
                  </div>
                </div>
              </div>

              {/* Subscribe Button */}
              {isSelected && (
                <Button
                  onClick={() => onSubscribe(module)}
                  disabled={disabled}
                  className="w-full bg-[#032282] hover:bg-[#032282]/90 text-white py-2 rounded-lg font-medium transition-all duration-200"
                >
                  {hasStockAndSales ? 'Start Free Trial' : 'Subscribe Now'}
                </Button>
              )}
            </div>
          );
        })}
      </div>

      {/* Additional Information */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-blue-800 mb-2">Why Choose Additional Modules?</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
          <div className="text-xs text-blue-700">✓ Seamless integration</div>
          <div className="text-xs text-blue-700">✓ Unified dashboard</div>
          <div className="text-xs text-blue-700">✓ Comprehensive analytics</div>
          <div className="text-xs text-blue-700">✓ Scalable solutions</div>
          <div className="text-xs text-blue-700">✓ Expert support</div>
          <div className="text-xs text-blue-700">✓ Regular updates</div>
        </div>
      </div>
    </div>
  );
};

export default AdditionalModulesCard;
