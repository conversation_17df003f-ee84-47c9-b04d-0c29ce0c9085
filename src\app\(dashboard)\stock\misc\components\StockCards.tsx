'use client';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import React from 'react';
import { Control, Controller } from 'react-hook-form';

import '@/components/core';

import { LinkButton, ToolTip } from '@/components/core';
import { addCommasToNumber, shortenNumber } from '@/utils/numbers';

import { StockCategoryCombobox } from './StockCategoryCombobox';
import { convertNumberToNaira } from '@/utils/currency';

interface StockCardsProps {
  control: Control<{ category: string }>;
  stockValue: number;
  stockCount: number;
}

export const StockCards: React.FunctionComponent<StockCardsProps> = ({
  control,
  stockValue,
  stockCount,
}) => {
  const searchParams = useSearchParams();

  const company = searchParams.get('company') as string;
  const branch = searchParams.get('branch') as string;

  return (
    <div className="grid gap-4 rounded-lg bg-[#EAEFFF] px-4 py-5 md:grid-cols-2 lg:grid-cols-4">
      <div className="flex h-40 flex-col justify-between rounded-lg bg-main-solid bg-[url('/images/stocks/stack_boxes.svg')] bg-contain bg-right bg-no-repeat p-4">
        <p className="text-[12px] text-white">
          Libertypay Stock keeping record system keep a track of physical
          quantities and the complete monetary valuation.
        </p>

        <div className="flex justify-start gap-x-4">
          <Link
            className="inline-block rounded-lg bg-[#fff]/10 p-2 text-xs text-white"
            href={`/stock/upload-stock?company=${company}&branch=${branch}`}
          >
            Upload Stock
          </Link>

          <Link
            className="inline-block rounded-lg bg-[#fff]/10 p-2 text-xs text-white"
            href={`/stock/input-stock?company=${company}&branch=${branch}&tabIndex=input_stock`}
          >
            Input Stock
          </Link>
        </div>
      </div>

      <div className="h-40 rounded-lg bg-white p-4">
        <div className="flex items-center justify-between rounded-lg">
          <div className="icon bg-[#F5F7F9] p-2">
            <svg
              fill="none"
              height="20"
              viewBox="0 0 20 20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M17.5415 9.75826V14.7499C17.5415 17.0499 15.6748 18.9166 13.3748 18.9166H6.09147C5.8998 18.9166 5.70814 18.8999 5.52481 18.8749C5.83314 18.7416 6.10814 18.5582 6.35814 18.3332C6.39148 18.3082 6.41646 18.2833 6.44146 18.2499C6.66646 18.0583 6.85813 17.8166 7.01646 17.5499C7.32479 17.0499 7.49981 16.4582 7.49981 15.8332C7.49981 14.9582 7.15813 14.1666 6.60813 13.5666C6.5248 13.4749 6.43312 13.3832 6.33312 13.3082C5.74979 12.7999 4.99981 12.4999 4.16647 12.4999C3.57481 12.4999 3.01646 12.6583 2.53312 12.9333C2.37479 13.0166 2.22481 13.1166 2.08314 13.2332C2.02481 13.2749 1.9748 13.3249 1.9248 13.3749V9.8166C2.55814 10.4999 3.45815 10.8916 4.43315 10.8916C5.48315 10.8916 6.49146 10.3666 7.1248 9.5249C7.69146 10.3666 8.65813 10.8916 9.73313 10.8916C10.7998 10.8916 11.7498 10.3916 12.3248 9.55822C12.9665 10.3832 13.9581 10.8916 14.9915 10.8916C15.9998 10.8916 16.9165 10.4833 17.5415 9.75826Z"
                fill="#292D32"
                opacity="0.4"
              />
              <path
                d="M12.225 1.30835H7.22502L6.60836 7.44167C6.55836 8.00834 6.64167 8.54169 6.85001 9.02502C7.33334 10.1584 8.46669 10.8917 9.73336 10.8917C11.0167 10.8917 12.1251 10.175 12.6251 9.03337C12.7751 8.67503 12.8667 8.25836 12.875 7.83336V7.67503L12.225 1.30835Z"
                fill="#292D32"
              />
              <path
                d="M18.3666 7.15841L18.1333 4.84172C17.7833 2.32505 16.6416 1.30005 14.2 1.30005H11L11.6167 7.55005C11.625 7.63338 11.6333 7.72507 11.6333 7.8834C11.6833 8.31674 11.8166 8.71673 12.0166 9.07506C12.6166 10.1751 13.7833 10.8834 15 10.8834C16.1083 10.8834 17.1083 10.3917 17.7333 9.52504C18.225 8.86671 18.45 8.02507 18.3666 7.15841Z"
                fill="#292D32"
                opacity="0.6"
              />
              <path
                d="M5.22477 1.30835C2.77477 1.30835 1.64141 2.33332 1.28307 4.87499L1.05811 7.16666C0.974773 8.05832 1.21645 8.92503 1.74145 9.60003C2.37478 10.425 3.34976 10.8917 4.4331 10.8917C5.64976 10.8917 6.81644 10.1833 7.4081 9.1C7.62477 8.725 7.76642 8.29165 7.80809 7.84165L8.45811 1.31669H5.22477V1.30835Z"
                fill="#292D32"
                opacity="0.6"
              />
              <path
                d="M6.60849 13.5667C6.52516 13.475 6.43348 13.3833 6.33348 13.3083C5.75014 12.8 5.00016 12.5 4.16683 12.5C3.57516 12.5 3.01681 12.6583 2.53348 12.9333C2.37514 13.0167 2.22516 13.1167 2.0835 13.2333C1.31683 13.8417 0.833496 14.7833 0.833496 15.8333C0.833496 16.4583 1.00851 17.05 1.31684 17.55C1.39184 17.6833 1.48352 17.8083 1.58352 17.925C1.60018 17.95 1.62514 17.9667 1.6418 17.9917C2.2418 18.7084 3.15016 19.1667 4.16683 19.1667C5.0085 19.1667 5.77517 18.8583 6.3585 18.3333C6.39183 18.3083 6.41681 18.2834 6.44181 18.25C6.66681 18.0584 6.85848 17.8167 7.01682 17.55C7.32515 17.05 7.50016 16.4583 7.50016 15.8333C7.50016 14.9583 7.15849 14.1667 6.60849 13.5667ZM5.41683 16.4417H4.79183V17.0917C4.79183 17.4333 4.5085 17.7167 4.16683 17.7167C3.82516 17.7167 3.54183 17.4333 3.54183 17.0917V16.4417H2.91683C2.57516 16.4417 2.29183 16.1583 2.29183 15.8167C2.29183 15.475 2.57516 15.1917 2.91683 15.1917H3.54183V14.6C3.54183 14.2583 3.82516 13.975 4.16683 13.975C4.5085 13.975 4.79183 14.2583 4.79183 14.6V15.1917H5.41683C5.7585 15.1917 6.04183 15.475 6.04183 15.8167C6.04183 16.1583 5.7585 16.4417 5.41683 16.4417Z"
                fill="#292D32"
              />
            </svg>
          </div>

          <div className=" rounded-lg bg-[#F5F7F9] !text-sm">
            <Controller
              control={control}
              name={'category'}
              render={({ field: { onChange, value } }) => (
                <StockCategoryCombobox
                  companyId={company}
                  // defaultCategoryId={categoryId}
                  id="category"
                  placeholder="Select your Category"
                  value={value}
                  onChange={onChange}
                />
              )}
            />
          </div>
        </div>

        <div className="my-3 flex items-center justify-between">
          <p className="text-sm">Total stock count</p>
          <LinkButton
            href={`/stock/restock/manual-view?company=${company}&branch=${branch}`}
            variant={'default'}
          >
            Restock
          </LinkButton>
        </div>

        <p className="text-3xl font-semibold">
          {addCommasToNumber(stockCount)}
        </p>
      </div>
      <div className="relative h-40 rounded-lg bg-white p-4">
        <div className="flex items-center justify-between ">
          <div className="icon rounded-lg bg-[#F5F7F9] p-2">
            <svg
              fill="none"
              height="20"
              viewBox="0 0 20 20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18.3332 18.3333H1.6665C1.32484 18.3333 1.0415 18.0499 1.0415 17.7083C1.0415 17.3666 1.32484 17.0833 1.6665 17.0833H18.3332C18.6748 17.0833 18.9582 17.3666 18.9582 17.7083C18.9582 18.0499 18.6748 18.3333 18.3332 18.3333Z"
                fill="#292D32"
              />
              <path
                d="M8.125 3.33341V18.3334H11.875V3.33341C11.875 2.41675 11.5 1.66675 10.375 1.66675H9.625C8.5 1.66675 8.125 2.41675 8.125 3.33341Z"
                fill="#292D32"
              />
              <path
                d="M2.5 8.33341V18.3334H5.83333V8.33341C5.83333 7.41675 5.5 6.66675 4.5 6.66675H3.83333C2.83333 6.66675 2.5 7.41675 2.5 8.33341Z"
                fill="#292D32"
                opacity="0.4"
              />
              <path
                d="M14.1665 12.4999V18.3333H17.4998V12.4999C17.4998 11.5833 17.1665 10.8333 16.1665 10.8333H15.4998C14.4998 10.8333 14.1665 11.5833 14.1665 12.4999Z"
                fill="#292D32"
                opacity="0.4"
              />
            </svg>
          </div>
          <div></div>
        </div>
        <div className="absolute bottom-4">
          <ToolTip
            content={convertNumberToNaira(stockValue)}
            contentClass='text-xs font-normal'
            asChild
          >
            <p className=" text-3xl font-semibold">
              ₦{shortenNumber(stockValue || 0)}
            </p>
          </ToolTip>
          <p className="text-xs">Total stock value</p>
        </div>
      </div>

      <div className="relative h-40 rounded-lg bg-white p-4">
        <div className="flex items-center justify-between ">
          <div className="icon rounded-lg bg-[#F5F7F9] p-2">
            <svg
              fill="none"
              height="20"
              viewBox="0 0 20 20"
              width="20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M17.5415 9.75826V14.7499C17.5415 17.0499 15.6748 18.9166 13.3748 18.9166H6.09147C5.8998 18.9166 5.70814 18.8999 5.52481 18.8749C5.83314 18.7416 6.10814 18.5582 6.35814 18.3332C6.39148 18.3082 6.41646 18.2833 6.44146 18.2499C6.66646 18.0583 6.85813 17.8166 7.01646 17.5499C7.32479 17.0499 7.49981 16.4582 7.49981 15.8332C7.49981 14.9582 7.15813 14.1666 6.60813 13.5666C6.5248 13.4749 6.43312 13.3832 6.33312 13.3082C5.74979 12.7999 4.99981 12.4999 4.16647 12.4999C3.57481 12.4999 3.01646 12.6583 2.53312 12.9333C2.37479 13.0166 2.22481 13.1166 2.08314 13.2332C2.02481 13.2749 1.9748 13.3249 1.9248 13.3749V9.8166C2.55814 10.4999 3.45815 10.8916 4.43315 10.8916C5.48315 10.8916 6.49146 10.3666 7.1248 9.5249C7.69146 10.3666 8.65813 10.8916 9.73313 10.8916C10.7998 10.8916 11.7498 10.3916 12.3248 9.55822C12.9665 10.3832 13.9581 10.8916 14.9915 10.8916C15.9998 10.8916 16.9165 10.4833 17.5415 9.75826Z"
                fill="#292D32"
                opacity="0.4"
              />
              <path
                d="M12.225 1.30835H7.22502L6.60836 7.44167C6.55836 8.00834 6.64167 8.54169 6.85001 9.02502C7.33334 10.1584 8.46669 10.8917 9.73336 10.8917C11.0167 10.8917 12.1251 10.175 12.6251 9.03337C12.7751 8.67503 12.8667 8.25836 12.875 7.83336V7.67503L12.225 1.30835Z"
                fill="#292D32"
              />
              <path
                d="M18.3666 7.15841L18.1333 4.84172C17.7833 2.32505 16.6416 1.30005 14.2 1.30005H11L11.6167 7.55005C11.625 7.63338 11.6333 7.72507 11.6333 7.8834C11.6833 8.31674 11.8166 8.71673 12.0166 9.07506C12.6166 10.1751 13.7833 10.8834 15 10.8834C16.1083 10.8834 17.1083 10.3917 17.7333 9.52504C18.225 8.86671 18.45 8.02507 18.3666 7.15841Z"
                fill="#292D32"
                opacity="0.6"
              />
              <path
                d="M5.22477 1.30835C2.77477 1.30835 1.64141 2.33332 1.28307 4.87499L1.05811 7.16666C0.974773 8.05832 1.21645 8.92503 1.74145 9.60003C2.37478 10.425 3.34976 10.8917 4.4331 10.8917C5.64976 10.8917 6.81644 10.1833 7.4081 9.1C7.62477 8.725 7.76642 8.29165 7.80809 7.84165L8.45811 1.31669H5.22477V1.30835Z"
                fill="#292D32"
                opacity="0.6"
              />
              <path
                d="M6.60849 13.5667C6.52516 13.475 6.43348 13.3833 6.33348 13.3083C5.75014 12.8 5.00016 12.5 4.16683 12.5C3.57516 12.5 3.01681 12.6583 2.53348 12.9333C2.37514 13.0167 2.22516 13.1167 2.0835 13.2333C1.31683 13.8417 0.833496 14.7833 0.833496 15.8333C0.833496 16.4583 1.00851 17.05 1.31684 17.55C1.39184 17.6833 1.48352 17.8083 1.58352 17.925C1.60018 17.95 1.62514 17.9667 1.6418 17.9917C2.2418 18.7084 3.15016 19.1667 4.16683 19.1667C5.0085 19.1667 5.77517 18.8583 6.3585 18.3333C6.39183 18.3083 6.41681 18.2834 6.44181 18.25C6.66681 18.0584 6.85848 17.8167 7.01682 17.55C7.32515 17.05 7.50016 16.4583 7.50016 15.8333C7.50016 14.9583 7.15849 14.1667 6.60849 13.5667ZM5.41683 16.4417H4.79183V17.0917C4.79183 17.4333 4.5085 17.7167 4.16683 17.7167C3.82516 17.7167 3.54183 17.4333 3.54183 17.0917V16.4417H2.91683C2.57516 16.4417 2.29183 16.1583 2.29183 15.8167C2.29183 15.475 2.57516 15.1917 2.91683 15.1917H3.54183V14.6C3.54183 14.2583 3.82516 13.975 4.16683 13.975C4.5085 13.975 4.79183 14.2583 4.79183 14.6V15.1917H5.41683C5.7585 15.1917 6.04183 15.475 6.04183 15.8167C6.04183 16.1583 5.7585 16.4417 5.41683 16.4417Z"
                fill="#292D32"
              />
            </svg>
          </div>
        </div>

        <div className="my-3 flex flex-col items-center justify-center">
          <p className="text-sm ">Stock out</p>
          <Link
            className="absolute bottom-8 inline-block rounded-lg bg-[#F2F5FF] px-4 py-2 text-xs text-[#032282]"
            href={`/stock/stock-out?company=${company}&branch=${branch}`}
          >
            Record Stockout
          </Link>
        </div>
      </div>
    </div>
  );
};
