"use client"

import React, { useEffect, useState } from 'react';

import { Button, LoaderModal, Tabs, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/core';

import { OrdersCustomersTable, OrdersHistoryTable } from '@/app/(dashboard)/sales/misc/components/tables';
// import { OrderManagemementPipeline } from '@/app/(dashboard)/instant-web/misc/components';
import { useSearchParams } from 'next/navigation';
import { useGetBranchOrdersPipeline } from '../../sales/misc/api';
import { OrderManagemementPipeline, OrdersIncompleteOrdersTable, OrdersPipelineConfig, ViewBranchesDialog } from '../misc/components';
import { CopyIcon } from '../misc/icons';
import { Plus } from 'lucide-react';
import { useCompaniesList } from '../misc/api';
import { useDefaultCompanyBranch } from '../misc/api/companies/getDefaultBranch';
import toast from 'react-hot-toast';
import { CSVLink } from 'react-csv';
import SubscriptionErrorModal from '../../spend-management/misc/components/subscription/ErrorSubscriptionModal';
import { AxiosError } from 'axios';

interface TabProps {
  value: string;
  label: string;
  component: React.ReactNode;
}

const Page = () => {
  const searchParams = useSearchParams();
  const companyId = searchParams.get('company') || searchParams.get('companyId') || "";
  const branchId = searchParams.get('branch') || searchParams.get('branchId') || "";
  // const router = useRouter();
  // const pathname = usePathname();

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  // useEffect(() => {
  //   if (!companyId && salesUserDetails?.data) {
  //     router.replace(`${pathname}?companyId=${salesUserDetails?.data.company_id}&companyName=${salesUserDetails?.data.company}&branchId=${salesUserDetails?.data.branch_id}&branchName=${salesUserDetails?.data.branch}`)
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [salesUserDetails?.data.company_id])


  const { data, isLoading, isFetching, refetch, error } = useGetBranchOrdersPipeline(branchId || "");
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [csvData, setCsvData] = useState<any[]>([]);

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const getData: any[] = []
    data?.stages.forEach((stage) => {
      stage.data.forEach((order) => {
        order.order_products.forEach((product) => {
          getData.push({
            stage_name: stage.stage_name,
            amount_paid: order.amount_paid,
            channels: order.channels,
            contact_name: order.contact_name,
            contact_phone: order.contact_phone_number,
            discount: order.discount,
            order_date: order.order_date,
            order_id: order.order_id,
            product_price: product.price,
            product_name: product.product_name,
            product_quantity: product.quantity,
            product_sub_total: product.sub_total,
            payment_status: order.payment_status,
            total_price: order.total_price,
          });
        });
      });
    });
    setCsvData(getData)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  const tabsArray: TabProps[] = [
    {
      value: 'order__pipeline',
      label: 'Orders',
      component: <OrderManagemementPipeline
        branch_id={branchId}
        company_id={companyId as string}
        isFetchingPipelineData={isFetching}
        isLoadingPipelineData={isLoading}
        pipelineData={data}
        refetchPipelineData={refetch}
      />,
    },
    {
      value: 'order__incomplete__orders',
      label: 'Incomplete Orders',
      component: <OrdersIncompleteOrdersTable
        branchId={branchId}
        companyId={companyId as string}
        stages={data?.stages.map((stage) => stage.stage_name) || []}
      />,
    },
    {
      value: 'order__customers',
      label: 'Customers',
      component: <OrdersCustomersTable branchId={branchId} />,
    },
    {
      value: 'order__history',
      label: 'History',
      component: <OrdersHistoryTable
        branchId={branchId}
        stages={data?.stages.map((stage) => stage.stage_name) || []}
      />,
    },
    {
      value: 'order__pipeline__config',
      label: 'Pipeline Config',
      component: <OrdersPipelineConfig
        apiData={data}
        defaultStages={data?.stages || []}
        pipelineId={data?.pipeline_id || ""}
        refetchPipelineData={refetch}
      />,
    },
  ];

  function copyText(text: string) {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('Text copied to clipboard!');
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  }


  const statusError = error as AxiosError


  return (
    <>
      <LoaderModal isOpen={isDefaultLoading || !companyId} />

      {branchId ?
        <div className='h-full overflow-y-hidden lg:max-h-[86vh]'>
          <Tabs className="h-full w-full grid grid-rows-[max-content_1fr] overflow-hidden" defaultValue="order__pipeline">
            <div>
              <div className='flex items-center justify-between bg-white  px-6 md:px-7 lg:px-11 py-1.5 '>
                <div className='flex items-center gap-5 text-sm'>
                  <p className='flex items-center gap-3 font-medium text-main-solid'>
                    Orders:
                    <span>
                      {data?.total_orders_count || 0}
                    </span>
                  </p>

                  <p className='flex items-center gap-3 font-medium text-[#009444]'>
                    Delivered:
                    <span>
                      {data?.delivered_orders_count || 0}
                    </span>
                  </p>

                  <p className='flex items-center gap-3 font-medium text-[#EF4444]'>
                    Cancelled:
                    <span>
                      {data?.cancelled_orders_count || 0}
                    </span>
                  </p>

                  <section className=' ml-auto flex items-center gap-5'>

                  </section>
                </div>

                <div className='flex items-center gap-4'>
                  <Button className='flex items-center gap-3 py-3 text-sm' variant="light" onClick={() => {
                    copyText(data?.web_store_url as string)
                  }}>
                    Store Link
                    <CopyIcon className="text-[#032282]" height={18} width={18} />
                  </Button>
                  <CSVLink
                    className="rounded-md border border-[#063898] w-max text-center whitespace-nowrap px-4 py-2 text-sm text-[#063898]"
                    data={csvData ?? []}
                    filename={`Order_History_${new Date()}`}

                  >  Export</CSVLink>
                  <Button className='flex items-center gap-2 py-3 text-sm'>
                    <Plus size={20} />
                    Create order
                  </Button>
                </div>
              </div>

              <TabsList className="flex items-center md:justify-start gap-x-4 md:gap-5 mb-1 px-6 md:px-7 lg:px-11 border-none !pt-0 bg-white">
                {tabsArray.map((tab) => (
                  <TabsTrigger
                    className="rounded-none text-[0.925rem] text-[#4A4A68] data-[state=active]:font-medium border-transparent border-b-[3px] hover:border-b-main-solid-light data-[state=active]:text-main-solid data-[state=active]:!border-b-main-solid data-[state=active]:shadow-none"
                    key={tab.value}
                    value={tab.value}
                  >
                    {tab.label}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
            {
              tabsArray.map((tab) => (
                <TabsContent
                  className="mt-0 h-full w-full rounded-sm bg-white overflow-x-hidden"
                  key={tab.value}
                  value={tab.value}
                >
                  {tab.component}
                </TabsContent>
              ))
            }
          </Tabs>
        </div>
        :
        <>
          {!isCompanyLoading && <ViewBranchesDialog company={companyId as string}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            companyList={companies?.results} link={'/instant-web/orders'} />}
        </>
      }


      {
        (statusError?.response?.status === 403 && !isLoading) && <SubscriptionErrorModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          message={String(statusError?.response?.data?.error as unknown as string) ?? ""}
        />
      }
    </>

  );
};

export default Page;
