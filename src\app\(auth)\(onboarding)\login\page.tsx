'use client';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/core';
import { OnboardingPageWrapper } from '../misc/components';
import { NewLoginForm, NewLoginPhoneNumberForm } from './misc/components';

export default function Login() {
  return (
    // <OnboardingPageWrapper
    //   heading="Welcome back 👋"
    //   subHeading="Enter your details to login"
    // >
    //   <NewLoginForm />
    // </OnboardingPageWrapper>
    <>
      <div className="mx-auto max-w-[32.375rem] px-2 md:hidden">
        <p className="relative mb-2 ml-auto w-max text-white md:hidden">1/7</p>
      </div>

      <OnboardingPageWrapper
        heading="Welcome back 👋"
        subHeading="Enter your details to login"
      >
        <main className=''>

          <Tabs className="relative w-full" defaultValue="email">
            <TabsList className="w-full flex justify-start rounded-10 bg-transparent text-white font-medium pb-0 ">
              <TabsTrigger
                className=" w-full pb-2 test-white border-b-[5px] border-b-white text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none data-[state=active]:text-white"
                value="email"
              >
                Email login
              </TabsTrigger>
              <div>
                <svg fill="none" height="34" viewBox="0 0 3 34" width="3" xmlns="http://www.w3.org/2000/svg">
                  <rect fill="white" height="3" rx="1.5" transform="rotate(90 3 0)" width="34" x="3" />
                </svg>
              </div>

              <TabsTrigger
                className=" w-full border-b-[5px] pb-2 border-b-white  text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none  data-[state=active]:text-white"
                value="phone"
              >
                Phone no login
              </TabsTrigger>
            </TabsList>

            <TabsContent
              className="mt-[18px] rounded-10"
              value="email"
            >
              <NewLoginForm />
            </TabsContent>

            <TabsContent
              className="mt-[18px] rounded-10"
              value="phone"
            >
              <NewLoginPhoneNumberForm />
            </TabsContent>
          </Tabs>
        </main>
      </OnboardingPageWrapper>
    </>
  );
}
