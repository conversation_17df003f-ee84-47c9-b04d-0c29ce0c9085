'use client'

import React from 'react';

import { Avatar, AvatarFallback, AvatarImage, Button, Card, CardContent, CardDescription, CardHeader, CardTitle, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, Input, LinkButton, Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core';

// import { HrisLogo } from '../../icons';
import { EmoloyeeProfileDetailsData } from '../../types';
// import Image from 'next/image';
import { AccDetailIcon, BioDataIcon, CertificationIcon, EditProfileIcon, EducationIcon, ExperienceIcon, LocationIcon, SalaryDetIcon } from '@/app/(dashboard)/instant-web/misc/icons';
import { useUserDetails } from '@/app/(auth)/(onboarding)/misc/api/getUserDetails';
import { UserData } from '@/types/users';
import { useEmployeeProfileResponse } from '../../api/employee-hris/employee-details/getEmployeeProfileResponse';
import { useEmployeeFamilyContactResponse } from '../../api/employee-hris/employee-details/getEmployeeFamilyContactResponse';
import { useEmployeeAccountDetails } from '../../api/employee-hris/employee-details/getEmployeeAccountDetails';
import { useEmployeeCertifications } from '../../api/employee-hris/employee-details/getEmployeeCertifications';
import { RequestLeaveDialog } from './leave-request/RequestLeaveDialog';
import { useBooleanStateControl } from '@/hooks';
import { Award, Bell, Building, Calendar, Filter, Gift, Megaphone, MoreVertical, Plus, Search, Users } from 'lucide-react';
import { Badge } from '@/components/core/Badge';
import { useAllEmployeesWithoutPermission } from '../../api/employees/getAllEmployeesWithoutPermission';
import { useEmployeeEventsAnnouncement } from '../../api/employee-hris/employee-details/getEventsAnnouncement';
import { usePayrollUserRole } from '../../api/payroll/user-role/getPayrollUserRole';
import { cn } from '@/utils/classNames';
import CreateAnnouncementModal from './forms/CreateAnnouncementModal';



// Mock data based on your JSON structure
// const eventsData = {
//   announcement: [
//     {
//       announcement_type: "ANNOUNCEMENT",
//       announcement_title: "Company All-Hands Meeting",
//       announcement_body: "Join us for our quarterly all-hands meeting to discuss company updates and future plans.",
//       announcement_date: "2025-06-20T11:50:00+01:00",
//       created_by: "Ted Buddy",
//     },
//     {
//       announcement_type: "ANNOUNCEMENT",
//       announcement_title: "New Policy Updates",
//       announcement_body: "Please review the updated employee handbook and policy changes effective next month.",
//       announcement_date: "2025-06-21T15:40:00+01:00",
//       created_by: "HR Department",
//     },
//   ],
//   event: [
//     {
//       announcement_type: "EVENT",
//       announcement_title: "Team Building Workshop",
//       announcement_body: "Interactive workshop focused on collaboration and team dynamics.",
//       announcement_date: "2025-06-20T11:50:00+01:00",
//       created_by: "Ted Buddy",
//     },
//     {
//       announcement_type: "EVENT",
//       announcement_title: "Tech Conference 2025",
//       announcement_body: "Annual technology conference featuring industry leaders and innovation showcases.",
//       announcement_date: "2025-06-21T15:40:00+01:00",
//       created_by: "Tech Team",
//     },
//   ],
//   birthday: [
//     {
//       announcement_type: "BIRTHDAY",
//       announcement_title: "Happy Birthday Sarah!",
//       announcement_body: "Wishing Sarah from Marketing a wonderful birthday celebration!",
//       announcement_date: "2025-06-20T11:50:00+01:00",
//       created_by: "HR Team",
//     },
//     {
//       announcement_type: "BIRTHDAY",
//       announcement_title: "Birthday Celebration - Mike",
//       announcement_body: "Join us in celebrating Mike's birthday with cake in the break room at 3 PM.",
//       announcement_date: "2025-06-21T15:40:00+01:00",
//       created_by: "Admin",
//     },
//   ],
//   work_anniversary: [
//     {
//       announcement_type: "WORK_ANNIVERSARY",
//       announcement_title: "5 Years with the Company!",
//       announcement_body: "Congratulations to John on completing 5 successful years with our team!",
//       announcement_date: "2025-06-20T11:50:00+01:00",
//       created_by: "Management",
//     },
//     {
//       announcement_type: "WORK_ANNIVERSARY",
//       announcement_title: "Work Anniversary - Lisa",
//       announcement_body: "Celebrating Lisa's 3rd work anniversary. Thank you for your dedication!",
//       announcement_date: "2025-06-21T15:40:00+01:00",
//       created_by: "HR Team",
//     },
//   ],
// }

// const employeesData = {
//   data_type: "EMPLOYEES_DATA",
//   count: 14,
//   results: [
//     {
//       id: 25,
//       employee_first_name: "Mariam",
//       employee_last_name: "Abandy",
//       employee_start_date: "2023-01-15",
//       employee_department: "c4d3e68e-00b7-49a1-aff4-841ff600a775",
//       created_at: "2023-09-11T09:54:28.414127+01:00",
//       email: "<EMAIL>",
//       department_name: "Tech",
//     },
//     {
//       id: 21,
//       employee_first_name: "Ted",
//       employee_last_name: "Buddy",
//       employee_start_date: "2022-05-10",
//       employee_department: "c4d3e68e-00b7-49a1-aff4-841ff600a775",
//       created_at: "2023-07-27T16:29:09.326765+01:00",
//       email: "<EMAIL>",
//       department_name: "Tech",
//     },
//     {
//       id: 37,
//       employee_first_name: "Eddy",
//       employee_last_name: "Bungo",
//       employee_start_date: "2024-01-20",
//       employee_department: null,
//       created_at: "2024-03-13T10:38:00.518102+01:00",
//       email: "<EMAIL>",
//       department_name: "Operations",
//     },
//     {
//       id: 18,
//       employee_first_name: "Eddy",
//       employee_last_name: "Bungo",
//       employee_start_date: "2023-03-15",
//       employee_department: "c4d3e68e-00b7-49a1-aff4-841ff600a775",
//       created_at: "2023-07-27T13:29:38.260153+01:00",
//       email: "<EMAIL>",
//       department_name: "Tech",
//     },
//     {
//       id: 32,
//       employee_first_name: "Eddy",
//       employee_last_name: "bungo",
//       employee_start_date: "2023-08-01",
//       employee_department: null,
//       created_at: "2024-03-11T15:41:58.316898+01:00",
//       email: "<EMAIL>",
//       department_name: "Marketing",
//     },
//     {
//       id: 19,
//       employee_first_name: "Eddy",
//       employee_last_name: "Bungos",
//       employee_start_date: "2023-06-12",
//       employee_department: null,
//       created_at: "2023-07-27T13:29:38.322390+01:00",
//       email: "<EMAIL>",
//       department_name: "HR",
//     },
//   ],
// }


export type InviteEmployeeFormProps = {
  companyid: string;
  companyName: string;
  progress?: number;
  employee_profile_details?: EmoloyeeProfileDetailsData;
};
export function HrisProfile({
  companyName,
  companyid,
  progress,
  // employee_profile_details
}: InviteEmployeeFormProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [_selectedEventType, setSelectedEventType] = React.useState("all")

  // console.log(employee_profile_details, "ghvsdhg").
  const [profileUrl, setProfileUrl] = React.useState('/images/payroll/upload-img.png');

  const fetchOptions = {
    companyid: companyid
  }

  const { data: employeesData } = useAllEmployeesWithoutPermission(fetchOptions)

  const { data: eventsData, refetch: refetchEventsData } = useEmployeeEventsAnnouncement(fetchOptions)


  const {
    state: isRequestLeaveDialogOpen,
    setState: setRequestLeaveDialogState,
    setTrue: openRequestLeaveDialog,
  } = useBooleanStateControl();


  const {
    state: isCreatennouncementDialogOpen,
    setState: setCreatennouncementDialogState,
    setTrue: openCreateAnnouncementDialog,
  } = useBooleanStateControl();


  const {
    data: userDetailsResponse,
    // error,
  } = useUserDetails({ retryCount: 3 });

  // console.log(userDetailsResponse?.user_data)

  const { data: employee_profile } = useEmployeeProfileResponse(fetchOptions)

  const { data: employee_family_contact } = useEmployeeFamilyContactResponse(fetchOptions)

  const { data: getEmployeeAccountDetails } = useEmployeeAccountDetails(fetchOptions)

  const { data: getEmployeeCertifications } = useEmployeeCertifications(fetchOptions)

  const { data: getUserRoleResponse } = usePayrollUserRole(companyid)

  // console.log(getEmployeeCertifications, "rrrrrrrrrrrrrrrrrrrrr")

  const user_data = userDetailsResponse?.user_data || {} as UserData


  React.useEffect(() => {

    if (employee_profile?.employee_profile_picture === null) {
      setProfileUrl('/images/payroll/upload-img.png')
    } else {
      setProfileUrl(employee_profile?.employee_profile_picture as string)
    }

  }, [employee_profile?.employee_profile_picture,]);


  const EmployeeCards = [
    {
      id: 1,
      title: 'Department',
      employee_det: employee_profile?.department_name || '--'
    },
    {
      id: 2,
      title: 'Phone',
      employee_det: employee_profile?.employee_phone_number || '--'

    },
    {
      id: 3,
      title: 'Work anniversary',
      employee_det: '--'

    },
    {
      id: 4,
      title: 'Supervisor',
      employee_det: '--'

    },
    {
      id: 5,
      title: 'Email',
      employee_det: employee_profile?.employee_alternate_email || '--'

    },
    {
      id: 6,
      title: 'Time in company',
      employee_det: '--'

    },
  ];

  const BioData = [
    { key: "Title", value: "Mr" },
    { key: "First Name", value: employee_profile?.employee_first_name || "--" },
    { key: "Last Name", value: employee_profile?.employee_last_name || "--" },
    { key: "Other Names", value: "--" },
    { key: "Gender", value: employee_profile?.employee_gender || "--" },
    { key: "Marital Status", value: employee_profile?.employee_marital_status || "--" },
    { key: "Date of Birth", value: employee_profile?.employee_birth_date || "--" },
    { key: "Religion", value: employee_profile?.employee_religion || "--" },
    { key: "Genotype", value: employee_profile?.employee_genotype || "--" },
    { key: "Blood Group", value: employee_profile?.employee_blood_group || "--" },
    { key: "Country", value: "Nigeria" },
    { key: "Place of Birth", value: "Lagos" },
    { key: "State of Origin", value: user_data.state },
    { key: "LGA", value: user_data.lga || "--" },
    { key: "Alternate Phone No", value: employee_profile?.employee_phone_number || "--" },
    { key: "Alternate Email", value: employee_profile?.employee_alternate_email || "--" },
  ];

  const Address = [
    { key: "Nearest Landmark", value: user_data.nearest_landmark || "--" },
    { key: "Country", value: "Nigeria" },
    { key: "State", value: "Lagos State" },
  ];

  const AccDet = [
    { key: "Account number", value: getEmployeeAccountDetails?.[0]?.account_number || "--" },
    { key: "Bank name", value: getEmployeeAccountDetails?.[0]?.bank_name || "--" },
    { key: "Account name", value: getEmployeeAccountDetails?.[0]?.account_name || "--" },
  ];

  const EduQualification = [
    { key: "Course", value: getEmployeeCertifications?.[0]?.course_name || "--" },
    { key: "Degree", value: getEmployeeCertifications?.[0]?.license_name || "--" },
    { key: "Grade", value: "--" },
    { key: "Start date", value: getEmployeeCertifications?.[0]?.start_date || "--" },
    { key: "End date", value: getEmployeeCertifications?.[0]?.end_date || "--" },
  ];

  const Next_of_kin_data = [
    { key: "Title", value: "Mr" },
    { key: "First Name", value: employee_family_contact?.employee_first_next_of_kin_firstname || "--" },
    { key: "Last Name", value: employee_family_contact?.employee_first_next_of_kin_lastname || "--" },
    // { key: "Other Names", value: employee_family_contact?.employee_first_next_of_kin_ || "--"},
    // { key: "Gender", value:employee_family_contact?. || "--"},
    { key: "Phone Number", value: employee_family_contact?.employee_first_next_of_kin_phone_number || "--" },
    { key: "Email", value: employee_family_contact?.employee?.email || "--" },
    { key: "Relationship", value: employee_family_contact?.employee_first_next_of_kin_relationship || "--" },
  ];

  const financialData = [
    { key: "Account Number", value: getEmployeeAccountDetails?.[0]?.account_number || "--" },
    { key: "Bank Name", value: getEmployeeAccountDetails?.[0]?.bank_name || "--" },
    { key: "Payable Amount", value: employee_profile?.employee_payable_amount || "--" },
    { key: "Salary Grade", value: employee_profile?.employee_pay_grade || "--" }
  ];

  const courses = [
    { courseName: "--", year: "--" },
    { courseName: "--", year: "--" },
    { courseName: "--", year: "--" }
  ];

  const positionData = [
    { key: "Position", value: "--" },
    { key: "Location", value: "--" },
    { key: "Start Date", value: "--" },
    { key: "End Date", value: "--" }
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case "ANNOUNCEMENT":
        return <Megaphone className="h-4 w-4" />
      case "EVENT":
        return <Calendar className="h-4 w-4" />
      case "BIRTHDAY":
        return <Gift className="h-4 w-4" />
      case "WORK_ANNIVERSARY":
        return <Award className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getEventColor = (type: string) => {
    switch (type) {
      case "ANNOUNCEMENT":
        return "bg-blue-100 text-blue-800"
      case "EVENT":
        return "bg-green-100 text-green-800"
      case "BIRTHDAY":
        return "bg-pink-100 text-pink-800"
      case "WORK_ANNIVERSARY":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getAllEvents = () => {
    const allEvents = [
      ...(eventsData?.announcement ?? []),
      ...(eventsData?.event ?? []),
      ...(eventsData?.birthday ?? []),
      ...(eventsData?.work_anniversary ?? []),
    ]

    return allEvents.sort((a, b) => new Date(b.announcement_date).getTime() - new Date(a.announcement_date).getTime())
  }

  const filteredEmployees = employeesData?.results.filter(
    (employee) =>
      `${employee.employee_first_name} ${employee.employee_last_name}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      employee?.department_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.email.toLowerCase().includes(searchTerm.toLowerCase()),
  )


  return (
    <>
      <main className=" w-full bg-[#F8F9FB] px-3 pb-4 pt-6 md:px-8">

        <Tabs defaultValue='profile'>
          <TabsList className='w-full md:pl-12 grid grid-cols-2 md:flex bg-white justify-start'>
            <TabsTrigger value='profile'>
              My Profile
            </TabsTrigger>

            <TabsTrigger value='company_view'>
              Company overview
            </TabsTrigger>

          </TabsList>
          <TabsContent value='profile'>
            <div className='bg-white px-6 pb-4 pt-8 rounded-10'>
              <div className='flex flex-col md:flex-row gap-4'>
                <article className='flex'>
                  <div className='relative'>
                    <div className="w-[200px] md:w-[128px] h-[114px] bg-[#F2F5FF] rounded-lg flex items-center justify-center overflow-hidden">
                      {profileUrl ? (
                        <div
                          className="w-full h-full rounded-lg flex items-center justify-center overflow-hidden bg-cover bg-center"
                          style={{ backgroundImage: `url(${profileUrl})` }}
                        />
                      ) : (
                        <svg fill="none" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
                          <path d="M15 22.75H9C3.57 22.75 1.25 20.43 1.25 15V9C1.25 3.57 3.57 1.25 9 1.25H15C20.43 1.25 22.75 3.57 22.75 9V15C22.75 20.43 20.43 22.75 15 22.75ZM9 2.75C4.39 2.75 2.75 4.39 2.75 9V15C2.75 19.61 4.39 21.25 9 21.25H15C19.61 21.25 21.25 19.61 21.25 15V9C21.25 4.39 19.61 2.75 15 2.75H9Z" fill="#032282" />
                          <path d="M9 10.75C7.48 10.75 6.25 9.52 6.25 8C6.25 6.48 7.48 5.25 9 5.25C10.52 5.25 11.75 6.48 11.75 8C11.75 9.52 10.52 10.75 9 10.75ZM9 6.75C8.31 6.75 7.75 7.31 7.75 8C7.75 8.69 8.31 9.25 9 9.25C9.69 9.25 10.25 8.69 10.25 8C10.25 7.31 9.69 6.75 9 6.75Z" fill="#032282" />
                          <path d="M2.67075 19.6996C2.43075 19.6996 2.19075 19.5796 2.05075 19.3696C1.82075 19.0296 1.91075 18.5596 2.26075 18.3296L7.19075 15.0196C8.27075 14.2896 9.76075 14.3796 10.7407 15.2096L11.0707 15.4996C11.5707 15.9296 12.4207 15.9296 12.9107 15.4996L17.0707 11.9296C18.1307 11.0196 19.8007 11.0196 20.8707 11.9296L22.5007 13.3296C22.8107 13.5996 22.8507 14.0696 22.5807 14.3896C22.3107 14.6996 21.8407 14.7396 21.5207 14.4696L19.8907 13.0696C19.3907 12.6396 18.5407 12.6396 18.0407 13.0696L13.8807 16.6396C12.8207 17.5496 11.1507 17.5496 10.0807 16.6396L9.75075 16.3496C9.29075 15.9596 8.53075 15.9196 8.02075 16.2696L3.09075 19.5796C2.96075 19.6596 2.81075 19.6996 2.67075 19.6996Z" fill="#032282" />
                        </svg>
                      )}
                    </div>

                    <button className="absolute -top-3 -left-3 bg-[#032282] py-3 px-[11px] rounded-full text-white text-center text-xxs z-10">
                      {parseFloat(progress?.toString() || '0').toFixed(0)}%
                    </button>
                  </div>
                </article>

                <div className='flex-1'>
                  <h2 className='text-[#0E0E2C] font-bold text-xl'>{employee_profile?.employee_first_name} {employee_profile?.employee_last_name}</h2>
                  <article className='grid grid-cols-2 md:grid-cols-3 gap-x-10 gap-y-3'>
                    {
                      EmployeeCards?.map((profile_det, index) => (
                        <div key={index} >
                          <p className='font-bold text-[#4A4A68] text-sm'>{profile_det?.employee_det}</p>
                          <p className='text-[#8C8CA1] text-xs'>{profile_det?.title}</p>
                        </div>
                      ))
                    }
                  </article>
                </div>
              </div>

              <div className='flex justify-between flex-col md:flex-row md:items-center w-full gap-7 items-start'>
                <div className='flex gap-4 mt-2 items-center'>
                  <LinkButton href={`/payroll/payroll-profile/${companyid}/${companyName}`}>Profile update</LinkButton>
                  <p className='text-[#4A4A68] font-medium text-sm'>profile is  {parseFloat(progress?.toString() || '0').toFixed(0)}% complete</p>
                </div>
                <div className='flex gap-4 mt-2 items-center'>
                  <Button onClick={() => openRequestLeaveDialog()}>Request leave</Button>
                </div>
              </div>
            </div>

            <article className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-3'>
              <div className='bg-white rounded-10 pl-5 pr-14 pt-6 pb-16'>
                <article className='flex justify-between items-center'>
                  <div className='flex items-center gap-3'>
                    <BioDataIcon />
                    <p className='text-[#0E0E2C] font-medium'>Bio data</p>
                  </div>
                  <EditProfileIcon />
                </article>
                <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                <div className='grid grid-cols-2 gap-y-3'>
                  {BioData?.map((personalData, index) => (
                    <div key={index}>
                      <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                      <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                    </div>
                  ))}
                </div>
              </div>
              <article>
                <div className='bg-white rounded-10 pl-5 pr-14 py-6 '>
                  <div className='flex justify-between items-center'>
                    <div className='flex items-center gap-3'>
                      <LocationIcon />
                      <p className='text-[#0E0E2C] font-medium'>Address</p>
                    </div>
                    <EditProfileIcon />
                  </div>
                  <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                  <div className='mb-4'>
                    <p className='text-sm font-medium text-[#4A4A68]'></p>
                    <p className='text-xs text-[#8C8CA1]'>Residential Address</p>
                  </div>
                  <div className='grid grid-cols-2 gap-y-3'>
                    {Address?.map((personalData, index) => (
                      <div key={index}>
                        <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                        <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                      </div>
                    ))}
                  </div>
                </div>
                <div className='bg-white rounded-10 pl-5 pr-14 pt-6 pb-14 mt-4'>
                  <div className='flex justify-between items-center'>
                    <div className='flex items-center gap-3'>
                      <AccDetailIcon />
                      <p className='text-[#0E0E2C] font-medium'>Account details</p>
                    </div>
                    <EditProfileIcon />
                  </div>
                  <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                  <div className='grid grid-cols-2 gap-y-3'>
                    {AccDet?.map((personalData, index) => (
                      <div key={index}>
                        <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                        <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </article>
              <article>
                <div className='bg-white rounded-10 pl-5 pr-14 py-6'>
                  <div className='flex justify-between items-center'>
                    <div className='flex items-center gap-3'>
                      <EducationIcon />
                      <p className='text-[#0E0E2C] font-medium'>Education Qualification</p>
                    </div>
                    <EditProfileIcon />
                  </div>
                  <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                  <div className='grid grid-cols-2 items-center'>
                    <p className='text-[#4A4A68] text-sm font-medium'>Qualification 1</p>
                    <Button className='bg-[#F7F9FF] text-[#032282] w-fit'>Download</Button>
                  </div>
                  <div className='my-4'>
                    <p className='text-sm font-medium text-[#4A4A68]'>{getEmployeeCertifications?.[0]?.institution_name}</p>
                    <p className='text-xs text-[#8C8CA1]'>School</p>
                  </div>
                  <div className='grid grid-cols-2 gap-y-3'>
                    {EduQualification?.map((personalData, index) => (
                      <div key={index}>
                        <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                        <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                      </div>
                    ))}
                  </div>
                  <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                  <div className='grid grid-cols-2 items-center'>
                    <p className='text-[#4A4A68] text-sm font-medium'>Qualification 2</p>
                    <Button className='bg-[#F7F9FF] text-[#032282] w-fit'>Download</Button>
                  </div>
                  <div className='my-4'>
                    <p className='text-sm font-medium text-[#4A4A68]'>{getEmployeeCertifications?.[0]?.institution_name}</p>
                    <p className='text-xs text-[#8C8CA1]'>School</p>
                  </div>
                  <div className='grid grid-cols-2 gap-y-3'>
                    {EduQualification?.map((personalData, index) => (
                      <div key={index}>
                        <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                        <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </article>
              <div className='bg-white rounded-10 pl-5 pr-14 pt-6 pb-16'>
                <article className='flex justify-between items-center'>
                  <div className='flex items-center gap-3'>
                    <BioDataIcon />
                    <p className='text-[#0E0E2C] font-medium'>Bio data</p>
                  </div>
                  <EditProfileIcon />
                </article>
                <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                <div className='grid grid-cols-2 gap-y-3'>
                  {Next_of_kin_data?.map((personalData, index) => (
                    <div key={index}>
                      <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                      <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                    </div>
                  ))}
                </div>
                <div className='mt-4'>
                  <p className='text-sm font-medium text-[#4A4A68]'></p>
                  <p className='text-xs text-[#8C8CA1]'>Residential Address</p>
                </div>
              </div>
              <article>
                <div className='bg-white rounded-10 pl-5 pr-14 py-6 '>
                  <div className='flex justify-between items-center'>
                    <div className='flex items-center gap-3'>
                      <SalaryDetIcon />
                      <p className='text-[#0E0E2C] font-medium'>Salary details</p>
                    </div>
                    <EditProfileIcon />
                  </div>
                  <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                  <div className='grid grid-cols-2 gap-y-3'>
                    {financialData?.map((personalData, index) => (
                      <div key={index}>
                        <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                        <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                      </div>
                    ))}
                  </div>
                </div>
                <div className='bg-white rounded-10 pl-5 pr-14 pt-6 pb-14 mt-4'>
                  <div className='flex justify-between items-center'>
                    <div className='flex items-center gap-3'>
                      <CertificationIcon />
                      <p className='text-[#0E0E2C] font-medium'>Professional certifications</p>
                    </div>
                    <EditProfileIcon />
                  </div>
                  <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                  <div className='flex flex-col gap-4'>
                    {courses?.map((personalData, index) => (
                      <div className='grid grid-cols-2 text-sm font-medium text-[#4A4A68]' key={index}>
                        <p>{personalData.courseName}</p>
                        <p>{personalData.year}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </article>
              <article>
                <div className='bg-white rounded-10 pl-5 pr-14 py-6'>
                  <div className='flex justify-between items-center'>
                    <div className='flex items-center gap-3'>
                      <ExperienceIcon />
                      <p className='text-[#0E0E2C] font-medium'>Education Qualification</p>
                    </div>
                    <EditProfileIcon />
                  </div>
                  <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                  <div className='mb-4'>
                    <p className='text-sm font-medium text-[#4A4A68]'></p>
                    <p className='text-xs text-[#8C8CA1]'>Company</p>
                  </div>
                  <div className='grid grid-cols-2 gap-y-3'>
                    {positionData?.map((personalData, index) => (
                      <div key={index}>
                        <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                        <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                      </div>
                    ))}
                  </div>
                  <div className='mt-4'>
                    <p className='text-sm font-medium text-[#4A4A68]'>Need a career advancement</p>
                    <p className='text-xs text-[#8C8CA1]'>Reason for leaving</p>
                  </div>
                  <div className='border-[0.2px] border-[#E4E4E4] my-4'></div>
                  <div className='mb-4'>
                    <p className='text-sm font-medium text-[#4A4A68]'></p>
                    <p className='text-xs text-[#8C8CA1]'>Company</p>
                  </div>
                  <div className='grid grid-cols-2 gap-y-3'>
                    {positionData?.map((personalData, index) => (
                      <div key={index}>
                        <p className='text-sm font-medium text-[#4A4A68]'>{personalData.value}</p>
                        <p className='text-xs text-[#8C8CA1]'>{personalData.key}</p>
                      </div>
                    ))}
                  </div>
                  <div className='mt-4'>
                    <p className='text-sm font-medium text-[#4A4A68]'>Need a career advancement</p>
                    <p className='text-xs text-[#8C8CA1]'>Reason for leaving</p>
                  </div>
                </div>
              </article>
            </article>
          </TabsContent>

          <TabsContent value='company_view'>
            {/* Company Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <Card className="shadow-none">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-[#818181] text-sm">Total Employees</p>
                      <p className="text-3xl font-bold">{employeesData?.count}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-none border-0 ">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-[#818181] text-sm">Active Events</p>
                      <p className="text-3xl font-bold">{getAllEvents().length}</p>
                    </div>
                    <Calendar className="h-8 w-8 text-green-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-none border-0 ">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-[#818181] text-sm">Announcements</p>
                      <p className="text-3xl font-bold">{eventsData?.announcement.length}</p>
                    </div>
                    <Building className="h-8 w-8 text-purple-400" />
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-none border-0 ">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-[#818181] text-sm">Birthdays This Month</p>
                      <p className="text-3xl font-bold">{eventsData?.birthday.length}</p>
                    </div>
                    <Gift className="h-8 w-8 text-orange-400" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
              {/* Events Section */}
              <div className="xl:col-span-2 space-y-6">
                <Card className="shadow-sm border-0">
                  <CardHeader className="pb-4">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                      <div>
                        <CardTitle className="text-xl flex items-center gap-2">
                          <Bell className="h-5 w-5 text-blue-600" />
                          Company Events & Announcements
                        </CardTitle>
                        <CardDescription>Stay updated with the latest company news and events</CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <DropdownMenu>
                          <div>
                            <Button className={cn(getUserRoleResponse?.role === "OWNER" || "ADMIN" ? 'flex items-center' : 'hidden')}
                              onClick={() => { openCreateAnnouncementDialog() }}>
                              <Plus className="h-4 w-4 mr-2" />
                              Add Announcement
                            </Button>
                          </div>
                          <DropdownMenuTrigger asChild>
                            <Button className='bg-transparent border text-[#818181]'>
                              <Filter className="h-4 w-4 mr-2" />
                              Filter
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => setSelectedEventType("all")}>All Events</DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setSelectedEventType("announcement")}>
                              Announcements
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setSelectedEventType("event")}>Events</DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setSelectedEventType("birthday")}>
                              Birthdays
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => setSelectedEventType("work_anniversary")}>
                              Work Anniversaries
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {getAllEvents().length === 0 ? (
                        <div className="text-center text-slate-500 py-8">No events available</div>
                      ) : (
                        getAllEvents().map((event, index) => (
                          <div
                            className="flex items-start space-x-4 p-4 rounded-lg border border-slate-200 hover:bg-slate-50 transition-colors"
                            key={index}
                          >
                            <div className={`p-2 rounded-full ${getEventColor(event.announcement_type)}`}>
                              {getEventIcon(event.announcement_type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="font-semibold text-slate-800 mb-1">{event.announcement_title}</h4>
                                  <p className="text-sm text-slate-600 mb-2">{event.announcement_body}</p>
                                  <div className="flex items-center gap-4 text-xs text-slate-500">
                                    <span>By {event.created_by}</span>
                                    <span>{formatDate(event.announcement_date)}</span>
                                  </div>
                                </div>
                                <Badge className={getEventColor(event.announcement_type)} variant="secondary">
                                  {event.announcement_type.replace("_", " ")}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Employees Section */}
              <div className="space-y-6">
                <Card className="shadow-sm border-0">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-xl flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      Team Directory
                    </CardTitle>
                    <CardDescription>Connect with your colleagues</CardDescription>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 h-4 w-4" />
                      <Input
                        className="pl-10"
                        placeholder="Search employees..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {filteredEmployees?.map((employee) => (
                        <div
                          className="flex items-center space-x-3 p-3 rounded-lg border border-slate-200 hover:bg-slate-50 transition-colors"
                          key={employee.id}
                        >
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-sm">
                              {employee.employee_first_name?.[0]}
                              {employee.employee_last_name?.[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-slate-800 truncate">
                              {employee.employee_first_name} {employee.employee_last_name}
                            </p>
                            <div className="flex items-center gap-2">
                              <Badge className="text-xs" variant="outline">
                                {
                                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                  //@ts-ignore
                                  employee.department_name || "No Department"}
                              </Badge>
                            </div>
                            <p className="text-xs text-slate-500 truncate">{employee.email}</p>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button size="tiny" variant="unstyled" >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem>View Profile</DropdownMenuItem>
                              <DropdownMenuItem>Send Message</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

        </Tabs>
      </main>


      <RequestLeaveDialog
        companyid={companyid}
        companyName={companyName}
        heading={'Request leave'}
        isRequestLeaveDialogOpen={isRequestLeaveDialogOpen}
        setRequestLeaveDialogState={setRequestLeaveDialogState}
        subheading={''}
      />


      <CreateAnnouncementModal
        company_id={companyid}
        isCreatennouncementDialogOpen={isCreatennouncementDialogOpen}
        refetch={refetchEventsData}
        setCreatennouncementDialogState={setCreatennouncementDialogState}
      />
    </>
  );
}