'use client';

import React from 'react';

import { Button, LinkButton, LoaderModal } from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { cn } from '@/utils/classNames';
import { convertNumberToNaira } from '@/utils/currency';
import {
  addCommasToNumber,
  shortenNumberWithReadableSymbols,
} from '@/utils/numbers';

import { useGetSingleCompany, useGetUnutilizedAllocations } from '../api';
import {
  Companies,
  CompaniesTeamsEntity,

} from '../types';
import { AddFundsDialog } from './AddFundsDialog';
import { AllocateTeamBudgetDialog } from './AllocateTeamBudgetDialog';
import { UnutilizedAllocationsDialog } from './UnutilizedAllocationsDialog';
import { WithdrawFundsDialog } from './WithdrawFundDialog';
import { AxiosError } from 'axios';
import SubscriptionErrorModal from './subscription/ErrorSubscriptionModal';

interface CardsProps {
  companies: Companies;
  // unutilizedInfo: UnutilizedAllocationsEntity;
  companyId: string;
  name: string;
}
export const CompanyWalletBalanceCards = ({
  // companies,
  // unutilizedInfo,
  companyId,
  name,
}: CardsProps) => {
  const { data: singleCompany } = useGetSingleCompany(companyId);
  const { state: isShown, toggle: toggleShow } = useBooleanStateControl(true);

  const { data: unutilizedAllocationInfo, error, isLoading } = useGetUnutilizedAllocations(
    companyId,
    // unutilizedInfo
  );

  const {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    teams, wallet, no_of_teams, no_of_members, total_req, pending_req, overall_running_budget, budget, allocated_amount, present_month_allocated_amount, total_current_month_expenses,
  } = singleCompany?.results?.[0] || {};

  const { available_balance, final_balance } = wallet || {};

  const areThereBudgets = !!budget && budget?.length > 0;
  const statusError = error as AxiosError;
  return (
    <>
      {isLoading && <LoaderModal isOpen={isLoading} />}

      {
        (statusError?.response?.status === 403 && !isLoading) && <SubscriptionErrorModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          message={String(statusError?.response?.data?.error as unknown as string) ?? ""}
        />
      }
      {(!isLoading && statusError?.response?.status !== 403) && (
        <div className="flex flex-wrap gap-4 sm:flex-row">
          <div
            className={cn(
              "flex min-w-0 grow flex-col justify-between rounded-xl bg-main-solid bg-[url('/images/requisition/card-backgrounds/diagonal-triangles.svg')] bg-contain bg-right bg-no-repeat p-6 text-white sm:p-3 md:p-6"
            )}
          >
            <div className="mb-3 flex items-end justify-between gap-2 md:gap-6">
              <p className="overflow-x-auto">
                <span className="mb-0.5 block text-xs text-[#cdcdcd]">
                  Company wallet
                </span>

                <span className="block overflow-x-auto">
                  <span className="font-heading text-xl font-semibold">
                    {isShown
                      ? `₦${convertNumberToNaira(Number(available_balance), false)}`
                      : '****'}
                  </span>
                </span>
              </p>

              <Button
                className="shrink-0 pb-0.5"
                size="unstyled"
                variant="unstyled"
                onClick={toggleShow}
              >
                <span className="sr-only">
                  {isShown ? 'Hide balance' : 'Show balance'}
                </span>
                <svg
                  fill="none"
                  height={24}
                  viewBox="0 0 24 24"
                  width={24}
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 16.33c-2.39 0-4.33-1.94-4.33-4.33S9.61 7.67 12 7.67s4.33 1.94 4.33 4.33-1.94 4.33-4.33 4.33Zm0-7.16c-1.56 0-2.83 1.27-2.83 2.83s1.27 2.83 2.83 2.83 2.83-1.27 2.83-2.83S13.56 9.17 12 9.17Z"
                    fill="#fff"
                  />
                  <path
                    d="M12 21.02c-3.76 0-7.31-2.2-9.75-6.02-1.06-1.65-1.06-4.34 0-6 2.45-3.82 6-6.02 9.75-6.02s7.3 2.2 9.74 6.02c1.06 1.65 1.06 4.34 0 6-2.44 3.82-5.99 6.02-9.74 6.02Zm0-16.54c-3.23 0-6.32 1.94-8.48 5.33-.75 1.17-.75 3.21 0 4.38 2.16 3.39 5.25 5.33 8.48 5.33 3.23 0 6.32-1.94 8.48-5.33.75-1.17.75-3.21 0-4.38-2.16-3.39-5.25-5.33-8.48-5.33Z"
                    fill="#fff"
                  />
                </svg>
              </Button>
            </div>

            <div>
              <div className="flex flex-wrap items-center gap-2">
                <p className="mb-2 text-xs">
                  Bal: {final_balance && final_balance < 0 ? '-' : ''}₦
                  {shortenNumberWithReadableSymbols(Math.abs(final_balance as number), 1)}
                </p>
                <p className="mb-2 text-xs">
                  Allocation: ₦
                  {shortenNumberWithReadableSymbols(Number(allocated_amount), 1)} (all
                  time), ₦
                  {shortenNumberWithReadableSymbols(
                    present_month_allocated_amount as number,
                    1
                  )}{' '}
                  (this month)
                </p>
                <p className="mb-2 hidden text-xs">
                  Current month allocation: ₦
                  {shortenNumberWithReadableSymbols(
                    present_month_allocated_amount as number,
                    1
                  )}
                </p>
              </div>

              <div className="flex gap-2">
                <AddFundsDialog />

                <WithdrawFundsDialog companyWallet={available_balance as number} />

                <AllocateTeamBudgetDialog
                  allocatedAmount={allocated_amount as number}
                  companyId={companyId}
                  companyName={name}
                  teams={teams as CompaniesTeamsEntity[]}
                  wallet={available_balance as number}
                />
              </div>
            </div>
          </div>

          <div
            className={cn(
              'flex grow flex-col justify-between gap-4 rounded-xl bg-white p-6 text-main-solid sm:p-3 md:p-6'
            )}
          >
            <div className="flex justify-between gap-2 md:gap-4">
              <p className="overflow-auto">
                <span className="mb-0.5 block text-xs text-input-placeholder">
                  Overall running budget
                </span>

                <span className="block overflow-x-auto font-heading text-lg font-medium text-black">
                  {convertNumberToNaira(Number(overall_running_budget))}
                </span>
              </p>

              <svg
                fill="none"
                height={40}
                viewBox="0 0 40 40"
                width={40}
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx={20} cy={20} fill="#F2F5FF" r={20} />
                <path
                  d="M15.975 28.959c-1.984 0-3.608-1.442-3.608-3.217v-1.7a.63.63 0 0 1 .624-.625.63.63 0 0 1 .626.625c0 1.042 1.008 1.825 2.358 1.825s2.358-.783 2.358-1.825a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v1.7c0 1.775-1.617 3.217-3.608 3.217Zm-2.142-2.4c.367.683 1.192 1.15 2.142 1.15s1.775-.475 2.141-1.15c-.591.358-1.325.566-2.141.566-.817 0-1.55-.208-2.142-.566Z"
                  fill="#032282"
                />
                <path
                  d="M15.975 24.833c-1.367 0-2.592-.625-3.2-1.617a2.79 2.79 0 0 1-.409-1.458c0-.875.384-1.692 1.084-2.3 1.35-1.183 3.675-1.183 5.033-.008.7.616 1.092 1.433 1.092 2.308a2.79 2.79 0 0 1-.409 1.458c-.6.992-1.825 1.617-3.191 1.617Zm0-5.042c-.65 0-1.25.217-1.7.608-.425.367-.659.85-.659 1.359 0 .291.075.558.225.808.384.634 1.2 1.025 2.134 1.025.933 0 1.75-.391 2.125-1.016.15-.242.225-.517.225-.809 0-.508-.234-.991-.659-1.366-.441-.392-1.041-.609-1.691-.609Z"
                  fill="#032282"
                />
                <path
                  d="M15.975 27.125c-2.059 0-3.608-1.325-3.608-3.075v-2.29c0-1.775 1.616-3.217 3.608-3.217.941 0 1.841.325 2.516.908.7.617 1.092 1.434 1.092 2.309v2.291c0 1.75-1.55 3.075-3.608 3.075Zm0-7.333c-1.3 0-2.358.883-2.358 1.967v2.291c0 1.042 1.008 1.825 2.358 1.825s2.358-.783 2.358-1.825v-2.29c0-.509-.233-.992-.658-1.367a2.61 2.61 0 0 0-1.7-.6Zm9.891 2.542c-1.258 0-2.325-.934-2.424-2.134a2.287 2.287 0 0 1 .683-1.858 2.258 2.258 0 0 1 1.633-.675H27.5c.825.025 1.458.675 1.458 1.475v1.717c0 .8-.633 1.45-1.433 1.475h-1.659Zm1.609-3.417h-1.709c-.291 0-.558.108-.75.308a1.05 1.05 0 0 0-.324.867c.041.55.575.992 1.174.992H27.5c.108 0 .208-.1.208-.225v-1.717c0-.125-.1-.217-.233-.225Z"
                  fill="#032282"
                />
                <path
                  d="M23.334 27.709H21.25a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h2.084c2.15 0 3.541-1.392 3.541-3.542v-.583h-1.008c-1.259 0-2.325-.934-2.425-2.134a2.287 2.287 0 0 1 .683-1.858 2.259 2.259 0 0 1 1.633-.675h1.109v-.583c0-1.95-1.142-3.292-2.992-3.509-.2-.033-.375-.033-.55-.033h-7.5c-.2 0-.392.017-.583.042-1.834.233-2.959 1.566-2.959 3.5v1.666a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625v-1.666c0-2.567 1.584-4.425 4.042-4.734.225-.033.483-.058.75-.058h7.5c.2 0 .458.008.725.05 2.458.283 4.067 2.15 4.067 4.742v1.208a.63.63 0 0 1-.625.625h-1.734c-.291 0-.558.108-.75.308a1.05 1.05 0 0 0-.325.867c.042.55.576.992 1.175.992H27.5a.63.63 0 0 1 .625.625v1.208c0 2.867-1.925 4.792-4.791 4.792Z"
                  fill="#032282"
                />
              </svg>
            </div>

            <LinkButton
              className="block px-3 py-2.5 text-xs md:text-sm"
              href={
                areThereBudgets
                  ? `/spend-management/budgeting/all-budgets/?companyId=${companyId}`
                  : `/spend-management/budgeting/create-company-budget?companyId=${companyId}&companyName=${name}`
              }
              size="fullWidth"
              variant="light"
            >
              {areThereBudgets ? 'View budgets' : 'Create budget'}
            </LinkButton>
          </div>

          <div
            className={cn(
              'flex grow flex-col justify-between gap-[1.125rem] rounded-xl bg-white p-6 text-main-solid sm:p-3 md:p-6'
            )}
          >
            <div className="flex justify-between gap-2 md:gap-4">
              <p className="overflow-auto">
                <span className="mb-0.5 block text-xs text-input-placeholder">
                  Current month expense
                </span>

                <span className="block overflow-x-auto font-heading text-lg font-medium text-black">
                  {total_current_month_expenses ||
                    total_current_month_expenses === 0
                    ? `₦${shortenNumberWithReadableSymbols(
                      total_current_month_expenses,
                      1
                    )}`
                    : 'N/A'}
                </span>
              </p>

              <svg
                fill="none"
                height={40}
                viewBox="0 0 40 40"
                width={40}
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx={20} cy={20} fill="#F2F5FF" r={20} />
                <path
                  d="M15.975 28.959c-1.984 0-3.608-1.442-3.608-3.217v-1.7a.63.63 0 0 1 .624-.625.63.63 0 0 1 .626.625c0 1.042 1.008 1.825 2.358 1.825s2.358-.783 2.358-1.825a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v1.7c0 1.775-1.617 3.217-3.608 3.217Zm-2.142-2.4c.367.683 1.192 1.15 2.142 1.15s1.775-.475 2.141-1.15c-.591.358-1.325.566-2.141.566-.817 0-1.55-.208-2.142-.566Z"
                  fill="#032282"
                />
                <path
                  d="M15.975 24.833c-1.367 0-2.592-.625-3.2-1.617a2.79 2.79 0 0 1-.409-1.458c0-.875.384-1.692 1.084-2.3 1.35-1.183 3.675-1.183 5.033-.008.7.616 1.092 1.433 1.092 2.308a2.79 2.79 0 0 1-.409 1.458c-.6.992-1.825 1.617-3.191 1.617Zm0-5.042c-.65 0-1.25.217-1.7.608-.425.367-.659.85-.659 1.359 0 .291.075.558.225.808.384.634 1.2 1.025 2.134 1.025.933 0 1.75-.391 2.125-1.016.15-.242.225-.517.225-.809 0-.508-.234-.991-.659-1.366-.441-.392-1.041-.609-1.691-.609Z"
                  fill="#032282"
                />
                <path
                  d="M15.975 27.125c-2.059 0-3.608-1.325-3.608-3.075v-2.29c0-1.775 1.616-3.217 3.608-3.217.941 0 1.841.325 2.516.908.7.617 1.092 1.434 1.092 2.309v2.291c0 1.75-1.55 3.075-3.608 3.075Zm0-7.333c-1.3 0-2.358.883-2.358 1.967v2.291c0 1.042 1.008 1.825 2.358 1.825s2.358-.783 2.358-1.825v-2.29c0-.509-.233-.992-.658-1.367a2.61 2.61 0 0 0-1.7-.6Zm9.891 2.542c-1.258 0-2.325-.934-2.424-2.134a2.287 2.287 0 0 1 .683-1.858 2.258 2.258 0 0 1 1.633-.675H27.5c.825.025 1.458.675 1.458 1.475v1.717c0 .8-.633 1.45-1.433 1.475h-1.659Zm1.609-3.417h-1.709c-.291 0-.558.108-.75.308a1.05 1.05 0 0 0-.324.867c.041.55.575.992 1.174.992H27.5c.108 0 .208-.1.208-.225v-1.717c0-.125-.1-.217-.233-.225Z"
                  fill="#032282"
                />
                <path
                  d="M23.334 27.709H21.25a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h2.084c2.15 0 3.541-1.392 3.541-3.542v-.583h-1.008c-1.259 0-2.325-.934-2.425-2.134a2.287 2.287 0 0 1 .683-1.858 2.259 2.259 0 0 1 1.633-.675h1.109v-.583c0-1.95-1.142-3.292-2.992-3.509-.2-.033-.375-.033-.55-.033h-7.5c-.2 0-.392.017-.583.042-1.834.233-2.959 1.566-2.959 3.5v1.666a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625v-1.666c0-2.567 1.584-4.425 4.042-4.734.225-.033.483-.058.75-.058h7.5c.2 0 .458.008.725.05 2.458.283 4.067 2.15 4.067 4.742v1.208a.63.63 0 0 1-.625.625h-1.734c-.291 0-.558.108-.75.308a1.05 1.05 0 0 0-.325.867c.042.55.576.992 1.175.992H27.5a.63.63 0 0 1 .625.625v1.208c0 2.867-1.925 4.792-4.791 4.792Z"
                  fill="#032282"
                />
              </svg>
            </div>

            <div className="flex items-end justify-between gap-2 md:gap-4">
              <p className="overflow-auto">
                <span className="mb-0.5 block text-xs text-input-placeholder">
                  Unutilized
                </span>

                <span className="block overflow-x-auto font-heading text-lg font-medium text-black">
                  ₦
                  {addCommasToNumber(
                    Number(unutilizedAllocationInfo?.company?.total_un_utilized)
                  )}
                </span>
              </p>
              {/* 
          <Button
            className="inline-block px-2.5 py-[.3125rem] text-xxs"
            variant="light"
            disabled
          >
            View ////
          </Button> */}

              <UnutilizedAllocationsDialog
                companyId={companyId}
              // unutilizedInfo={unutilizedInfo}
              />
            </div>
          </div>

          <div className="grid grow grid-cols-2 items-end gap-x-2 gap-y-5 rounded-xl bg-white p-6 sm:gap-x-4 sm:p-3 md:grid-cols-2 md:p-6 lg:grow lg:grid-cols-4 xl:gap-0">
            <p className="h-max xl:border-r xl:border-card-border">
              <span className="xl:mx-auto xl:block xl:w-max">
                <span className="block">{no_of_teams}</span>
                <span className="block text-xs text-input-placeholder">Teams</span>
              </span>
            </p>

            <p className="h-max xl:border-r xl:border-card-border">
              <span className="xl:mx-auto xl:block xl:w-max">
                <span className="block">{no_of_members}</span>
                <span className="block text-xs text-input-placeholder">
                  Members
                </span>
              </span>
            </p>

            <p className="h-max xl:border-r xl:border-card-border">
              <span className="xl:mx-auto xl:block xl:w-max">
                <span className="block">{total_req}</span>
                <span className="block text-xs text-input-placeholder">
                  Requisitions
                </span>
              </span>
            </p>

            <p className="h-max grow basis-[28%]">
              <span className="xl:mx-auto xl:block xl:w-max">
                <span className="block">{pending_req}</span>
                <span className="flex items-center gap-1 text-xs text-input-placeholder sm:gap-2">
                  <span className="inline-block h-2 w-2 rounded-full bg-[#F8A629]"></span>
                  <span>Pending</span>
                </span>
              </span>
            </p>
          </div>
        </div>)}
    </>
  );
};
