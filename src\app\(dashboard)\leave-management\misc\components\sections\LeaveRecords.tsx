import React from 'react';
import { LeaveCard } from '../LeaveCard';
import { LeaveRecordsTable } from '../tables';
import { LeaveResultsData, useLeaveRecordList } from '../../api/getLeaveRecord';
import { useLeaveManagementDashboard } from '../../api/getLeaveManagementDashboard';


export type LeaveRecordsProps = {
    userId: string;
    company_id: string;
    company_name: string;
};

function LeaveRecords({ company_id }: LeaveRecordsProps) {

    const fetchOptions = {
        companyid: company_id
    };

    const { data: leaveManagementDashboardData, isLoading: isLeaveManagementDashboardLoading } = useLeaveManagementDashboard(fetchOptions);
    const { data: leaveRecordList, isLoading: isLeaveRecordListLoading } = useLeaveRecordList(fetchOptions);

    return (
        <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 space-y-8">
            <div className="flex flex-col gap-6">
                <div className="flex justify-between items-center">
                    <h1 className="text-3xl font-semibold text-neutral-800">
                        Leave Records
                    </h1>
                    <div className="flex gap-4">
                        {!isLeaveManagementDashboardLoading && !isLeaveRecordListLoading && (
                            <span className="text-sm text-neutral-500">
                                Total Records: {leaveRecordList?.count || 0}
                            </span>
                        )}
                    </div>
                </div>

                <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <LeaveCard
                        number={leaveManagementDashboardData?.active_leave || 0}
                        title="Total active leave"
                    />
                    <LeaveCard
                        number={leaveManagementDashboardData?.total_leave || 0}
                        title="Total leave request"
                    />
                    <LeaveCard
                        number={leaveManagementDashboardData?.approved_leave || 0}
                        title="Total approved request"
                    />
                    <LeaveCard
                        number={leaveManagementDashboardData?.rejected_leave || 0}
                        title="Total declined request"
                    />
                </section>
            </div>

            <section className="bg-white border border-neutral-200/50 rounded-xl overflow-hidden shadow-sm">
                {isLeaveRecordListLoading ? (
                    <div className="flex items-center justify-center h-64">
                        <p className="text-neutral-500">Loading records...</p>
                    </div>
                ) : (
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    <LeaveRecordsTable
                        data={leaveRecordList as LeaveResultsData}
                    />
                )}
            </section>
        </div>
    );
}

export default LeaveRecords;