'use client'
import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';


import { SelectCompanyDialog } from '../../../misc/components/modals/SelectCompanyDialog';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';

const SelectCompany = () => {

  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companiesList } = companiesResponse as Companies;


  return (
    <>
      <SelectCompanyDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyLists={companiesList}
        link={'/stock/product-and-categories/view/'}
      />
    </>
  );
};

export default SelectCompany;
