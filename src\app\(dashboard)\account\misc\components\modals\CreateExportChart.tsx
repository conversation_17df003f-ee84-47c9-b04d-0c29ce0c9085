import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alog<PERSON>ontent, DialogTitle, DialogClose, DialogBody, SuccessModal, ErrorModal, LinkButton } from '@/components/core';
import React, { useCallback, useState, useRef } from 'react'
import { SmallSpinner } from '@/icons/core';
import { useUploadCSVfile } from '../../api/uploadCSVfile';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';
import { useSearchParams } from 'next/navigation';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';


interface UseBooleanStateControlProps {
    isExportChartModal: boolean;
    setIsExportChartModal: React.Dispatch<
        React.SetStateAction<boolean>>;
    heading: string;
    children?: React.ReactNode;
}

function ExportChartModal({
    isExportChartModal,
    setIsExportChartModal,
    heading,
}: UseBooleanStateControlProps) {

    const {
        state: isSuccessModalOpen,
        setState: setSuccessModalState,
    } = useBooleanStateControl();

    const {
        isErrorModalOpen,
        setErrorModalState,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();


    // file upload
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const [uploadedFile, setUploadedFile] = useState<File | null>(null);

    const { data: salesUserDetails } = useDefaultCompanyBranch();
    React.useEffect(() => {
        //
    }, [])

    const params = useSearchParams();

    const company = params.get('company') || salesUserDetails?.data.company_id;

    const { mutate: uploadJournalFile, isLoading } = useUploadCSVfile()

    const handleFile = (file: File) => {
        if (
            file.type === 'text/csv' || // PDF files
            file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.type === 'application/vnd.ms-excel'     // Any image file (jpeg, png, etc.)
        ) {
            setUploadedFile(file);
            uploadJournalFile({
                company: company as string,
                file
            }, {
                onSuccess: () => {
                    setSuccessModalState(true);
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                },
            });
            // se tValue("suppliers.certificate_of_reg", file, { shouldValidate: true });
        } else {
            alert('Unsupported file type. Only images and PDFs are allowed.');
        }
    };
    const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        const files = Array.from(event.dataTransfer.files);
        files.forEach(handleFile);
    }, []);
    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
    };
    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        files.forEach(handleFile);
        // Reset the file input so that the same file can be selected again if needed
        if (fileInputRef.current) fileInputRef.current.value = "";
    };

    // const handleDownloadSamples = () => {
    //     const ws = XLSX.utils.json_to_sheet(CSVFileMockData);
    //     // Create a new workbook and add the worksheet
    //     const wb = XLSX.utils.book_new();
    //     XLSX.utils.book_append_sheet(wb, ws, 'Journals');
    //     // Generate the Excel file and trigger download
    //     XLSX.writeFile(wb, 'journal_file.csv');
    // }

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
    }

    return (
        <div>
            <ClientOnly>
                <Dialog open={isExportChartModal}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>{heading}</DialogTitle>
                            <DialogClose onClick={() => setIsExportChartModal(false)}>close</DialogClose>
                        </DialogHeader>
                        <DialogBody>
                            {/* <div className='border-dashed border-2 border-[#2867C6] gap-3 pt-6 pb-2.5 rounded-2xl flex flex-col justify-center items-center'>
                                <CSVfile />
                                <h3 className='font-medium text-[#0E0E2C] items-center text-xs max-w-[148px]'>Drag and drop your file(s) here <span className='font-bold'> Click to upload</span> </h3>
                                <p className='text-[#032282] text-xxs'>Files types: CSV. </p>
                            </div>
                            <div className='flex gap-6 bg-[#EFF5FF] px-4 py-3 mt-4 rounded-10'>
                                <p className='text-xxs text-[#032282]'>Download sample file and ensure required fields are completed before upload</p>
                                <Button>Download</Button>
                            </div> */}


                            {
                                <form className=" flex flex-col w-full" encType='multipart/form-data' onSubmit={handleSubmit}>
                                    <div className="mb-auto grow w-full ">
                                        {isLoading ? <div className="border-2 border-dashed cursor-pointer mt-3 w-full flex justify-center items-center flex-col rounded-lg bg-white border-gray-400  p-5 text-center mb-4"
                                        ><SmallSpinner color='blue' /></div> : <div
                                            className="border-2 border-dashed cursor-pointer mt-3 w-full flex justify-center items-center flex-col rounded-lg bg-white border-gray-400  p-5 text-center mb-4"
                                            onClick={() => fileInputRef.current?.click()}
                                            onDragOver={handleDragOver}
                                            onDrop={handleDrop}
                                        >
                                            <svg fill="none" height="48" viewBox="0 0 48 48" width="48" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="24" cy="24" fill="#EFF5FF" r="24" />
                                                <path d="M20.9999 29.75C20.5899 29.75 20.2499 29.41 20.2499 29V24.81L19.5299 25.53C19.2399 25.82 18.7599 25.82 18.4699 25.53C18.1799 25.24 18.1799 24.76 18.4699 24.47L20.4699 22.47C20.6799 22.26 21.0099 22.19 21.2899 22.31C21.5699 22.42 21.7499 22.7 21.7499 23V29C21.7499 29.41 21.4099 29.75 20.9999 29.75Z" fill="#032282" />
                                                <path d="M22.9999 25.7499C22.8099 25.7499 22.6199 25.6799 22.4699 25.5299L20.4699 23.5299C20.1799 23.2399 20.1799 22.7599 20.4699 22.4699C20.7599 22.1799 21.2399 22.1799 21.5299 22.4699L23.5299 24.4699C23.8199 24.7599 23.8199 25.2399 23.5299 25.5299C23.3799 25.6799 23.1899 25.7499 22.9999 25.7499Z" fill="#032282" />
                                                <path d="M27 34.75H21C15.57 34.75 13.25 32.43 13.25 27V21C13.25 15.57 15.57 13.25 21 13.25H26C26.41 13.25 26.75 13.59 26.75 14C26.75 14.41 26.41 14.75 26 14.75H21C16.39 14.75 14.75 16.39 14.75 21V27C14.75 31.61 16.39 33.25 21 33.25H27C31.61 33.25 33.25 31.61 33.25 27V22C33.25 21.59 33.59 21.25 34 21.25C34.41 21.25 34.75 21.59 34.75 22V27C34.75 32.43 32.43 34.75 27 34.75Z" fill="#032282" />
                                                <path d="M34 22.75H30C26.58 22.75 25.25 21.42 25.25 18V14C25.25 13.7 25.43 13.42 25.71 13.31C25.99 13.19 26.31 13.26 26.53 13.47L34.53 21.47C34.74 21.68 34.81 22.01 34.69 22.29C34.57 22.57 34.3 22.75 34 22.75ZM26.75 15.81V18C26.75 20.58 27.42 21.25 30 21.25H32.19L26.75 15.81Z" fill="#032282" />
                                            </svg>
                                            <div className="text-center flex justify-center items-center flex-col">
                                                <p className='text-xs sm:text-sm text-[#0E0E2C] font-semibold mt-2 w-[70%]'>Drag and drop your file(s)
                                                    here or Click to browse and upload
                                                </p>
                                                <p className='text-xs text-[#032282] mt-2'>Files types: CSV. </p>
                                            </div>
                                            <div>
                                                <input
                                                    accept=".csv, .xls, .xlsx"
                                                    className="hidden"
                                                    ref={fileInputRef}
                                                    type="file"
                                                    onChange={handleFileChange}
                                                />
                                            </div>
                                            <p className='text-xs mt-3 text-[#6D6D6D]'>{uploadedFile?.name}</p>
                                        </div>
                                        }
                                    </div>
                                    <div className="w-full bg-[#EFF5FF] rounded-10 fw px-4 py-[.6875rem] flex gap-6">
                                        <p className='text-xxs text-[#032282]'>Download sample file and ensure required fields are completed before upload</p>
                                        <LinkButton
                                            className=""
                                            href="/files/sample_journal_upload.csv"
                                            target="_blank" type="button">
                                            Download
                                        </LinkButton>
                                    </div>
                                </form>
                            }
                        </DialogBody>
                    </DialogContent>
                </Dialog>
            </ClientOnly>

            {isSuccessModalOpen &&
                <SuccessModal
                    heading="CSV uploaded successfully!"
                    isSuccessModalOpen={isSuccessModalOpen}
                    setSuccessModalState={setSuccessModalState}
                    subheading=""
                >
                    <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
                        <LinkButton
                            className="grow text-base"
                            href="/account/journal-entry"
                            size="fullWidth"
                        >
                            Okay
                        </LinkButton>
                    </div>
                </SuccessModal>
            }
            {isErrorModalOpen &&
                <ErrorModal
                    isErrorModalOpen={isErrorModalOpen}
                    setErrorModalState={setErrorModalState}
                    subheading={errorModalMessage || 'Please check your inputs and try again.'}
                >
                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                        <LinkButton
                            className="grow text-base"
                            href="/account/journal-entry"
                            size="fullWidth"
                        >
                            Okay
                        </LinkButton>
                    </div>
                </ErrorModal>
            }
        </div>
    )
}

export default ExportChartModal