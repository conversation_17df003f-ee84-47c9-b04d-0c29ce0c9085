'use client'

/* eslint-disable tailwindcss/no-contradicting-classname */
import React from 'react';

import { LinkButton, LoaderModal } from '@/components/core';
import { cn } from '@/utils/classNames';
import { CompanySummaryCard } from '../misc/components/CompanySummaryCard';
import { GenericLoaderFallback } from '@/icons/core';


import { CompaniesData } from '../misc/types';
import { CompaniesList } from '../misc/components/CompaniesAndCardsTabs';
import { useGetCompaniesOverview } from '../misc/api/getCompaniesOverview';

const Page = () => {

  const { data: companiesResponse, isLoading } = useGetCompaniesOverview();

  return (
    <>
      <LoaderModal isOpen={isLoading} />
      <div>
        <div className="relative z-2 mb-2 overflow-hidden bg-[#EBEFFB] px-6 py-4 md:rounded-10 md:bg-requisition-companies-bg-gradient md:px-7 lg:px-11">
          <div className="lg:flex lg:gap-4">
            <div className="flex flex-col rounded-xl bg-white lg:grow">
              <div className="flex max-w-[13rem] gap-2 rounded-xl bg-white px-3.5 pb-0 pt-4 min-[380px]:px-5 sm:gap-4 md:px-6">
                <LinkButton
                  className="grow px-1.5 py-2.5 sm:text-sm md:px-6"
                  href="/stock/create-company"
                  size="tiny"
                >
                  Create company
                </LinkButton>
              </div>

              <CompanySummaryCard data={companiesResponse?.data as CompaniesData} />
            </div>

            <div
              className={cn(
                "hidden rounded-xl bg-main-solid bg-[url('/images/requisition/card-backgrounds/budgeting-card-cubes.png')] bg-contain bg-right bg-no-repeat px-6 py-5 text-white lg:inline-flex lg:w-1/3 lg:max-w-[19.875rem] lg:shrink lg:flex-col xl:w-[26%]"
              )}
            >
              <h2 className="mb-1 text-lg font-semibold">Companies</h2>
              <p className="mb-2 text-xs font-medium">
                Companies with overview of each
                company and branches
              </p>

            </div>
          </div>
        </div>

        <div className="px-8 md:py-3">
          <div className="md:px-7 lg:px-11">
            <React.Suspense
              fallback={
                <GenericLoaderFallback className="mx-6 rounded-10 md:mx-0" />
              }
            >
              <CompaniesList />
            </React.Suspense>
          </div>
        </div>
      </div>
    </>
  );
};

export default Page;
