"use client"
import { Button, ErrorModal, } from '@/components/core'

import React, { useState } from 'react'

import { useRouter } from 'next/navigation'
import { getInputValueFromForm } from '@/utils/forms'
import { useResetPassword } from '../api/resetPassword'
import { useErrorModalState } from '@/hooks'
import { formatAxiosErrorMessage } from '@/utils/errors'
import { AxiosError } from 'axios'

interface EmailOTPFormProps {
    email: string;
    phone: string
    otp: string
}

const ResetPasswordForm = ({ otp, phone }: EmailOTPFormProps) => {
    const router = useRouter()
    const [isShown, setIsShown] = useState(false)
    const { mutate: resetPassword, isLoading: isResetPasswordLoading } = useResetPassword()

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();


    async function handleResetPassword(event: React.FormEvent<HTMLFormElement>) {
        event.preventDefault()

        const form = event.target as HTMLFormElement;
        const passcode = getInputValueFromForm(form, "password")
        const confirm = getInputValueFromForm(form, "confirm")

        const updatedData = {
            pin1: passcode,
            pin2: confirm,
            phone_number: phone,
            otp_value: otp
        }

        resetPassword(updatedData, {
            onSuccess: () => {
                router.push("/forgot-password/success")
            },
            onError: (error: unknown) => {
                const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                openErrorModalWithMessage(errorMessage);
            },
        })
    }
    return (
        <div className='relative z-10'>
            <form onSubmit={handleResetPassword}>
                <div className='relative mt-3 flex items-center overflow-hidden rounded-lg !bg-white/30 transition duration-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 focus-within:ring-offset-[#403C3A]'>
                    <input
                        className="login-autofill-text login-no-chrome-autofill-bg h-auto min-w-0 grow !bg-transparent py-3.5 pl-6 text-base font-medium text-white placeholder:text-white focus-visible:outline-none"
                        id="password"
                        inputMode="numeric"
                        name="password"
                        pattern="[0-9]*"
                        placeholder="New Passcode"
                        type={isShown ? 'text' : 'password'}
                        required
                    />
                    <Button
                        className="bg-transparent px-6 py-3.5 absolute right-1 top-1/2 -translate-y-1/2"
                        size="unstyled"
                        type="button"
                        variant="unstyled"
                        onClick={() => setIsShown(!isShown)}
                    >
                        <svg
                            fill="none"
                            height={20}
                            viewBox="0 0 20 20"
                            width={20}
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M.833 9.63S4.167 3.21 10 3.21s9.167 6.42 9.167 6.42-3.334 6.42-9.167 6.42S.833 9.63.833 9.63Z"
                                stroke="#fff"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                            <path
                                d="M10 12.037c1.38 0 2.5-1.078 2.5-2.407 0-1.33-1.12-2.408-2.5-2.408S7.5 8.3 7.5 9.63c0 1.33 1.12 2.407 2.5 2.407Z"
                                stroke="#fff"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                        </svg>
                    </Button>
                </div>
                <div className='relative mt-3 flex items-center overflow-hidden rounded-lg !bg-white/30 transition duration-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 focus-within:ring-offset-[#403C3A]'>
                    <input
                        className="login-autofill-text login-no-chrome-autofill-bg h-auto min-w-0 grow !bg-transparent py-3.5 pl-6 text-base font-medium text-white placeholder:text-white focus-visible:outline-none"
                        id="confirm"
                        inputMode="numeric"
                        name="confirm"
                        pattern="[0-9]*"
                        placeholder="Confirm Passcode"
                        type={isShown ? 'text' : 'password'}
                        required
                    />
                    <Button
                        className="bg-transparent px-6 py-3.5 absolute right-1 top-1/2 -translate-y-1/2"
                        size="unstyled"
                        type="button"
                        variant="unstyled"
                        onClick={() => setIsShown(!isShown)}
                    >
                        <svg
                            fill="none"
                            height={20}
                            viewBox="0 0 20 20"
                            width={20}
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M.833 9.63S4.167 3.21 10 3.21s9.167 6.42 9.167 6.42-3.334 6.42-9.167 6.42S.833 9.63.833 9.63Z"
                                stroke="#fff"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                            <path
                                d="M10 12.037c1.38 0 2.5-1.078 2.5-2.407 0-1.33-1.12-2.408-2.5-2.408S7.5 8.3 7.5 9.63c0 1.33 1.12 2.407 2.5 2.407Z"
                                stroke="#fff"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                        </svg>
                    </Button>
                </div>
                <Button
                    className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
                    disabled={isResetPasswordLoading}
                    type="submit"
                    variant="white"
                >
                    {isResetPasswordLoading ? 'Loading' : 'Create password'}

                </Button>
            </form>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </div>
    )
}

export default ResetPasswordForm
