'use client';

import Link from 'next/link';

import {
  <PERSON>alog,
  <PERSON>alogBody,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/core';
import { useBooleanStateControl, useClipboard } from '@/hooks';
import { cn } from '@/utils/classNames';
import { launchNotification } from '@/utils/notifications';

import { AddFundsSecondDialog } from './AddFundsSecondDialog';
import { useParams } from 'next/navigation';
import { useGetSingleCompanyDetails } from '../../companies/company/[id]/[name]/wallet-history/api/fetchCompanyDetails';
import { useGetUserAccountDetails } from '../../companies/company/[id]/[name]/wallet-history/api/getUSerAccountDetails';
import { SmallSpinner } from '@/icons/core';
// import { useGetFundWallet } from '../api';
// 
export function AddFundsDialog() {
  const {
    state: isAddFundSecondDialog,
    setState: setAddFundSecondState,
    // setTrue: openAddSecondDialog,
  } = useBooleanStateControl();
  // const { data: walletDetails } = useGetFundWallet();
  const { id } = useParams()
  const { data } = useGetSingleCompanyDetails(String(id));


  const fetchOptions = {

    id: String(id),
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    account_type: String(data?.results[0]?.account_type)


  };


  // const { account_name, account_number, bank_name } = data?.results[0]?.spend_mgmt_account_details || {}

  const clipboard = useClipboard({ timeout: 500 });

  const isAddFundModalHidden = isAddFundSecondDialog;
  const { data: walletDetailData, isLoading } = useGetUserAccountDetails(fetchOptions)



  // const { account_name, account_number, bank_name, } =
  //   walletDetailData && walletDetailData?.results[0] || {};
  return (
    <Dialog>
      <DialogTrigger className="grow bg-white/10 px-3 py-2.5 text-xs text-white md:text-sm">
        Top up
      </DialogTrigger>

      <DialogContent
        className={cn(isAddFundModalHidden && 'hidden')}
        overlayClassName={cn(isAddFundModalHidden && 'hidden')}
      >
        <DialogHeader>
          <DialogTitle>Top up</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody>
          {
            isLoading ? <div className='flex justify-center items-center py-10 w-full'>
              <SmallSpinner color='blue' />
            </div> :
              <>
                <h2 className="text-sm text-black">
                  Fund wallet with any of the underlisted options
                </h2>

                <div className="mt-4 rounded-10 border border-dashed border-[#9F9F9F] p-6 shadow-card ">
                  <p className="border-b pb-2 text-xs text-black">
                    Fund wallet with transfer
                  </p>

                  <div className="mt-2 flex flex-wrap items-center justify-between">
                    <div>
                      <p className="text-xs text-[#556575]">Account name</p>
                      <p className="text-sm font-semibold capitalize text-black">

                        {
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          data?.results[0]?.spend_mgmt_account_details?.account_name ?? ""}
                      </p>
                    </div>

                    <div className="">
                      <p className="text-xs text-[#556575]">Account No</p>
                      <p className="text-sm font-semibold text-black">
                        {
                          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          data?.results[0]?.spend_mgmt_account_details?.account_number ?? ""}
                      </p>
                    </div>

                    <button
                      className="inline-block"
                      onClick={() => {
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        clipboard.copy(data?.results[0]?.spend_mgmt_account_details?.account_number ?? '');
                        launchNotification('neutral', 'Account number copied');
                      }}
                    >
                      <svg
                        fill="none"
                        height="20"
                        viewBox="0 0 20 20"
                        width="20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M9.25008 18.4577H5.75008C4.18336 18.4577 3.16354 18.1078 2.52759 17.4718C1.89165 16.8359 1.54175 15.8161 1.54175 14.2494V10.7493C1.54175 9.18263 1.89165 8.1628 2.52759 7.52686C3.16354 6.89092 4.18336 6.54102 5.75008 6.54102H9.25008C10.8168 6.54102 11.8366 6.89092 12.4726 7.52686C13.1085 8.1628 13.4584 9.18263 13.4584 10.7493V14.2494C13.4584 15.8161 13.1085 16.8359 12.4726 17.4718C11.8366 18.1078 10.8168 18.4577 9.25008 18.4577ZM5.75008 6.79102C4.42887 6.79102 3.38786 7.00863 2.69861 7.69788C2.00936 8.38713 1.79175 9.42814 1.79175 10.7493V14.2494C1.79175 15.5706 2.00936 16.6116 2.69861 17.3008C3.38786 17.9901 4.42887 18.2077 5.75008 18.2077H9.25008C10.5713 18.2077 11.6123 17.9901 12.3016 17.3008C12.9908 16.6116 13.2084 15.5706 13.2084 14.2494V10.7493C13.2084 9.42814 12.9908 8.38713 12.3016 7.69788C11.6123 7.00863 10.5713 6.79102 9.25008 6.79102H5.75008Z"
                          fill="#032282"
                          stroke="#032282"
                        />
                        <path
                          d="M13.4584 12.7077V13.2077H13.9584H14.2501C15.5713 13.2077 16.6123 12.9901 17.3016 12.3008C17.9908 11.6116 18.2084 10.5706 18.2084 9.24935V5.74935C18.2084 4.42814 17.9908 3.38713 17.3016 2.69788C16.6123 2.00863 15.5713 1.79102 14.2501 1.79102H10.7501C9.42887 1.79102 8.38786 2.00863 7.69861 2.69788C7.00936 3.38713 6.79175 4.42814 6.79175 5.74935V6.04102V6.54102H7.29175H9.25008C10.8168 6.54102 11.8366 6.89092 12.4726 7.52686C13.1085 8.1628 13.4584 9.18263 13.4584 10.7493V12.7077ZM14.2501 13.4577H13.3334C13.3033 13.4577 13.272 13.4454 13.2463 13.4198C13.2207 13.3941 13.2084 13.3628 13.2084 13.3327V10.7493C13.2084 9.42813 12.9908 8.38713 12.3016 7.69788C11.6123 7.00863 10.5713 6.79102 9.25008 6.79102H6.66675C6.6366 6.79102 6.60532 6.77873 6.57968 6.75309C6.55403 6.72744 6.54175 6.69616 6.54175 6.66602V5.74935C6.54175 4.18263 6.89165 3.1628 7.52759 2.52686C8.16354 1.89092 9.18336 1.54102 10.7501 1.54102H14.2501C15.8168 1.54102 16.8366 1.89092 17.4726 2.52686C18.1085 3.1628 18.4584 4.18263 18.4584 5.74935V9.24935C18.4584 10.8161 18.1085 11.8359 17.4726 12.4718C16.8366 13.1078 15.8168 13.4577 14.2501 13.4577Z"
                          fill="#032282"
                          stroke="#032282"
                        />
                      </svg>
                    </button>
                  </div>

                  <div className="mt-4">
                    <p className="text-xs text-[#556575]">Bank name </p>
                    <p className="text-sm font-semibold text-black">{
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                      data?.results[0]?.spend_mgmt_account_details?.bank_name ?? ""}</p>
                  </div>
                </div>

                <div className="payment__methods">
                  <AddFundsSecondDialog
                    isAddFundSecondDialog={isAddFundSecondDialog}
                    mainBalance={String(walletDetailData?.libertypay_account_balance) || ''}
                    setAddFundSecondState={setAddFundSecondState}
                  />
                  <Link
                    className="mt-4 flex items-center justify-between rounded-[12px] bg-[#F2F5FF] px-4 py-2"
                    href=""
                  >
                    <span className="inline-block text-sm text-[#032282]">USSD <span className='text-xxs'> (coming soon)</span></span>

                    <span className="inline-block">
                      <svg
                        fill="none"
                        height="22"
                        viewBox="0 0 28 22"
                        width="28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clipRule="evenodd"
                          d="M18.2376 8.21555C19.5695 9.56828 19.6139 11.7335 18.3708 13.1404L18.2376 13.2831L14.6041 16.7578C14.2596 17.1076 13.7011 17.1076 13.3566 16.7578C13.0387 16.4349 13.0142 15.9267 13.2833 15.5753L13.3566 15.4909L16.9902 12.0162C17.6429 11.3534 17.6772 10.3003 17.0933 9.59641L16.9902 9.48245L13.3566 6.0078C13.0122 5.65795 13.0122 5.09074 13.3566 4.7409C13.6746 4.41797 14.1749 4.39312 14.521 4.66638L14.6041 4.7409L18.2376 8.21555Z"
                          fill="#032282"
                          fillRule="evenodd"
                        />
                        <g opacity="0.3">
                          <path
                            clipRule="evenodd"
                            d="M12.9456 8.21555C14.2775 9.56828 14.3219 11.7335 13.0788 13.1404L12.9456 13.2831L9.31206 16.7578C8.9676 17.1076 8.40911 17.1076 8.06465 16.7578C7.74669 16.4349 7.72223 15.9267 7.99127 15.5753L8.06465 15.4909L11.6982 12.0162C12.3509 11.3534 12.3852 10.3003 11.8013 9.59641L11.6982 9.48245L8.06465 6.0078C7.72019 5.65795 7.72019 5.09074 8.06465 4.7409C8.38262 4.41797 8.88294 4.39312 9.22896 4.66638L9.31206 4.7409L12.9456 8.21555Z"
                            fill="#032282"
                            fillRule="evenodd"
                          />
                        </g>
                      </svg>
                    </span>
                  </Link>

                  <Link
                    className="mt-4 flex items-center justify-between rounded-[12px] bg-[#F2F5FF] px-4 py-2"
                    href=""
                  >
                    <span className="inline-block text-sm text-[#032282]">Card  <span className='text-xxs'> (coming soon)</span>  </span>

                    <span className="inline-block">
                      <svg
                        fill="none"
                        height="22"
                        viewBox="0 0 28 22"
                        width="28"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clipRule="evenodd"
                          d="M18.2376 8.21555C19.5695 9.56828 19.6139 11.7335 18.3708 13.1404L18.2376 13.2831L14.6041 16.7578C14.2596 17.1076 13.7011 17.1076 13.3566 16.7578C13.0387 16.4349 13.0142 15.9267 13.2833 15.5753L13.3566 15.4909L16.9902 12.0162C17.6429 11.3534 17.6772 10.3003 17.0933 9.59641L16.9902 9.48245L13.3566 6.0078C13.0122 5.65795 13.0122 5.09074 13.3566 4.7409C13.6746 4.41797 14.1749 4.39312 14.521 4.66638L14.6041 4.7409L18.2376 8.21555Z"
                          fill="#032282"
                          fillRule="evenodd"
                        />
                        <g opacity="0.3">
                          <path
                            clipRule="evenodd"
                            d="M12.9456 8.21555C14.2775 9.56828 14.3219 11.7335 13.0788 13.1404L12.9456 13.2831L9.31206 16.7578C8.9676 17.1076 8.40911 17.1076 8.06465 16.7578C7.74669 16.4349 7.72223 15.9267 7.99127 15.5753L8.06465 15.4909L11.6982 12.0162C12.3509 11.3534 12.3852 10.3003 11.8013 9.59641L11.6982 9.48245L8.06465 6.0078C7.72019 5.65795 7.72019 5.09074 8.06465 4.7409C8.38262 4.41797 8.88294 4.39312 9.22896 4.66638L9.31206 4.7409L12.9456 8.21555Z"
                            fill="#032282"
                            fillRule="evenodd"
                          />
                        </g>
                      </svg>
                    </span>
                  </Link>
                </div>
              </>
          }
        </DialogBody>
      </DialogContent>
    </Dialog>
  );
}
