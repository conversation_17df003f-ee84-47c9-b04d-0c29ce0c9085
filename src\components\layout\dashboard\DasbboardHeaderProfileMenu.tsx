'use client';

import { useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
// import { signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useIdle } from 'react-use';

import {
  NoPinError,
  useUserDetails,
} from '@/app/(auth)/(onboarding)/misc/api/getUserDetails';
import { useBooleanStateControl } from '@/hooks';
import { convertKebabAndSnakeToTitleCase, getInitials } from '@/utils/strings';

import {
  Avatar,
  AvatarFallback,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  InActivityModal,
  LoaderModal,
  LogOutModal,
  TokenExpiredModal,
} from '../../core';
import { useUserCredentialsDetails } from '@/stores';
import { refreshTokenStorage, tokenStorage, useRefreshLogin } from '@/app/(auth)/(onboarding)/misc';
// import { formatAxiosErrorMessage } from '@/utils/errors';
import { AES, enc } from 'crypto-js';
import { formatAxiosErrorMessage } from '@/utils/errors';
import toast from 'react-hot-toast';

const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'fallback-key';

if (!process.env.NEXT_PUBLIC_ENCRYPTION_KEY) {
  throw new Error('NEXT_PUBLIC_ENCRYPTION_KEY is required');
}

export function DashboardHeaderProfileMenu() {
  // DEFINE INACTIVITY TIME I.E IDLE
  const isIdle = useIdle(10 * 60 * 1000); // 10 minutes in milliseconds
  const isIdleForFifteenMinutes = useIdle(15 * 60 * 1000); //15 minutes in milliseconds
  const { state: isLoaderModalOpen, setTrue: openLoaderModal } =
    useBooleanStateControl();

  const { email: storedEmail, password: storedPassword } = useUserCredentialsDetails();

  const {
    state: isLogoutModalOpen,
    setState: setLogoutModalState,
    setTrue: openLogoutModal,
  } = useBooleanStateControl();

  const {
    state: isInActivityModalOpen,
    setState: setInActivityModalState,
    setTrue: openInActivityModal,
    setFalse: closeInactivityModal,
  } = useBooleanStateControl();

  const {
    state: isTokenExpiredModalOpen,
    setState: setTokenExpiredModalState,
    setTrue: _openTokenExpiredModal,
    setFalse: _closeTokenExpireModal,
  } = useBooleanStateControl();

  const queryClient = useQueryClient();

  const router = useRouter();
  const {
    data: userDetailsResponse,
    error,
    isSuccess,
    isError,
  } = useUserDetails({
    retryCount: 3,
  });

  const typedError = error as AxiosError<NoPinError>;

  const errorStatus = typedError?.response?.status;

  const missingPinResponse =
    'You do not have any pin yet. Please create a transaction pin with link below';

  const hasNoPin = typedError?.response?.data?.message === missingPinResponse;



  React.useEffect(() => {
    if (!isSuccess && hasNoPin) {
      queryClient.clear();
      openLoaderModal();

      router.push('/transaction-pin');
    }

  }, [
    isError,
    errorStatus,
    hasNoPin,
    isSuccess,
    openLoaderModal,
    queryClient,
    router,
  ]);

  const { first_name, last_name, email } = userDetailsResponse?.user_data ?? {};

  const unformattedFullName = `${first_name ?? ''} ${last_name ?? ''}`;
  const fullName = convertKebabAndSnakeToTitleCase(unformattedFullName);

  const initials = getInitials(fullName);

  // React.useEffect(() => {
  //   // FOR NOW BASICALLY CONDITION FOR MY AUTHENTICATION ERROR CHECK
  //   // IMPLEMENTATION MIGHT CHANGE LATER
  //   if (userDetailsResponse === undefined) {
  //     console.log('userDetailsResponse', userDetailsResponse)
  //   }
  // }, [])

  React.useEffect(() => {
    if (isIdle) {
      // Perform actions when inactivity is detected (e.g., log out the user)
      openInActivityModal();
    }
  }, [isIdle, openInActivityModal]);

  React.useEffect(() => {
    if (isIdleForFifteenMinutes) {
      router.push('/login');
    }
  }, [isIdleForFifteenMinutes]);


  const { mutate: postRefreshLogIn } = useRefreshLogin();
  React.useEffect(() => {
    if (errorStatus === 401) {

      try {
        // Decrypt the stored credentials
        const decryptedEmail = storedEmail ? AES.decrypt(storedEmail, ENCRYPTION_KEY).toString(enc.Utf8) : null;
        const decryptedPassword = storedPassword ? AES.decrypt(storedPassword, ENCRYPTION_KEY).toString(enc.Utf8) : null;

        if (decryptedEmail && decryptedPassword) {
          const updatedData = {
            refresh: refreshTokenStorage.getToken(),
          };

          postRefreshLogIn(updatedData, {
            onSuccess: () => {
              toast.success("Token refreshed successfully")
            },
            onError: error => {
              const _errorMessage = formatAxiosErrorMessage(error as AxiosError);
            },
          });
        }
      } catch (error) {
        console.error('Error decrypting credentials:', error);
      }

    }
  }, [errorStatus, isError, postRefreshLogIn])

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen} />

      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center gap-3 rounded-md transition duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 active:scale-90">
          <Avatar>
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>

          <div className="flex items-center gap-4">
            <div>
              <p className="text-left text-sm font-bold">{fullName}</p>
              <p className="text-left text-xxs leading-[0.8] text-input-placeholder">
                {email}
              </p>
            </div>
            <svg
              fill="none"
              height={7}
              viewBox="0 0 12 7"
              width={12}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M8.357 5.522a3.333 3.333 0 0 1-4.581.126l-.133-.126L.41 2.089A.833.833 0 0 1 1.51.84l.078.07L4.82 4.342c.617.617 1.597.65 2.251.098l.106-.098L10.411.91a.833.833 0 0 1 1.248 1.1l-.07.079-3.232 3.433Z"
                fill="#121826"
                fillRule="evenodd"
              />
            </svg>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="">
          <DropdownMenuItem
            className="cursor-pointer text-red-800 focus:bg-red-100/70"
            onSelect={event => {
              event.preventDefault();
              // signOut({
              //   callbackUrl: `${window.location.origin}/login`,
              // });
              openLogoutModal();

            }}
          >
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <LogOutModal
        heading=""
        isLogOutModalOpen={isLogoutModalOpen}
        setLogOutModalState={setLogoutModalState}
        subheading={''}
      >
        <div className="mt-[30px] flex gap-[20px]">
          <Button
            className="mt-1 px-[40px]"
            size="fullWidth"
            variant="red"
            onClick={async (event) => {
              event.preventDefault();
              try {
                // Clear the token
                tokenStorage.clearToken();
                localStorage.removeItem(`${'MANAGEMENT_PAYBOX'}TOKEN`);

                // Optional sign out logic
                // await signOut({
                //   callbackUrl: `${window.location.origin}/login`,
                // });

                // Check if the token is cleared
                if (!tokenStorage.getToken()) {

                  // Delay before reloading
                  setTimeout(() => {
                    // location.reload();
                    location.reload();
                  }, 1500); // Adjust delay as necessary
                } else {
                  console.error("Token still exists in local storage.");
                }
              } catch (error) {
                console.error("Error during logout:", error);
              }
            }}
          >
            Log out
          </Button>
        </div>
      </LogOutModal>

      <InActivityModal
        heading=""
        isInActivityModalOpen={isInActivityModalOpen}
        setInActivityModalState={setInActivityModalState}
        subheading={''}
      >
        <div className="mt-[30px] flex gap-[20px]">
          <Button
            className="mt-1 whitespace-nowrap px-[40px]"
            size="fullWidth"
            variant="outlined"
            onClick={async (event) => {
              event.preventDefault();
              try {
                // Clear the token
                tokenStorage.clearToken();
                localStorage.removeItem(`${'MANAGEMENT_PAYBOX'}TOKEN`);

                // Optional sign out logic
                // await signOut({
                //   callbackUrl: `${window.location.origin}/login`,
                // });

                // Check if the token is cleared
                if (!tokenStorage.getToken()) {

                  // Delay before reloading
                  setTimeout(() => {
                    // location.reload();
                    location.reload();
                  }, 1500); // Adjust delay as necessary
                } else {
                  console.error("Token still exists in local storage.");
                }
              } catch (error) {
                console.error("Error during logout:", error);
              }
            }}
          >
            Done for now
          </Button>

          <Button
            className="mt-1 px-[40px]"
            size="fullWidth"
            variant="red"
            onClick={() => {
              closeInactivityModal();
            }}
          >
            No not done
          </Button>
        </div>
      </InActivityModal>

      <TokenExpiredModal
        heading="Session expired"
        isTokenExpiredModalOpen={isTokenExpiredModalOpen}
        setTokenExpiredModalState={setTokenExpiredModalState}
        subheading="Your session has expired. Please log in again."
      >

      </TokenExpiredModal>
    </>
  );
}
