import { managementAxios } from "@/lib/axios";;
import { useQuery } from "@tanstack/react-query";

export type JournalEntry = {
    id: string;
    date: string;
    journal_number: string;
    references: string;
    notes: string;
    journal_type: "CASH";
    transaction_type: "debit" | "credit";
    is_draft: boolean;
    lines: JournalLine[];
};

export type JournalLineEntry = {
    lines: JournalLine[];

}

type JournalLine = {
    description: string;
    debit_amount: number;
    credit_amount: number;
    bal_before: number;
    bal_after: number;
    customer: string;
    tax: number;
    account: string;
};

const getAllJornalEntry = async (company: string) => {
    const response = await managementAxios.get(`api/v1/accounting/journals/?company=${company}`)
    return response.data as JournalEntry[]
}

export const UseGetJournalEntry = (company: string) => {
    return useQuery({
        queryKey: ['accounts', company],
        enabled: !!company,
        queryFn: () => getAllJornalEntry(company),
    })
}
