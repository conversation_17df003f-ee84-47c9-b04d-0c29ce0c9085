'use client';

import React, { useEffect } from 'react';

import { StockTransferTable, ViewBranchesDialog } from '../misc/components';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCompaniesList } from '../../instant-web/misc/api';
import { LoaderModal } from '@/components/core';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';

const Page = () => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const company = searchParams.get('company') || '';
  const branch = searchParams.get('branch') || '';

  // if (!branch) redirect(`/stock/stock-transfer/select-branch?company=${company}`)

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  useEffect(() => {
    if (!company && salesUserDetails?.data) {
      router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])

  return (
    <>
      <LoaderModal isOpen={isCompanyLoading || isDefaultLoading || !company} />
      {branch ?
        <div className="md:px-7 md:py-3 lg:px-11">
          <div className="my-2 flex flex-wrap items-center">
          </div>
          <StockTransferTable />
        </div>
        :
        <ViewBranchesDialog company={company}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          companyList={companies?.results} link={'/stock/stock-transfer'} />
      }
    </>
  );
};

export default Page;
