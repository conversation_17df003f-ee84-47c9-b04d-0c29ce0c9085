'use client'

import React from 'react';



import { ViewCategoriesModal } from '../../misc/components/modals';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types/companies';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

export default function CreateBranch() {
  const _CALLBACK_URL = '/spend-managment/stock';


  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies } = companiesResponse || {};

  return (
    <>
      <ViewCategoriesModal
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies as CompaniesResultsEntity[]} />
    </>
  );
}
