import React from 'react';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/core';
import { MoreHorizontal, Eye, ChevronLeft, ChevronRight } from 'lucide-react';
import { LeaveResultEntity, LeaveResultsData } from '../../api/getNewLeaveRequest';
import MainLeaveRequestDialog from '../modals/MainLeaveRequestDialog';

type NewLeaveRequestDetailedProps = {
    data: LeaveResultsData;
    overview?: boolean;
    pageSize: number;
    company_id: string;
    company_name: string;
};

const NewLeaveRequestDetailedTable: React.FC<NewLeaveRequestDetailedProps> = ({
    data,
    pageSize,
    company_id,
    company_name
}) => {
    const [currentPage, setCurrentPage] = React.useState(1);
    const [currentDetail, setCurrentDetail] = React.useState<LeaveResultEntity>();
    const [isLeaveRequestDialogOpen, setLeaveRequestDialogState] = React.useState(false);

    const totalPages = Math.ceil(data?.results?.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, data?.results?.length);
    const pageItems = data?.results?.slice(startIndex, endIndex);

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'approved':
                return 'text-status-approved';
            case 'pending':
                return 'text-status-pending';
            case 'ongoing':
                return 'text-status-ongoing';
            default:
                return 'text-status-rejected';
        }
    };

    return (
        <div className="flex flex-col h-full">
            <div className="relative overflow-x-auto">
                <table className="w-full text-sm text-left">
                    <thead className="text-xs text-neutral-500 uppercase bg-neutral-50/50">
                        <tr>
                            <th className="px-6 py-3 font-medium">ID</th>
                            <th className="px-6 py-3 font-medium">Employee Name</th>
                            <th className="px-6 py-3 font-medium">Leave Type</th>
                            <th className="px-6 py-3 font-medium">Days</th>
                            <th className="px-6 py-3 font-medium">Reason</th>
                            <th className="px-6 py-3 font-medium">From</th>
                            <th className="px-6 py-3 font-medium">To</th>
                            <th className="px-6 py-3 font-medium">Status</th>
                            <th className="px-6 py-3 font-medium text-right">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {pageItems?.length === 0 ? (
                            <tr>
                                <td className="px-6 py-8 text-center text-neutral-500" colSpan={8}>
                                    <div className="flex flex-col items-center justify-center gap-4">
                                        <svg fill="none" height="121" viewBox="0 0 123 121" width="123" xmlns="http://www.w3.org/2000/svg">
                                            <rect fill="white" height="98" rx="13.5" stroke="#E2E6EE" transform="rotate(-10.9436 0.585829 24.0288)" width="91" x="0.585829" y="24.0288" />
                                            <rect fill="#E2E6EE" height="10" rx="5" transform="rotate(-10.9436 16.1482 43.9375)" width="65" x="16.1482" y="43.9375" />
                                            <rect fill="#E2E6EE" height="10" rx="5" transform="rotate(-10.9436 20.3247 65.5352)" width="37" x="20.3247" y="65.5352" />
                                            <rect fill="#E2E6EE" height="10" rx="5" transform="rotate(-10.9436 24.5012 87.1367)" width="37" x="24.5012" y="87.1367" />
                                            <rect fill="#E2E6EE" height="10" rx="5" transform="rotate(-10.9436 61.5609 57.5625)" width="23" x="61.5609" y="57.5625" />
                                            <rect fill="#E2E6EE" height="10" rx="5" transform="rotate(-10.9436 65.7374 79.1641)" width="23" x="65.7374" y="79.1641" />
                                            <rect fill="white" height="98" rx="13.5" stroke="#E2E6EE" width="91" x="31.0607" y="0.5" />
                                            <rect fill="#E2E6EE" height="10" rx="5" width="65" x="42.5607" y="23" />
                                            <rect fill="#E2E6EE" height="10" rx="5" width="37" x="42.5607" y="45" />
                                            <rect fill="#E2E6EE" height="10" rx="5" width="37" x="42.5607" y="67" />
                                            <rect fill="#E2E6EE" height="10" rx="5" width="23" x="84.5607" y="45" />
                                            <rect fill="#E2E6EE" height="10" rx="5" width="23" x="84.5607" y="67" />
                                        </svg>
                                        <span>No leave requests found</span>
                                    </div>
                                </td>
                            </tr>
                        ) : (
                            pageItems?.map((item, index) => (
                                <tr
                                    className="border-b border-neutral-200/50 last:border-0 hover:bg-neutral-50/50 transition-colors"
                                    key={index}
                                >
                                    <td className="px-6 py-4 font-medium text-neutral-900">
                                        {startIndex + index + 1}
                                    </td>
                                    <td className="px-6 py-4 text-neutral-700">
                                        {item.employee}
                                    </td>
                                    <td className="px-6 py-4 text-neutral-700">
                                        {item.leave_type}
                                    </td>
                                    <td className="px-6 py-4 text-neutral-700">
                                        {item.leave_days}
                                    </td>
                                    <td className="px-6 py-4 text-neutral-700">
                                        {item.reason.length > 50
                                            ? `${item.reason.substring(0, 50)}...`
                                            : item.reason}
                                    </td>
                                    <td className="px-6 py-4 text-neutral-700">
                                        {item.start_date}
                                    </td>
                                    <td className="px-6 py-4 text-neutral-700">
                                        {item.end_date}
                                    </td>
                                    <td className="px-6 py-4">
                                        <span className={`font-medium ${getStatusColor(item.status)}`}>
                                            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 text-right">
                                        <Popover>
                                            <PopoverTrigger className="p-2 rounded-md hover:bg-neutral-100 transition-colors">
                                                <MoreHorizontal className="w-4 h-4 text-neutral-500" />
                                            </PopoverTrigger>
                                            <PopoverContent className="w-48 p-1.5 bg-white rounded-lg shadow-lg border border-neutral-200/50">
                                                <button
                                                    className="w-full flex items-center gap-2 px-3 py-2 text-sm text-neutral-700 rounded-md hover:bg-neutral-100 transition-colors"
                                                    onClick={() => {
                                                        setCurrentDetail(item);
                                                        setLeaveRequestDialogState(true);
                                                    }}
                                                >
                                                    <Eye className="w-4 h-4" />
                                                    View details
                                                </button>
                                            </PopoverContent>
                                        </Popover>
                                    </td>
                                </tr>
                            ))
                        )}


                        {

                        }
                    </tbody>
                </table>
            </div>

            <div className="flex items-center justify-end px-6 py-4 border-t border-neutral-200/50">
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                        <button
                            className="p-1 rounded-md hover:bg-neutral-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={currentPage === 1}
                            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                        >
                            <ChevronLeft className="w-4 h-4" />
                        </button>
                        <span className="text-sm text-neutral-600">
                            Page {currentPage} of {totalPages || 1}
                        </span>
                        <button
                            className="p-1 rounded-md hover:bg-neutral-100 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={currentPage === totalPages}
                            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                        >
                            <ChevronRight className="w-4 h-4" />
                        </button>
                    </div>
                </div>
            </div>

            <MainLeaveRequestDialog
                company_id={company_id}
                company_name={company_name}
                data={currentDetail as LeaveResultEntity}
                isMainLeaveRequestDialogOpen={isLeaveRequestDialogOpen}
                setMainLeaveRequestDialogState={setLeaveRequestDialogState}
            />
        </div>
    );
};

export default NewLeaveRequestDetailedTable;