'use client';
import React from 'react';

import {
    ClientOnly,
    Dialog,
    DialogBody,
    DialogClose,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/core';

import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';
import { LeaveResultEntity } from '../../api/getNewLeaveRequest';


interface LeaveDetailsDialogProps {
    isLeaveDetailsDialogOpen: boolean;
    setLeaveDetailsDialogState: React.Dispatch<React.SetStateAction<boolean>>;
    data: LeaveResultEntity;
    nextStep?: React.Dispatch<React.SetStateAction<boolean>>;
}





const LeaveDetailsDialog = ({
    isLeaveDetailsDialogOpen,
    setLeaveDetailsDialogState,
    data
}: LeaveDetailsDialogProps) => {
    const { leave_days, start_date, end_date, leave_type, employee, reason, status } = data || {}

    return (
        <ClientOnly>
            <Dialog
                open={isLeaveDetailsDialogOpen}
                onOpenChange={setLeaveDetailsDialogState}
            >
                <DialogContent className="bg-[#FFFFFF]">

                    <DialogHeader>
                        <DialogTitle>Leave Details</DialogTitle>
                        <DialogClose>Close</DialogClose>
                    </DialogHeader>

                    <DialogBody className='flex flex-col w-full'>
                        {/* <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                                <span className='text-[#556575] text-[0.875rem]'>Date created:</span>
                                <span className='font-semibold text-[0.9rem]'>{date_created}</span>
                            </p> */}
                        <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                            <span className='text-[#556575] text-[0.875rem]'>Employee name:</span>
                            <span className='font-semibold text-[0.9rem]'>{employee}</span>
                        </p>
                        <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                            <span className='text-[#556575] text-[0.875rem]'>Leave type:</span>
                            <span className='font-semibold text-[0.9rem]'>{leave_type}</span>
                        </p>
                        <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                            <span className='text-[#556575] text-[0.875rem]'>Leave Duration:</span>
                            <span className='font-semibold text-[0.9rem]'>{leave_days}</span>
                        </p>
                        {/* <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                                <span className='text-[#556575] text-[0.875rem]'>status:</span>
                                <span className='font-semibold text-[0.9rem]'>{status}</span>
                            </p> */}
                        <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                            <span className='text-[#556575] text-[0.875rem]'>Leave start date:</span>
                            <span className='font-semibold text-[0.9rem]'>{start_date}</span>
                        </p>
                        <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                            <span className='text-[#556575] text-[0.875rem]'>Leave end date:</span>
                            <span className='font-semibold text-[0.9rem]'>{end_date}</span>
                        </p>
                        <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                            <span className='text-[#556575] text-[0.875rem]'>Status:</span>
                            <span className={`font-semibold text-[0.9rem] ${status == 'APPROVED' ? 'text-[#18A201]' : status == 'PENDING' ? 'text-[#FB9700]' : status == 'ONGOING' ? 'text-[#032180]' : 'text-[#fb3b00]'}`}>{convertKebabAndSnakeToTitleCase(status)}</span>
                        </p>
                        {/* <p className='grid grid-cols-2 py-4 px-3 border-b-[0.3px] border-[#D6D6D6]'>
                                <span className='text-[#556575] text-[0.875rem]'>Attached doc:</span>
                                <span className='font-semibold text-[0.9rem]'>{attached_doc}</span>
                            </p> */}
                        <p className='grid grid-cols-2 py-4 px-3'>
                            <span className='text-[#556575] text-[0.875rem]'>Reasons:</span>
                            <span className='font-semibold text-[0.9rem]'>{reason}</span>
                        </p>

                    </DialogBody>
                </DialogContent>
            </Dialog>
        </ClientOnly>
    );
}

export default LeaveDetailsDialog