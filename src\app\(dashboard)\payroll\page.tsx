'use client'
import React from 'react'
import { <PERSON><PERSON>, LinkButton } from '@/components/core';
import { SampleRequisitionBanners } from './misc/components';
import { VideoModal } from '@/app/(marketing)/landingmisc/components';

export default function Payroll() {
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  return (
    <div className="md:px-7 md:py-3 lg:px-11">
      <div className="relative z-10 overflow-hidden bg-[#EBEFFB] p-6 md:rounded-10 md:bg-requisition-bg-gradient lg:px-11 lg:py-8">

        <h2 className="mb-1 text-lg font-semibold md:mb-[.3125rem] md:text-xl lg:text-2xl">
          Payroll Process
        </h2>

        <p className="mb-2.5 max-w-[83%] text-justify text-sm text-light-text sm:max-w-[42.375rem] lg:text-base">
          The Libertypay payroll system gives you the ability to take charge and
          seize control of your payroll with our innovative solution and
          cutting-edge system which empowers you to streamline your payroll
          process like never before.
        </p>

        <div className="z-10 mb-7 flex gap-4">
          <LinkButton className="px-[1.375rem] md:text-sm" href={'/book-demo'} size="lg">
            Book a demo
          </LinkButton>
          <Button
            className="gap-1.5 rounded-full bg-[#E9EEFF] md:text-sm"
            size="lg"
            variant="light"
            onClick={() => { setIsModalOpen(true); }}
          >
            <svg
              fill="none"
              height={20}
              viewBox="0 0 20 20"
              width={20}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7.583 10V8.767c0-1.592 1.126-2.233 2.5-1.442l1.067.617 1.067.617c1.375.791 1.375 2.091 0 2.883l-1.067.617-1.066.616c-1.375.792-2.5.142-2.5-1.441V10Z"
                stroke="#032282"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeMiterlimit={10}
                strokeWidth={1.5}
              />
              <path
                d="M10 18.334a8.333 8.333 0 1 0 0-16.667 8.333 8.333 0 0 0 0 16.667Z"
                stroke="#032282"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
              />
            </svg>

            <span>Watch video</span>
          </Button>
        </div>

        <div className="flex max-w-[42.375rem] flex-col gap-6 sm:grid sm:grid-cols-2 md:grid-cols-1 lg:grid lg:grid-cols-2">
          <article className="rounded-2xl bg-main-solid p-4 sm:p-6 md:max-w-[20.4375rem] md:bg-white">
            <h3 className="mb-1 text-sm font-semibold text-white md:text-base md:text-main-solid lg:text-lg">
              Create company
            </h3>

            <p className="mb-2 max-w-[14.625rem] text-xs text-white/80 md:text-sm md:text-light-text">
              Create a company list and add employees to your company.
            </p>

            <div className="flex gap-4">
              <LinkButton
                className="bg-white px-[.875rem] text-main-solid sm:py-2.5 md:bg-main-solid md:text-white"
                href="/payroll/create-company"
              >
                Create Company
              </LinkButton>

              <LinkButton
                className="bg-white/40 px-[.875rem] text-white sm:py-2.5 md:bg-main-bg md:text-main-solid"
                href="/payroll/all-companies"
                variant="light"
              >
                View companies
              </LinkButton>
            </div>
          </article>

          <article className="rounded-2xl bg-white p-4 sm:p-6 md:max-w-[20.4375rem]">
            <h3 className="mb-1 text-sm font-semibold md:text-base lg:text-lg">
              Payroll settings
            </h3>

            <p className="mb-2 max-w-[14.625rem] text-xs text-light-text md:text-sm">
              Run payroll, make approved bulk payment and stay complaint.
              {/* Run payroll, make bulk payment and stay complaint. 
               Have all your business expenses within a set budget and have them
              tracked with ease. */}
            </p>

            <div className="flex gap-4">
              <LinkButton
                className="px-[.875rem] sm:py-2.5"
                href="/payroll/settings"
              >
                Explore payroll settings
              </LinkButton>

              {/* <LinkButton
                className="px-[.875rem] sm:py-2.5"
                href="/spend-management/budgeting/all-budgets"
                variant=""
              >
                View budgets
              </LinkButton> */}
            </div>
          </article>

          <article className="rounded-2xl bg-white p-4 sm:p-6 md:max-w-[20.4375rem]">
            <h3 className="mb-1 text-sm font-semibold md:text-base lg:text-lg">
              Employees
            </h3>

            <p className="mb-2 max-w-[14.625rem] text-xs text-light-text md:text-sm">
              Add employees to company created, in order to{' '}
              <strong>run a payroll</strong> company needs to have employees.
            </p>

            <div className="flex gap-4">
              <LinkButton
                className="px-[.875rem] sm:py-2.5"
                href="/payroll/payroll-redirect-option/create-employees"
              >
                Add employees
              </LinkButton>

              <LinkButton
                className="px-[.875rem] sm:py-2.5"
                href="/payroll/payroll-redirect-option/view-employees"
                variant="light"
              >
                View employees
              </LinkButton>
            </div>
          </article>

          <article className="rounded-2xl bg-white p-4 sm:p-6 md:max-w-[20.4375rem]">
            <h3 className="mb-1 text-sm font-semibold md:text-base lg:text-lg">
              Time & Attendance
            </h3>

            <p className="mb-2 max-w-[14.625rem] text-xs text-light-text md:text-sm">
              Automate the process of tracking workflow and keep your team
              accountable.
            </p>

            <div className="flex gap-4">
              {/* <LinkButton
                className="px-[.875rem] sm:py-2.5"
                href="/spend-management/make-requisitions"
              >
                Make requisition
              </LinkButton> */}
              <LinkButton
                className="px-[.875rem] sm:py-2.5"
                href="/payroll/payroll-redirect-option/time-attendance"
                variant="light"
              >
                View time & attendance
              </LinkButton>
            </div>
          </article>

          <article className="rounded-2xl bg-main-solid p-4 sm:p-6 md:max-w-[20.4375rem] md:bg-white">
            <h3 className="mb-1 text-sm font-semibold text-white md:text-base md:text-main-solid lg:text-lg">
              Leave management
            </h3>

            <p className="mb-2 max-w-[14.625rem] text-xs text-white/80 md:text-sm md:text-light-text">
              Create a leave policy and manage leave requests.
            </p>

            <div className="flex gap-4">
              <LinkButton
                className="bg-white/40 px-[.875rem] text-white sm:py-2.5 md:bg-main-bg md:text-main-solid"
                href="/leave-management/companies"
                variant="light"
              >
                View leave management
              </LinkButton>
            </div>
          </article>

          <article className="rounded-2xl bg-main-solid p-4 sm:p-6 md:max-w-[20.4375rem] md:bg-white">
            <h3 className="mb-1 text-sm font-semibold text-white md:text-base md:text-main-solid lg:text-lg">
              Performance management
            </h3>

            <p className="mb-2 max-w-[14.625rem] text-xs text-white/80 md:text-sm md:text-light-text">
              Track employee goals and company goals, conduct reviews.
            </p>

            <div className="flex gap-4">
              <LinkButton
                className="bg-white/40 px-[.875rem] text-white sm:py-2.5 md:bg-main-bg md:text-main-solid"
                href="/performance-management/company"
                variant="light"
              >
                View performance management
              </LinkButton>
            </div>
          </article>

        </div>

        <SampleRequisitionBanners />

      </div>

      <VideoModal isOpen={isModalOpen} videoId={'v_MdwzT45cw'} onClose={() => setIsModalOpen(false)} />
    </div>
  );
}
