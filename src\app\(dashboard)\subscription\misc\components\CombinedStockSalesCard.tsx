'use client';
import React from 'react';
import { Button, Checkbox, Input } from '@/components/core';
import PayboxModuleIcon from '@/components/icons/PayboxModuleIcon';
import { SubScriptionModulePropTypes, Plan } from '../api/getSubscriptionPlan';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';
import { Package, ShoppingCart, Star } from 'lucide-react';

interface CombinedStockSalesCardProps {
  stockModule: SubScriptionModulePropTypes;
  salesModule: SubScriptionModulePropTypes;
  isStockSelected: boolean;
  isSalesSelected: boolean;
  onModuleSelection: (module: SubScriptionModulePropTypes) => void;
  onSubscribe: (module: SubScriptionModulePropTypes) => void;
  salesTokenUnits: number;
  onSalesTokenUnitsChange: (moduleId: number, units: number) => void;
  disabled?: boolean;
}

const CombinedStockSalesCard: React.FC<CombinedStockSalesCardProps> = ({
  stockModule,
  salesModule,
  isStockSelected,
  isSalesSelected,
  onModuleSelection,
  onSubscribe,
  salesTokenUnits,
  onSalesTokenUnitsChange,
  disabled = false
}) => {
  const handleSalesTokenUnitsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    if (value >= 0) {
      onSalesTokenUnitsChange(salesModule.id, value);
    }
  };

  const salesTotalAmount = salesTokenUnits * 10; // 1 token = ₦10

  return (
    <div className="relative bg-white border-[0.2px] border-[#0323821a] rounded-xl p-6 mt-8">
      {/* Priority Badge */}
      <div className="absolute -top-3 left-6">
        <div className="bg-[#032282] text-white px-4 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
          <Star className="h-3 w-3 fill-current" />
          Priority Modules
        </div>
      </div>

      {/* Header */}
      <div className="mt-4 mb-6">
        <h3 className="text-xl font-bold text-[#032282] mb-2">Stock & Sales Management</h3>
        <p className="text-sm text-gray-600">
          Complete business management solution for inventory and sales operations
        </p>
      </div>

      {/* Modules Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Stock Module - No Checkbox (Always Available) */}
        <div className="border-[0.2px] border-[#03238264] bg-white rounded-lg p-4">
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <Package className="h-5 w-5 text-[#032282]" />
              <h4 className="font-semibold text-[#032282] text-lg">{stockModule.name}</h4>
            </div>
            <p className="text-sm text-gray-600 mb-4">{stockModule.description}</p>

            {/* Stock Features */}
            <div className="grid grid-cols-2 gap-1 mb-4">
              <div className="text-xs text-gray-500">✓ Inventory Management</div>
              <div className="text-xs text-gray-500">✓ Stock Tracking</div>
              <div className="text-xs text-gray-500">✓ Supplier Management</div>
              <div className="text-xs text-gray-500">✓ Stock Reports</div>
            </div>
          </div>

          {/* Stock Subscription Section - Always Available */}
          <div className="p-4 bg-white rounded-lg border border-[#032282] border-opacity-20">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-blue-700">Subscription Plan:</span>
                {/* <span className="text-lg font-bold text-blue-800">Annual Plan</span> */}
              </div>
              <p className="text-xs text-blue-600">
                Full access to all stock management features
              </p>
            </div>

            {/* <Button
              onClick={() => onSubscribe(stockModule)}
              disabled={disabled}
              className="w-full bg-[#032282] hover:bg-[#032282]/90 text-white py-3 rounded-lg font-semibold text-lg transition-all duration-200 shadow-md hover:shadow-lg"
            >
              Subscribe to Stock Management
            </Button> */}
          </div>
        </div>

        {/* Sales Module - Enhanced */}
        <div className={`border-[0.2px] rounded-lg p-4 transition-all duration-200 ${isSalesSelected
          ? 'border-[#0323827b] bg-white'
          : 'border-gray-200'
          }`}>

          {/* Header with checkbox and title */}
          <div className="flex items-start gap-3 mb-4">
            <Checkbox
              checked={isSalesSelected}
              onCheckedChange={() => onModuleSelection(salesModule)}
              disabled={disabled}
              className="w-4 h-4 border-2 rounded-md mt-1 border-[#032282]"
              id={salesModule.id.toString()}
            />
            <div className="flex-1">
              <label
                className="font-bold text-lg cursor-pointer block"
                htmlFor={salesModule.id.toString()}
              >
                <div className="flex items-center gap-2 mb-2">
                  <ShoppingCart className="h-5 w-5 text-[#032282]" />
                  <p className="text-[#032282] text-lg font-semibold leading-tight">{salesModule.name}</p>
                  <span className="bg-amber-100 text-amber-800 text-xs px-2 py-1 rounded-full font-medium">
                    Token-based
                  </span>
                </div>
              </label>
              <p className="text-[#032282] text-sm font-medium mt-1">
                🚀 Recommended for business growth
              </p>
              <p className="text-sm text-gray-600 mb-3">{salesModule.description}</p>

              {/* Sales Features */}
              <div className="grid grid-cols-2 gap-1 mb-4">
                <div className="flex items-center gap-3">

                  <span className="text-xs text-gray-500">✓ Sales Management</span>
                </div>
                <div className="flex items-center gap-3">

                  <span className="text-xs text-gray-500">✓ Customer Management</span>
                </div>
                <div className="flex items-center gap-3">

                  <span className="text-xs text-gray-500">✓ Invoice Generation</span>
                </div>
                <div className="flex items-center gap-3">

                  <span className="text-xs text-gray-500">✓ Sales Analytics</span>
                </div>
              </div>
            </div>
          </div>

          {/* Token Input section - Always visible when selected */}
          <div className={`p-4 rounded-lg mb-4 border ${isSalesSelected
            ? 'bg-[#fdfdfd] bg-opacity-5 border-[#03238271] border-opacity-20'
            : 'bg-gray-50 border-gray-200'
            }`}>
            <div className="space-y-3">
              <div>
                <label className="block text-[13px] font-medium text-gray-700 mb-2">
                  Number of Token Units
                </label>
                <Input
                  className="w-full text-center text-sm font-medium border border-gray-300 rounded-lg focus:border-[#032282] focus:ring-1 focus:ring-[#032282]"
                  disabled={disabled || !isSalesSelected}
                  min="0"
                  placeholder={isSalesSelected ? "Enter token units" : "Select module first"}
                  type="number"
                  value={salesTokenUnits || ''}
                  onChange={handleSalesTokenUnitsChange}
                />
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600 mb-1">Total Amount for this module</p>
                <p className="text-xl font-bold text-[#032282]">
                  {convertNumberToNaira(salesTotalAmount)}
                </p>
                <p className="text-xs text-gray-500">
                  {addCommasToNumber(salesTokenUnits)} tokens × ₦10 each
                </p>
              </div>
            </div>
          </div>

          {/* Subscribe Button */}
          <Button
            className="w-full py-3 font-medium transition-all duration-200 bg-[#032282] hover:bg-[#032282]/90 text-white disabled:bg-gray-400"
            disabled={disabled || !isSalesSelected || salesTokenUnits <= 0}
            onClick={() => onSubscribe(salesModule)}
          >
            {!isSalesSelected
              ? 'Select Module First'
              : salesTokenUnits <= 0
                ? 'Enter Token Units'
                : 'Subscribe Now'
            }
          </Button>

          {/* Additional info */}
          <div className="mt-3 text-center">
            <p className="text-xs text-[#032282] font-medium">
              🚀 Recommended for business growth
            </p>
          </div>
        </div>
      </div>


      {/* Promotional Message - Shows when Sales is selected */}
      {isSalesSelected && (
        <div className="mt-4 bg-gradient-to-r from-green-50 to-white border border-green-300 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <div className="bg-green-500 rounded-full p-2 flex-shrink-0">
              <Star className="h-5 w-5 text-white fill-current" />
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-bold text-green-800 mb-2">
                🎉 Special Offer Unlocked!
              </h4>
              <p className="text-green-700 font-semibold mb-3">
                Get 30 Days FREE Access to HR & Spend Management Modules
              </p>
              <div className="bg-white bg-opacity-70 rounded-lg p-3 mb-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">✓</span>
                    <span className="text-sm font-medium text-gray-700">HR Management</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">✓</span>
                    <span className="text-sm font-medium text-gray-700">Spend Management</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">✓</span>
                    <span className="text-sm font-medium text-gray-700">Employee Management</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-green-600">✓</span>
                    <span className="text-sm font-medium text-gray-700">Expense Tracking</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className="bg-green-600 text-white text-xs px-3 py-1 rounded-full font-bold">
                  LIMITED TIME
                </span>
                <span className="text-sm text-green-700 font-medium">
                  Automatically included with your Sales subscription
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CombinedStockSalesCard;
