'use client';
import React from 'react';
import { Button, Checkbox, Input } from '@/components/core';
import PayboxModuleIcon from '@/components/icons/PayboxModuleIcon';
import { SubScriptionModulePropTypes, Plan } from '../api/getSubscriptionPlan';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';
import { Package, ShoppingCart, Star } from 'lucide-react';

interface CombinedStockSalesCardProps {
  stockModule: SubScriptionModulePropTypes;
  salesModule: SubScriptionModulePropTypes;
  isStockSelected: boolean;
  isSalesSelected: boolean;
  onModuleSelection: (module: SubScriptionModulePropTypes) => void;
  onSubscribe: (module: SubScriptionModulePropTypes) => void;
  salesTokenUnits: number;
  onSalesTokenUnitsChange: (moduleId: number, units: number) => void;
  disabled?: boolean;
}

const CombinedStockSalesCard: React.FC<CombinedStockSalesCardProps> = ({
  stockModule,
  salesModule,
  isStockSelected,
  isSalesSelected,
  onModuleSelection,
  onSubscribe,
  salesTokenUnits,
  onSalesTokenUnitsChange,
  disabled = false
}) => {
  const handleSalesTokenUnitsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    if (value >= 0) {
      onSalesTokenUnitsChange(salesModule.id, value);
    }
  };

  const salesTotalAmount = salesTokenUnits * 10; // 1 token = ₦10

  return (
    <div className="relative bg-white border-2 border-[#032282] rounded-xl p-6 shadow-lg">
      {/* Priority Badge */}
      <div className="absolute -top-3 left-6">
        <div className="bg-[#032282] text-white px-4 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
          <Star className="h-3 w-3 fill-current" />
          Priority Modules
        </div>
      </div>

      {/* Header */}
      <div className="mt-4 mb-6">
        <h3 className="text-xl font-bold text-[#032282] mb-2">Stock & Sales Management</h3>
        <p className="text-sm text-gray-600">
          Complete business management solution for inventory and sales operations
        </p>
      </div>

      {/* Modules Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Stock Module */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-start gap-3 mb-4">
            <Checkbox
              checked={isStockSelected}
              onChange={() => onModuleSelection(stockModule)}
              disabled={disabled}
              className="mt-1"
            />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Package className="h-5 w-5 text-[#032282]" />
                <h4 className="font-semibold text-[#032282]">{stockModule.name}</h4>
              </div>
              <p className="text-sm text-gray-600 mb-3">{stockModule.description}</p>
              
              {/* Stock Features */}
              <div className="space-y-1">
                <div className="text-xs text-gray-500">✓ Inventory Management</div>
                <div className="text-xs text-gray-500">✓ Stock Tracking</div>
                <div className="text-xs text-gray-500">✓ Supplier Management</div>
                <div className="text-xs text-gray-500">✓ Stock Reports</div>
              </div>
            </div>
          </div>

          {/* Stock Subscription Button */}
          {isStockSelected && (
            <Button
              onClick={() => onSubscribe(stockModule)}
              disabled={disabled}
              className="w-full bg-[#032282] hover:bg-[#032282]/90 text-white py-2 rounded-lg font-medium transition-all duration-200"
            >
              Subscribe to Stock Management
            </Button>
          )}
        </div>

        {/* Sales Module */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-start gap-3 mb-4">
            <Checkbox
              checked={isSalesSelected}
              onChange={() => onModuleSelection(salesModule)}
              disabled={disabled}
              className="mt-1"
            />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <ShoppingCart className="h-5 w-5 text-[#032282]" />
                <h4 className="font-semibold text-[#032282]">{salesModule.name}</h4>
                <span className="bg-amber-100 text-amber-800 text-xs px-2 py-1 rounded-full font-medium">
                  Token-based
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-3">{salesModule.description}</p>
              
              {/* Sales Features */}
              <div className="space-y-1 mb-4">
                <div className="text-xs text-gray-500">✓ Sales Management</div>
                <div className="text-xs text-gray-500">✓ Customer Management</div>
                <div className="text-xs text-gray-500">✓ Invoice Generation</div>
                <div className="text-xs text-gray-500">✓ Sales Analytics</div>
              </div>

              {/* Token Input for Sales */}
              {isSalesSelected && (
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Number of Token Units
                    </label>
                    <Input
                      type="number"
                      value={salesTokenUnits || ''}
                      onChange={handleSalesTokenUnitsChange}
                      disabled={disabled}
                      min="0"
                      className="w-full border border-gray-300 rounded-lg focus:border-[#032282] focus:ring-1 focus:ring-[#032282]"
                      placeholder="Enter token units"
                    />
                  </div>

                  {/* Sales Amount Display */}
                  <div className="bg-[#032282] bg-opacity-5 border border-[#032282] border-opacity-20 rounded-lg p-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-600">Sales Module Cost:</span>
                      <span className="text-lg font-bold text-[#032282]">
                        {convertNumberToNaira(salesTotalAmount)}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {addCommasToNumber(salesTokenUnits)} tokens × ₦10 each
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sales Subscribe Button */}
          {isSalesSelected && salesTokenUnits > 0 && (
            <Button
              onClick={() => onSubscribe(salesModule)}
              disabled={disabled}
              className="w-full bg-[#032282] hover:bg-[#032282]/90 text-white py-2 rounded-lg font-medium transition-all duration-200 mt-3"
            >
              Subscribe to Sales (Token-based)
            </Button>
          )}
        </div>
      </div>

      {/* Combined Benefits */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-semibold text-blue-800 mb-2">Combined Benefits</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div className="text-xs text-blue-700">✓ Seamless inventory-to-sales flow</div>
          <div className="text-xs text-blue-700">✓ Unified business dashboard</div>
          <div className="text-xs text-blue-700">✓ Real-time stock updates</div>
          <div className="text-xs text-blue-700">✓ Comprehensive reporting</div>
        </div>
      </div>

      {/* Promotional Message */}
      {isStockSelected && isSalesSelected && (
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4 text-green-600 fill-current" />
            <span className="text-sm font-semibold text-green-800">
              Perfect Combination! Get 30 days free HR & Spend Management
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CombinedStockSalesCard;
