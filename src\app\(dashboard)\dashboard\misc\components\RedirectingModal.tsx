'use client';
import React, { useEffect, useState } from 'react';
import { X, ExternalLink, ArrowRight } from 'lucide-react';

interface RedirectingModalProps {
  isOpen: boolean;
  onClose: () => void;
  moduleName: string;
  redirectUrl: string;
}

const RedirectingModal: React.FC<RedirectingModalProps> = ({
  isOpen,
  onClose,
  moduleName,
  redirectUrl
}) => {
  const [countdown, setCountdown] = useState(5);
  const [_isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setCountdown(5);
      setIsRedirecting(false);
      return;
    }

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setIsRedirecting(true);
          // Redirect to external URL
          window.open(redirectUrl, '_blank');
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isOpen, redirectUrl, onClose]);

  const handleRedirectNow = () => {
    setIsRedirecting(true);
    window.open(redirectUrl, '_blank');
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleCancel}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-full">
              <ExternalLink className="h-5 w-5 text-blue-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Redirecting to {moduleName}</h2>
          </div>
          <button
            onClick={handleCancel}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center">
            {/* Countdown Circle */}
            <div className="relative mx-auto mb-4 w-20 h-20">
              <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
              <div
                className="absolute inset-0 rounded-full border-4 border-blue-600 transition-all duration-1000 ease-linear"
                style={{
                  borderTopColor: 'transparent',
                  borderRightColor: 'transparent',
                  borderBottomColor: 'transparent',
                  transform: `rotate(${(5 - countdown) * 120}deg)`
                }}
              ></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold text-blue-600">{countdown}</span>
              </div>
            </div>

            <p className="text-gray-700 mb-2">
              You will be redirected to <strong>{moduleName}</strong> in {countdown} seconds
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Opening in a new tab: <br /> <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">{redirectUrl}</span>
            </p>

            {/* Action Buttons */}
            <div className="flex gap-3 justify-center">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleRedirectNow}
                className="px-4 py-2 text-sm font-medium text-white bg-[#032282] hover:bg-[#032282]/80 rounded-lg transition-colors flex items-center gap-2"
              >
                Go Now
                <ArrowRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <ExternalLink className="h-3 w-3" />
            <span>This will open in a new tab</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RedirectingModal;
