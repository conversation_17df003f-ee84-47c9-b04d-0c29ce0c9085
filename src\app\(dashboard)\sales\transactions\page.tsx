'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

// import {
//   LinkButton,
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from '@/components/core';

// import { useGetAssignedBranchesByCompany } from '../misc/api';

import SalesHistoryTable from '../misc/components/tables/BranchSalesHistoryTable';
import { ViewBranchesDialog } from '../../stock/misc/components';
import { LoaderModal } from '@/components/core';
import { useCompaniesList } from '../../instant-web/misc/api';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';
// import SubscriptionErrorModal from '../../spend-management/misc/components/subscription/ErrorSubscriptionModal';

const Page = () => {

    const searchParams = useSearchParams();
    const pathname = usePathname();
    const router = useRouter();

    const branchId = searchParams.get('branch') || '';
    const companyId = searchParams.get('company') || '';

    const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
    const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

    useEffect(() => {
        if (!companyId && salesUserDetails?.data) {
            router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [salesUserDetails?.data.company_id])



    // const searchParams = useSearchParams();

    // const branchId = searchParams.get('branch') as string;

    // const companyId = searchParams.get('company') || searchParams.get('companyId') as string;

    // if (!branchId) redirect(`/sales/select-branch?company=${companyId}`)

    // const { data: branchesData } = useGetAssignedBranchesByCompany(companyId);

    // const branches = branchesData?.data?.branches;

    // const _ = branches?.find(branch => branch.id === branchId);

    return (
        <>
            <LoaderModal isOpen={isDefaultLoading || !companyId} />
            {branchId ?
                <section className="md:px-7 md:py-3 lg:px-11">
                    <section className="px-4 md:px-7 md:py-3 lg:px-11">
                        <SalesHistoryTable />
                    </section>
                </section>
                :
                <>
                    {!isCompanyLoading && <ViewBranchesDialog company={companyId}
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        companyList={companies?.results} link={'/sales/transactions'} />}
                </>
            }
            {/* {
                (!salesUserDetails?.data?.is_subscription && !isDefaultLoading) && <SubscriptionErrorModal
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    message={"Access Forbidden"}
                />
            } */}
        </>
    );
};

export default Page;
