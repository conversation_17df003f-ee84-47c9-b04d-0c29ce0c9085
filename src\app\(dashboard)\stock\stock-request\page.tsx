'use client';

import React from 'react';

import {
  StockRequestDashboardStats,
  StockRequestsTable,
  ViewBranchesDialog,
} from '../misc/components';
import { useSearchParams } from 'next/navigation';
import { LoaderModal } from '@/components/core';
import { useCompaniesList } from '../../instant-web/misc/api';

const Page = () => {
  const searchParams = useSearchParams();
  const company = searchParams.get('company') || '';
  const branch = searchParams.get('branch') || '';

  // if (!branch) return redirect(`/stock/stock-request/select-branch?company=${company}`);

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();

  return (
    <>
      <LoaderModal isOpen={isCompanyLoading} />
      {branch ?
        <div className="md:px-7 md:py-3 lg:px-11">
          <StockRequestDashboardStats />

          <div>
            <StockRequestsTable />
          </div>
        </div>
        :
        <ViewBranchesDialog company={company}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          companyList={companies?.results} link={'/stock/stock-request'} />
      }
    </>
  );
};

export default Page;
