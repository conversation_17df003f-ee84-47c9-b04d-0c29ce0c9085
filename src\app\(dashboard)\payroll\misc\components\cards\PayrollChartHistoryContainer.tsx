'use client';

import React from 'react';

import { PayrollChartData, SelectDayFilter } from '../../types';
import { PayrollHistoryChartCard } from './PayrollHistoryChartCard';

const listOfYears = [
  { id: 1, filter: '', name: 'All time', unavailable: false },
  { id: 2, filter: 'today', name: 'Today', unavailable: false },
  { id: 3, filter: 'this_month', name: 'This month', unavailable: false },
  { id: 4, filter: 'this_week', name: 'This week', unavailable: false },
  { id: 5, filter: 'last_month', name: 'Last month', unavailable: false },
  { id: 6, filter: 'last_week', name: 'Last week', unavailable: false },
  { id: 7, filter: 'last_year', name: 'Last year', unavailable: false },
  { id: 8, filter: 'this_year', name: 'This year', unavailable: false },
];

interface PayrollHistoryChartProps {
  PayrollChartResponse: PayrollChartData;
}

export function PayrollChartHistoryContainer({
  PayrollChartResponse,
}: PayrollHistoryChartProps) {
  const months = PayrollChartResponse?.chart_labels;

  const salary_values = PayrollChartResponse?.salary_chart_data;

  const pension_values = PayrollChartResponse?.pension_chart_data;

  const tax_values = PayrollChartResponse?.tax_chart_data;

  const [tripleBarChartFilter, setTripleBarChartFilter] =
    React.useState<SelectDayFilter>(listOfYears[0]);

  const dashTripleBarChartData = {
    transactionComparative: {
      labels: months,
      values: [
        {
          label: 'Salary',
          data: salary_values,
        },
        {
          label: 'Pension',
          data: pension_values,
        },
        {
          label: 'Taxes',
          data: tax_values,
        },
      ],
    },
  };

  return (
    <>
      <PayrollHistoryChartCard
        change={'up'}
        colors={['#B4C0EB', '#6882D8', '#D9E2FF']}
        data={dashTripleBarChartData?.transactionComparative?.values}
        dataTitle={'Salary'}
        headLabel={'Payroll history'}
        labelsTitle={dashTripleBarChartData?.transactionComparative?.labels}
        secondDataTitle={'Pension'}
        selectDay={tripleBarChartFilter}
        setSelectDay={setTripleBarChartFilter}
        thirdDataTitle={'Taxes'}
        year={listOfYears}
      />
    </>
  );
}
