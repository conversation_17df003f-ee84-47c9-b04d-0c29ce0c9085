"use client"
// import { redirect } from 'next/navigation';
import React from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/core';


// import { Companies, CompaniesResultsEntity } from '../../../spend-management/misc/types';
import { CompaniesResultsEntity } from '../../../spend-management/misc/types';
import { BranchBaseDashboard } from '../../misc/components/BranchBaseDashboard';
import CompanyDashboardStats from '../../misc/components/CompanyDashboardStats';
import { RepresentativeData } from '../../misc/components/RepresentativeData';
import { SalesTable } from '../../misc/components/tables';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';

const ViewBranchPage = ({
  searchParams,
}: {
  searchParams: { company: string };
}) => {
  // const CALLBACK_URL = '/stock';
  const { company } = searchParams;

  // const companiesResponse = await spendManagementFetchClient<Companies>(
  //   'req/get-companies/',
  //   {
  //     headers: { Authorization: `Bearer ${access}` },
  //   }
  // );

  // // console.log({ companiesResponse });

  // // const companyOverviewResponse =
  // //   await spendManagementFetchClient<SingleCompanyOverviewResponse>(
  // //     `api/v1/stock/company?id=${company}`,
  // //     {
  // //       headers: { Authorization: `Bearer ${access}` },
  // //     }
  // //   );
  // // console.log({ companyOverviewResponse });
  // if (
  //   companiesResponse === 'unauthorized'
  //   // ||
  //   // companyOverviewResponse === 'unauthorized'
  // ) {
  //   redirect(`/login?callbackUrl=${CALLBACK_URL}`);
  // }
  const { data } = useCompaniesList()
  return (
    <>
      <CompanyDashboardStats
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={data?.results as CompaniesResultsEntity[]} company={company} />

      <section className="md:px-7 md:py-3 lg:px-11">
        <div className="mt-4">
          <Tabs className="w-full" defaultValue="branches">
            <TabsList className="flex w-full flex-col justify-start gap-[22px] rounded-[.625rem] bg-white pb-0 lg:flex-row">
              <div className="w-full rounded-[13px] bg-white px-4 py-[6px]">
                <TabsTrigger
                  className={`rounded-[13px]
                    bg-white px-[37px]
                    py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:text-[#032282] data-[state=active]:shadow-none`}
                  value="branches"
                >
                  Branches
                </TabsTrigger>

                <TabsTrigger
                  className={`rounded-[13px]
                    bg-white px-[37px]
                    py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:text-[#032282] data-[state=active]:shadow-none`}
                  value="sales__table"
                >
                  Sales Table
                </TabsTrigger>
                <TabsTrigger
                  className={`rounded-[13px]
                    bg-white px-[37px]
                    py-[14px] text-[#032282] data-[state=active]:bg-[#F8FAFF] data-[state=active]:shadow-none`}
                  value="representative__data"
                >
                  Data Representation
                </TabsTrigger>
              </div>
            </TabsList>
            <TabsContent className="mt-4 bg-[#F8FAFF]" value="branches">
              <BranchBaseDashboard />
            </TabsContent>
            <TabsContent className="mt-4 bg-[#F8FAFF]" value="sales__table">
              <SalesTable />
            </TabsContent>
            <TabsContent
              className="mt-4 rounded-sm bg-[#F8FAFF]"
              value="representative__data"
            >
              <RepresentativeData />
            </TabsContent>
          </Tabs>
        </div>
      </section>
    </>
  );
};

export default ViewBranchPage;
