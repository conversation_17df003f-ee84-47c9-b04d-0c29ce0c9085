'use client'

import { redirect } from 'next/navigation';
import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';

import { ViewSuppliersModal } from '../../misc/components/modals';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';

const ViewSuppliers = () => {


  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies, count: companiesCount } = companiesResponse as Companies;

  if (companiesCount < 1) {
    redirect('/spend-management/companies/create-company');
  }

  return (
    <>
      <ViewSuppliersModal
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies}
      // initialUsersResponse={usersResponse}
      />
    </>
  );
};

export default ViewSuppliers;
