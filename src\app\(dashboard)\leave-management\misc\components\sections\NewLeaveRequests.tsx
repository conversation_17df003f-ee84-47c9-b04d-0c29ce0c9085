import React from 'react';

import { FileText } from 'lucide-react';
import { NewLeaveRequestDetailedTable } from '../tables';
import { LeaveResultsData, useNewLeaveRequest } from '../../api/getNewLeaveRequest';

export type NewLeaveRequestsProps = {
    userId: string;
    company_id: string;
    company_name: string;
};

function NewLeaveRequests({ company_id, company_name }: NewLeaveRequestsProps) {
    const fetchOptions = {
        companyid: company_id
    };

    const { data: leaveNewRequestsData, isLoading: isLeaveNewRequestsLoading } = useNewLeaveRequest(fetchOptions);

    return (
        <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 space-y-8">
            <div className="flex flex-col gap-6">
                <div className="flex justify-between items-center">
                    <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-primary-light">
                            <FileText className="w-6 h-6 text-primary" />
                        </div>
                        <h1 className="text-3xl font-semibold text-neutral-800">
                            New Leave Requests
                        </h1>
                    </div>
                    {!isLeaveNewRequestsLoading && (
                        <span className="text-sm text-neutral-500">
                            Total Requests: {leaveNewRequestsData?.count || 0}
                        </span>
                    )}
                </div>
            </div>

            <section className="bg-white border border-neutral-200/50 rounded-xl overflow-hidden shadow-sm">
                {isLeaveNewRequestsLoading ? (
                    <div className="flex items-center justify-center h-64">
                        <p className="text-neutral-500">Loading requests...</p>
                    </div>
                ) : (
                    <NewLeaveRequestDetailedTable
                        company_id={company_id}
                        company_name={company_name}
                        data={leaveNewRequestsData as LeaveResultsData}
                        pageSize={10}
                    />
                )}
            </section>
        </div>
    );
}

export default NewLeaveRequests;