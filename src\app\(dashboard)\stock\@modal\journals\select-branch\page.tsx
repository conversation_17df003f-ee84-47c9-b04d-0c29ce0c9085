'use client'

import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';

import { ViewBranchesDialog } from '../../../misc/components';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';


const _CALLBACK_URL = '/stock';
const SelectBranch = () => {

  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies } = companiesResponse as Companies;

  return (
    <>
      <ViewBranchesDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={companies} link={'/stock/journals'} />
    </>
  );
};

export default SelectBranch;
