"use client"

import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

export default function ClientHideBottomPadding({ routes }: { routes: string[] }) {
    const pathname = usePathname()
    const [shouldHide, setShouldHide] = useState(false)

    useEffect(() => {
        const shouldHideBottomPadding = routes.some((route) => pathname.includes(route))
        setShouldHide(shouldHideBottomPadding)
    }, [pathname, routes])

    return shouldHide
}
