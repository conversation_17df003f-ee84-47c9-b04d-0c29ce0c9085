'use client'

import type React from "react"
import { useState, useEffect } from "react"
import NextTopLoader from "nextjs-toploader"

import { DashboardHeader, QuickAccessModal, Sidebar } from "@/components/layout"
import { ClientHideBottomPadding } from '@/components/layout/dashboard';

import { cn } from "@/utils/classNames"


import { SidebarProvider } from "@/components/layout/dashboard/SidebarContext";

export function ClientHideBottomPaddingWrapper({
  routes,
  children,
}: {
  routes: string[]
  children: (hideBottomPadding: boolean) => React.ReactNode
}) {
  const [hideBottomPadding, setHideBottomPadding] = useState(false)

  useEffect(() => {
    // Check if current path matches any of the routes
    const currentPath = window.location.pathname
    const shouldHideBottomPadding = routes.some((route) => currentPath.includes(route))
    setHideBottomPadding(shouldHideBottomPadding)
  }, [routes])

  return <>{children(hideBottomPadding)}</>
}

// Keep the main layout as a server component
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const routesWithFullScreen = ["/instant-web/orders"]

  return (
    <>
      <NextTopLoader color="#426FFB" shadow={false} showSpinner={true} />
      <SidebarProvider>
        <div className="grid overflow-hidden bg-dash-dark-bg  md:h-screen  md:grid-cols-[auto_1fr] md:py-7 md:pr-7">
          <aside className="relative overflow-auto no-scrollbar hidden text-white md:block md:h-full">
            <Sidebar />
          </aside>
          <div className={cn("relative overflow-hidden bg-dash-light-bg md:rounded-[1.875rem]",
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            <ClientHideBottomPadding routes={routesWithFullScreen} /> && "grid-rows-[max-content_1fr] max-h-full overflow-hidden"
          )}>
            <DashboardHeader />
            <main
              className={cn(
                'min-h-screen-small-without-header overflow-y-auto bg-dash-light-bg p-0 pb-32 md:h-screen-small-without-header-desktop md:min-h-screen-small-without-header-desktop',
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                <ClientHideBottomPadding routes={routesWithFullScreen} /> && 'pb-0'
              )}
            >

              {children}
            </main>
          </div>
          <QuickAccessModal />
        </div>
      </SidebarProvider>
    </>
  )
}
