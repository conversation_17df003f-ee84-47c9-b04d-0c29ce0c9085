'use client';

import { usePathname, useRouter } from 'next/navigation';
import * as React from 'react';

import { useAuth } from '@/contexts/authentication';
import { ScrollingFeatures } from '../../sign-up/welcome/misc/components';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRouteGuard({ children }: ProtectedRouteProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { authState } = useAuth();
  const { isAuthenticated, isLoading } = authState;

  const protectedRoutes = [

    '/dashboard',
    '/analytics',

    '/subscription',
    '/analytics/select-company',
    '/send-money',
    '/savings',
    '/buy-airtime-and-utilities',
    '/instant-wage',
    '/loans',
    '/stock',
    '/stock/business-loans/',
    '/stock',
    '/stock/companies/',
    '/stock/branch-details',
    '/stock/teams',
    '/stock/product-and-categories/view',
    '/stock/suppliers/view-suppliers',
    '/stock/price-list',
    '/stock/stock-management',
    '/stock/add-stock/select-branch',
    '/stock/stock-out/select-company',
    '/stock/restock/select-branch',
    '/stock/stock-request/select-branch',
    '/stock/stock-tracking/select-branch',
    '/stock/stock-transfer/select-branch',
    '/stock/purchase-orders/select-branch',
    '/sales/business-loans/',
    '/sales',
    '/sales/dashboard',
    '/sales/transactions',
    '/sales/customers',
    '/sales/price-list',
    '/sales/sell',
    '/sales/manage-tables',
    '/sales/return-refund',
    '/sales/web-pos-access/select-branch',
    '/sales/invoicing-new',
    '/sales/ledger',
    '/sales/invoice-financing',
    '/sales/stock-financing',
    '/pos-shop-outlet',
    '/request-pos',
    '/employee-benefits/companies',
    '/payroll',
    '/payroll/all-companies',
    '/payroll/employee',
    '/payroll/time-and-attendance/', // This is already included
    '/payroll/payroll-redirect-option/time-attendance',

    '/get-linked',
    '/payroll/settings',
    '/spend-management',
    '/spend-management/overview/select-company',
    '/spend-management/companies',
    '/spend-management/teams/select-company',
    '/spend-management/requisitions/select-company',
    '/spend-management/procurement/select-company',
    '/spend-management/assets/overview/select-company',
    '/spend-management/assets/register/select-company',
    '/spend-management/suppliers/add-suppliers/select-company',
    '/spend-management/suppliers/suppliers-management/select-company',
    '/spend-management/suppliers/suppliers-settings/select-company',
    '/spend-management/budgeting/all-budgets',
    '/spend-management/expenses/all-expenses',
    '/spend-management/purchase-order/select-company',
    '/account',
    '/account/charts-of-account',
    '/account/journal-entry',
    '/account/profit-and-loss',
    '/account/balance-sheet',
    '/instant-web',
    '/instant-web/manage-website/select-company',
    '/instant-web/product',
    '/instant-web/all-products/select-branch',
    '/instant-web/add-product/select-branch',
    '/instant-web/product-categories/select-company',
    '/instant-web/qr-code/select-company',
    '/instant-web/orders',
    '/instant-web/manage-tables/select-branch',
    '/instant-web/customers/select-branch',
    '/instant-web/transactions/select-branch',
    '/instant-web/analytics/select-branch',
    '/instant-web/campaign/select-company',
    '/cards',
    '/business-loans',
    '/leaderboard',
    '/profile-settings',
    '/bank-performance',
    '/subscription-dashboard',
    '/payroll/time-and-attendance/*', // Add this line to include dynamic segments

    '/payroll/time-and-attendance/*', // Add this line to include dynamic segments
    '/performance-management/*',
    '/leave-management/*',
  ];

  // Add public routes that should be freely accessible
  const _publicRoutes = [
    '/',
    '/login',
    '/sign-up',
    '/about',
    '/contact',
    // Add other public routes here
  ];

  // Check if the current path is a protected route or starts with a protected path
  const isProtectedPath = React.useMemo(() => {
    return protectedRoutes.some(route => {
      // For exact matches
      if (pathname === route) return true;

      // For routes that should match the beginning of the path (including nested routes)
      if (route.endsWith('*')) {
        const baseRoute = route.slice(0, -1); // Remove the '*'
        return pathname.startsWith(baseRoute);
      }

      // For nested routes under a protected parent route
      return pathname.startsWith(route + '/');
    });
  }, [pathname]);

  React.useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated && pathname === '/') {
        router.replace('/dashboard');
      } else if (!isAuthenticated && isProtectedPath) {
        router.replace('/login');
      }
    }
  }, [isLoading, isAuthenticated, pathname, isProtectedPath, router]);

  const LoadingUI = () => (
    <div className="flex h-screen w-screen bg-[url('/images/background_loading.jpg')] bg-no-repeat bg-cover bg-center items-center justify-center">
      <div className="flex h-screen w-screen backdrop-blur-md bg-[#080D27]/40 items-center justify-center transition-all duration-100">
        <div className="relative w-[300px] h-[300px]">
          <ScrollingFeatures />
        </div>
      </div>
    </div>
  );

  // Show loading state for:
  // 1. Initial loading
  // 2. Protected routes when not authenticated
  // 3. Root path when authenticated
  if (
    isLoading ||
    (!isAuthenticated && isProtectedPath) ||
    (isAuthenticated && pathname === '/')
  ) {
    return <LoadingUI />;
  }

  // Only render children when we're sure about the authentication state
  // and the user is allowed to see the content
  return <>{children}</>;
}