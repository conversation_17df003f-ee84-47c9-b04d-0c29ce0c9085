'use client'

import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';


import { CreateProductModalMultiple } from '../../../misc/components/modals';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';
const AddProduct = ({
  searchParams,
}: {
  searchParams: {
    category: string;
  };
}) => {
  const categoryId = searchParams.category;

  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies } = companiesResponse as Companies;

  return (
    <>
      <CreateProductModalMultiple
        categoryId={categoryId}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies}
      />
    </>
  );
};

export default AddProduct;
