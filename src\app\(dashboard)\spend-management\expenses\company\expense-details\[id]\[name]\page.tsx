'use client'

import * as React from 'react';

import {
  AllCategoriesExpenseListTable,
  ExpenseCategoriesTable,
  SingleExpenseCards,
} from '@/app/(dashboard)/spend-management/misc/components';
import {
  SingleExpense,
  SingleExpenseCategoryList,
} from '@/app/(dashboard)/spend-management/misc/types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core';
import { useExpenseDashboardResponse } from '@/app/(dashboard)/spend-management/misc/api/expenses/getExpenseDashboardResponse';
import { useExpenseListBudgets } from '@/app/(dashboard)/spend-management/misc/api/expenses/getExpenseListBudgets';
import { AxiosError } from 'axios';
import SubscriptionErrorModal from '@/app/(dashboard)/spend-management/misc/components/subscription/ErrorSubscriptionModal';
import { SmallSpinner } from '@/icons/core';


export default function SingleCompanyExpensesDetails({
  params,
  searchParams,
}: {
  params: { name: string; id: string };
  searchParams: { budgetId: string; category: string };
}) {
  const { id } = params;
  // const name = decodeURIComponent(urlName);

  const { budgetId, category } = searchParams;



  const { data: singleCompanyExpense, error: expenseErro, isLoading: loadingDash } = useExpenseDashboardResponse(id, budgetId, category)

  const { data: expenseListResponse, error: listBudgetError, isLoading: loadingExpe } = useExpenseListBudgets(id, budgetId, category)

  const statusExpenseErro = expenseErro as AxiosError
  const statusListBudgetError = listBudgetError as AxiosError
  const statusError: AxiosError = statusListBudgetError || statusExpenseErro
  const isLoading = loadingDash || loadingExpe

  return (
    <>
      {isLoading && <div className='py-8 flex justify-center items-center'><SmallSpinner color='blue' /></div>}
      {(!isLoading && statusError?.response?.status !== 403) && (<>
        <div className="mb-2 bg-white">
          <SingleExpenseCards
            initialSingleCompanyExpense={singleCompanyExpense as SingleExpense}
          />
        </div>

        <div className="lg:mx-11 lg:rounded-10">
          <Tabs defaultValue="by-item">
            <div className="rounded-10 bg-white">
              <TabsList className="flex mb-1 w-full justify-start rounded-10 bg-white pb-0 text-main-solid md:max-w-sm md:pl-6">
                <TabsTrigger
                  className="inline-flex w-full items-center justify-center rounded-none border-b-2 border-b-transparent pb-1.5 pt-4 text-base data-[state=active]:border-b-solid-underline data-[state=active]:text-main-solid data-[state=active]:shadow-none"
                  value="by-item"
                >
                  By Item
                </TabsTrigger>
                <TabsTrigger
                  className="inline-flex w-full items-center justify-center rounded-none border-b-2 border-b-transparent pb-1.5 pt-4 text-base data-[state=active]:border-b-solid-underline data-[state=active]:text-main-solid data-[state=active]:shadow-none"
                  value="by-category"
                >
                  By Category
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent
              className="mt-1 rounded-10 bg-white px-6 lg:px-0"
              value="by-item"
            >
              <AllCategoriesExpenseListTable
                budgetId={budgetId}
                category={category}
                headerContainerClassName="px-0 md:px-0 lg:px-6"
                initialSingleExpenseCategoryList={expenseListResponse as SingleExpenseCategoryList}
                tableContainerClassName="py-0 px-0 md:px-0 lg:px-6 md:py-0 lg:py-0"
              />
            </TabsContent>

            <TabsContent
              className="mt-1 rounded-10 bg-white px-6 lg:px-0"
              value="by-category"
            >
              <ExpenseCategoriesTable
                budgetId={budgetId}
                category={category}
                headerContainerClassName="px-0 md:px-0 lg:px-6"
                initialSingleCompanyExpense={singleCompanyExpense}
                tableContainerClassName="py-0 px-0 md:px-0 lg:px-6 md:py-0 lg:py-0"
              />
            </TabsContent>
          </Tabs>
        </div>

      </>)}

      {
        (statusError?.response?.status === 403 && !isLoading) && <SubscriptionErrorModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          message={String(statusError?.response?.data?.error as unknown as string) ?? ""}
        />
      }
    </>
  );
}
