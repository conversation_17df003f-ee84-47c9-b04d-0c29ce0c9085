'use client';

import { use<PERSON>ara<PERSON>, usePathname, useRouter } from 'next/navigation';
import React from 'react';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
  Button,
  ErrorModal,
} from '@/components/core';
import { useBooleanStateControl, useRouteChangeEvent, useErrorModalState } from '@/hooks';
import { useCompanies } from '../api';
import { CompanyListEntity } from '../types';
import { cn } from '@/utils/classNames';
import { XCircle, CheckCircle2, ChevronRight, Clock, CreditCard } from 'lucide-react';
import { useCreateInvoice } from '@/app/(dashboard)/subscription/misc/api/createSubscribe';
import { AxiosError } from 'axios';
import { formatAxiosErrorMessage } from '@/utils/errors';

interface CompanySwitcherProps {
  companies?: CompanyListEntity[] | undefined;
}

const pathsWithCompanySwitcher = ['/payroll/payroll-dash/', '/payroll/employee-view/'];

export function ModuleSubscriptionModal({ isOpen, onClose, company }: { isOpen: boolean; onClose: () => void; company: CompanyListEntity }) {
  const router = useRouter();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const allModules = [
    ...company.active_subscriptions.flatMap(sub =>
      sub.modules.map(module => ({
        ...module,
        subscription: sub,
        plan_id: module.plan_id
      }))
    ),
    ...company.not_started_subscriptions.flatMap(sub =>
      sub.modules.map(module => ({
        ...module,
        subscription: sub,
        plan_id: module.plan_id
      }))
    )
  ];

  const hasHRModule = allModules.some(module =>
    module.module.code === 'HR' && module.is_active
  );

  const { mutate: handlePaystackInvoice, isLoading: isLoadingPaystack } = useCreateInvoice();

  const handlePaystack = (moduleSubscriptionId: number, planId: number) => {
    if (!planId) {
      openErrorModalWithMessage('No plan selected for this module');
      return;
    }

    handlePaystackInvoice(
      {
        company_id: String(company.id),
        requestData: [
          {
            module_id: moduleSubscriptionId,
            plan: planId
          }
        ]
      },
      {
        onSuccess: (data) => {
          if (data) {
            window.open(data?.payment_link, '_blank');
          }
        },
        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        }
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-xl h-[650px] flex flex-col">
        <DialogHeader className="px-6 py-4">
          <DialogTitle className="text-sm font-semibold flex items-center gap-2">
            Module Subscription Details
          </DialogTitle>
          <DialogClose>Close</DialogClose>
        </DialogHeader>

        {/* Scrollable content area */}
        <div className="p-6 overflow-y-auto flex-1">
          {!hasHRModule && (
            <div className="mb-6 p-4 bg-red-50 border border-red-100 rounded-lg flex items-start gap-3">
              <XCircle className="h-5 w-5 text-red-500 mt-0.5 shrink-0" />
              <div>
                <p className="font-medium text-red-700">HR Management is not included</p>
                <p className="text-sm text-red-600 mt-1">
                  Contact your account manager to add this module to your subscription.
                </p>
              </div>
            </div>
          )}

          <h3 className="text-sm font-medium text-slate-700 mb-3">Your current subscription includes:</h3>

          <div className="space-y-3 mb-6">
            {allModules.map((module) => (
              <div
                className="p-3 border rounded-lg hover:bg-slate-50 transition-colors"
                key={module.module_subscription_id}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <CheckCircle2 className="h-5 w-5 text-emerald-500 shrink-0" />
                    <span className="font-medium">{module.module.name}</span>
                  </div>
                  <div
                    className={cn(
                      "flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium",
                      module.is_not_started && "bg-amber-100 text-amber-700",
                      module.status === "active" && !module.is_not_started && "bg-emerald-100 text-emerald-700",
                      module.status === "pending" && !module.is_not_started && "bg-amber-100 text-amber-700",
                      module.status === "expired" && !module.is_not_started && "bg-red-100 text-red-700",
                    )}
                  >
                    {module.is_not_started ? (
                      <>
                        <Clock className="h-3 w-3" />
                        <span>Pending Payment</span>
                      </>
                    ) : module.status === "active" ? (
                      <>
                        <CheckCircle2 className="h-3 w-3" />
                        <span>Active</span>
                      </>
                    ) : module.status === "pending" ? (
                      <>
                        <Clock className="h-3 w-3" />
                        <span>Pending</span>
                      </>
                    ) : (
                      <>
                        <XCircle className="h-3 w-3" />
                        <span>Expired</span>
                      </>
                    )}
                  </div>
                </div>
                {module.module.description && (
                  <p className="text-sm text-slate-500 mt-1 ml-8">{module.module.description}</p>
                )}
                {module.is_not_started && (
                  <div className="mt-3 ml-8">
                    <Button
                      className="gap-1.5"
                      disabled={isLoadingPaystack}
                      size="default"
                      variant="outlined"
                      onClick={() => handlePaystack(module.module.id, module.plan_id)}
                    >
                      <CreditCard className="h-3.5 w-3.5" />
                      Complete Payment
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Fixed button container at the bottom */}
        <div className="p-4 border-t sticky bottom-0 bg-white">
          <Button
            className="gap-1 w-full sm:w-auto"
            onClick={() => {
              router.push(`/subscription?companyId=${company.id}&companyName=${company.company_name}`)
            }}
          >
            Manage Subscription <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </DialogContent>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={errorModalMessage || 'Please check your inputs and try again.'}
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button className="grow bg-red-950 text-base" size="lg" onClick={closeErrorModal}>
            Okay
          </Button>
        </div>
      </ErrorModal>
    </Dialog>
  );
}

export function HeaderCompanySwitcher({
  companies: initialCompanies = [],
}: CompanySwitcherProps) {
  const pathname = usePathname();
  const params = useParams();
  const router = useRouter();
  const { data: companies } = useCompanies(initialCompanies);
  const [selectedCompany, setSelectedCompany] = React.useState<CompanyListEntity | null>(null);
  const [showModuleModal, setShowModuleModal] = React.useState(false);

  const {
    state: isDesktopPopoverOpen,
    setState: setDesktopPopoverState,
    setFalse: closeDesktopPopover,
  } = useBooleanStateControl();

  const {
    state: isMobilePopoverOpen,
    setState: setMobilePopoverState,
    setFalse: closeMobilePopover,
  } = useBooleanStateControl();

  useRouteChangeEvent(() => {
    closeDesktopPopover();
    closeMobilePopover();
  });

  const hasSwitcher = React.useMemo(
    () =>
      pathsWithCompanySwitcher.some(page => pathname.includes(page)) ||
      pathsWithCompanySwitcher.includes(pathname),
    [pathname]
  );

  const handleCompanyClick = (company: CompanyListEntity) => {
    if (!company.is_subscription) {
      router.push(`/subscription?companyId=${company.id}&companyName=${company.company_name}`);
      return;
    }

    const allModules = [
      ...company.active_subscriptions.flatMap(sub =>
        sub.modules.map(module => ({
          ...module,
          subscription: sub,
          plan_id: module.plan_id
        }))
      ),
      ...company.not_started_subscriptions.flatMap(sub =>
        sub.modules.map(module => ({
          ...module,
          subscription: sub,
          plan_id: module.plan_id
        }))
      )
    ];

    const hasHRModule = allModules.some(module =>
      module.module.code === 'HR' && module.is_active
    );

    if (!hasHRModule) {
      setSelectedCompany(company);
      setShowModuleModal(true);
      return;
    }

    router.push(`/payroll/payroll-dash/${company.id}/payroll-dashboard/${company.company_name}?payrollstage=${""}`);
  };

  const getSubscriptionStatus = (company: CompanyListEntity) => {
    const hasNotStartedSubscriptions = company.not_started_subscriptions.length > 0;

    if (hasNotStartedSubscriptions) {
      return {
        text: "Pending Payment",
        className: "text-amber-600 text-xxs"
      };
    }
    if (company.is_free_trial) {
      return {
        text: "Free Trial",
        className: "text-amber-600 text-xxs"
      };
    }
    if (company.is_paid_subscription) {
      return {
        text: "Paid Subscription",
        className: "text-green-600 text-xxs"
      };
    }
    if (company.is_subscription) {
      return {
        text: "Subscribed",
        className: "text-green-600 text-xxs"
      };
    }
    return {
      text: "Not Subscribed",
      className: "text-red-600 text-xxs"
    };
  };

  return !!companies && hasSwitcher ? (
    <>
      {/* Desktop Popover */}
      <Popover open={isDesktopPopoverOpen} onOpenChange={setDesktopPopoverState}>
        <PopoverTrigger className="rounded-lg bg-[#F2F5FF] items-center gap-1 px-3 py-1.5 text-xs text-[#242424] justify-between hidden xl:flex">
          <span className="flex items-center gap-1 mr-[6px]">
            Company: <span className="text-[#032282]">{decodeURI(String(params.company || params.companyName))}</span>
          </span>

          <svg fill="none" height="5" viewBox="0 0 6 5" width="6" xmlns="http://www.w3.org/2000/svg">
            <path d="M3.86602 4.5C3.48112 5.16667 2.51887 5.16667 2.13397 4.5L0.401923 1.5C0.0170231 0.833333 0.498149 0 1.26795 0H4.73205C5.50185 0 5.98298 0.833333 5.59808 1.5L3.86602 4.5Z" fill="#00775D" />
          </svg>

        </PopoverTrigger>

        <PopoverContent
          align="start"
          className="mt-2 w-full rounded-10 border-none p-0 pb-2 text-xs [@media(min-width:408px)]:max-w-[18.375rem]"
        >
          <p className="px-4 pb-2.5 pt-4">Select company to change view.</p>
          <ul className="space-y-1 px-2">
            {companies?.map((company) => {
              const status = getSubscriptionStatus(company);
              return (
                <li key={company.id}>
                  <Button
                    className="block w-full rounded-[.75rem] px-6 py-2.5 text-left text-label-text transition ease-in-out hover:bg-[#F8FAFF]"
                    variant="white"
                    onClick={() => handleCompanyClick(company)}
                  >
                    <div className="flex flex-col gap-1">
                      <span>{company.company_name}</span>
                      <span className={status.className}>{status.text}</span>
                    </div>
                  </Button>
                </li>
              );
            })}
          </ul>
        </PopoverContent>
      </Popover>

      {/* Mobile Popover */}
      <Popover open={isMobilePopoverOpen} onOpenChange={setMobilePopoverState}>
        <PopoverTrigger className="rounded-lg bg-[#F2F5FF] items-center gap-1 px-3 py-1.5 text-xs text-[#242424] justify-between flex xl:hidden">
          <span className="flex items-center gap-1 mr-[6px] text-[#032282]">
            Switch
          </span>

          <svg fill="none" height="5" viewBox="0 0 6 5" width="6" xmlns="http://www.w3.org/2000/svg">
            <path d="M3.86602 4.5C3.48112 5.16667 2.51887 5.16667 2.13397 4.5L0.401923 1.5C0.0170231 0.833333 0.498149 0 1.26795 0H4.73205C5.50185 0 5.98298 0.833333 5.59808 1.5L3.86602 4.5Z" fill="#00775D" />
          </svg>

        </PopoverTrigger>

        <PopoverContent
          align="start"
          className="mt-2 w-full rounded-10 border-none p-0 pb-2 text-xs [@media(min-width:408px)]:max-w-[18.375rem]"
        >
          <p className="px-4 pb-2.5 pt-4">Select company to change view.</p>
          <ul className="space-y-1 px-2">
            {companies?.map((company) => {
              const status = getSubscriptionStatus(company);
              return (
                <li key={company.id}>
                  <Button
                    className="block w-full rounded-[.75rem] px-6 py-2.5 text-left text-label-text transition ease-in-out hover:bg-[#F8FAFF]"
                    variant="white"
                    onClick={() => handleCompanyClick(company)}
                  >
                    <div className="flex flex-col gap-1">
                      <span>{company.company_name}</span>
                      <span className={status.className}>{status.text}</span>
                    </div>
                  </Button>
                </li>
              );
            })}
          </ul>
        </PopoverContent>
      </Popover>

      {selectedCompany && (
        <ModuleSubscriptionModal
          company={selectedCompany}
          isOpen={showModuleModal}
          onClose={() => {
            setShowModuleModal(false);
            setSelectedCompany(null);
          }}
        />
      )}
    </>
  ) : null;
}
