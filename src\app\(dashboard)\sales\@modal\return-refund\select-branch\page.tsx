"use client"
// import { redirect } from 'next/navigation';
import React from 'react';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';
// import { Companies, CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';


import SelectBranchDialog from '../../../misc/components/modals/SelectBranchDialog';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { redirect } from 'next/navigation';
import { LoaderModal } from '@/components/core';

const Page = () => {
  const { data: defaultData, isLoading } = useDefaultCompanyBranch()

  const { data } = useCompaniesList()

  if (defaultData?.data.company_id !== null && defaultData?.data.branch_id !== null && !isLoading) {
    return redirect(`/sales/return-refund?companyId=${defaultData?.data.company_id}&companyName=${defaultData?.data.company}&branch=${defaultData?.data.branch_id}&branchName=${defaultData?.data.branch}`);
  }

  if (isLoading) {
    return <LoaderModal isOpen={isLoading} />;
  }
  return (
    <>
      <SelectBranchDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={data?.results as CompaniesResultsEntity[]}
        link={'/sales/dashboard'}
        queryParametersOnly={true}
        subLink={'return-refund'}
      />
    </>
  );
};

export default Page;
