'use client'

import { useSearchParams } from 'next/navigation';
import React from 'react';

import { CreateSubCategoryModal } from '../misc/components';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { LoaderModal } from '@/components/core';

const Page = () => {
  const searchParams = useSearchParams();
  const categoryId = searchParams.get('category') || '';

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();

  return (
    <>
      <LoaderModal isOpen={isCompanyLoading} />
      <CreateSubCategoryModal
        categoryId={categoryId}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies?.results || []}
      />
    </>
  );
};

export default Page;
