'use client';

// import { format as formatDate } from 'date-fns';

import { LinkButton } from '@/components/core';
import { cn } from '@/utils/classNames';
import { convertNumberToNaira } from '@/utils/currency';
import { shortenNumberWithReadableSymbols } from '@/utils/numbers';

import { useGetSingleCompany } from '../api';
import { BudgetResultsEntity, Budgets, Companies, CompaniesTeamsEntity } from '../types';
import { BudgetSummaryTeamCard } from './BudgetSummaryTeamCard';
import moment from 'moment';

const formatAmount = (number: number) => {
  if (isNaN(number)) return 'N/A';

  return number >= 1e5
    ? `₦${shortenNumberWithReadableSymbols(number, 2)}`
    : convertNumberToNaira(number);
};

interface SingleBudgetSummaryCardsProps {
  budgets: BudgetResultsEntity[];
  name: string;
  durations: number;
  id: string;
  budget_amount: string;
  allocated_amount: number;
  amount_spent: number;
  percent_amount_spent: number;
  initialData: Companies;
  isReport?: boolean;
  team: {
    id: string;
    team_name: string;
  } | null;
  companyName: string;
  companyId: string;
  singleBudgetResponse: Budgets;
  budgetBalance: number;
  outsideBudgetExpense: number;
  updatedAt: string;
}

export function SingleBudgetSummaryCards({
  name,
  id,
  budget_amount,
  allocated_amount,
  amount_spent,
  // initialData,
  team: linkedTeam,
  companyId,
  companyName,
  singleBudgetResponse,
  budgetBalance,
  outsideBudgetExpense,
  updatedAt,
}: SingleBudgetSummaryCardsProps) {
  const { data: singleCompany } = useGetSingleCompany(companyId);


  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  const { teams } = singleCompany?.results?.[0] || {};

  const areThereTeams = !!teams && teams?.length > 0;

  return (
    <div className="relative z-10 overflow-hidden bg-requisition-single-company-bg-gradient px-6 py-4 md:rounded-10 md:px-7 lg:mb-2 lg:px-11">
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2 2xl:grid-cols-4">
        <div
          className={cn(
            "flex min-w-0 grow flex-col justify-between rounded-xl bg-main-solid bg-[url('/images/requisition/card-backgrounds/diagonal-triangles.svg')] bg-cover bg-right bg-no-repeat p-6 text-white"
          )}
        >
          <div className="flex justify-between gap-2 md:gap-4">
            <svg
              fill="none"
              height={40}
              viewBox="0 0 40 40"
              width={40}
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx={20} cy={20} fill="#fff" fillOpacity={0.1} r={20} />
              <path
                d="M15.975 28.958c-1.984 0-3.608-1.442-3.608-3.217v-1.7a.63.63 0 0 1 .624-.625.63.63 0 0 1 .626.625c0 1.042 1.008 1.825 2.358 1.825s2.358-.783 2.358-1.825a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v1.7c0 1.775-1.617 3.217-3.608 3.217Zm-2.142-2.4c.367.683 1.192 1.15 2.142 1.15s1.775-.475 2.141-1.15c-.591.358-1.325.566-2.141.566-.817 0-1.55-.208-2.142-.566Z"
                fill="#fff"
              />
              <path
                d="M15.975 24.834c-1.367 0-2.592-.625-3.2-1.617a2.79 2.79 0 0 1-.409-1.458c0-.875.384-1.692 1.084-2.3 1.35-1.183 3.675-1.183 5.033-.008.7.616 1.092 1.433 1.092 2.308a2.79 2.79 0 0 1-.409 1.458c-.6.992-1.825 1.617-3.191 1.617Zm0-5.042c-.65 0-1.25.217-1.7.608-.425.367-.659.85-.659 1.359 0 .291.075.558.225.808.384.634 1.2 1.025 2.134 1.025.933 0 1.75-.391 2.125-1.016.15-.242.225-.517.225-.809 0-.508-.234-.991-.659-1.366-.441-.392-1.041-.609-1.691-.609Z"
                fill="#fff"
              />
              <path
                d="M15.975 27.124c-2.059 0-3.608-1.325-3.608-3.075v-2.291c0-1.775 1.616-3.217 3.608-3.217.941 0 1.841.325 2.516.908.7.617 1.092 1.434 1.092 2.309v2.291c0 1.75-1.55 3.075-3.608 3.075Zm0-7.333c-1.3 0-2.358.883-2.358 1.967v2.291c0 1.042 1.008 1.825 2.358 1.825s2.358-.783 2.358-1.825v-2.291c0-.509-.233-.992-.658-1.367a2.61 2.61 0 0 0-1.7-.6Zm9.891 2.542c-1.258 0-2.325-.934-2.424-2.134a2.287 2.287 0 0 1 .683-1.858 2.258 2.258 0 0 1 1.633-.675H27.5c.825.025 1.458.675 1.458 1.475v1.717c0 .8-.633 1.45-1.433 1.475h-1.659Zm1.609-3.417h-1.709c-.291 0-.558.108-.75.308a1.05 1.05 0 0 0-.324.867c.041.55.575.992 1.174.992H27.5c.108 0 .208-.1.208-.225V19.14c0-.125-.1-.217-.233-.225Z"
                fill="#fff"
              />
              <path
                d="M23.334 27.708H21.25a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h2.084c2.15 0 3.541-1.392 3.541-3.542v-.583h-1.008c-1.259 0-2.325-.934-2.425-2.134a2.287 2.287 0 0 1 .683-1.858 2.259 2.259 0 0 1 1.633-.675h1.109v-.583c0-1.95-1.142-3.292-2.992-3.509-.2-.033-.375-.033-.55-.033h-7.5c-.2 0-.392.017-.583.042-1.834.233-2.959 1.566-2.959 3.5v1.666a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625v-1.666c0-2.567 1.584-4.425 4.042-4.734.225-.033.483-.058.75-.058h7.5c.2 0 .458.008.725.05 2.458.283 4.067 2.15 4.067 4.742v1.208a.63.63 0 0 1-.625.625h-1.734c-.291 0-.558.108-.75.308-.241.234-.358.55-.325.867.042.55.576.992 1.175.992H27.5a.63.63 0 0 1 .625.625v1.208c0 2.867-1.925 4.792-4.791 4.792Z"
                fill="#fff"
              />
            </svg>

            <LinkButton
              className="grow-0 rounded-lg bg-white/10 px-5 py-1 text-xs font-semibold text-white"
              color="white"
              href={`/spend-management/budgeting/edit-budget?id=${id}`}
            >
              Update
            </LinkButton>
          </div>

          <div>
            <div className="mt-7 flex flex-wrap justify-between gap-x-2 gap-y-3">
              <p className="space-y-0.5 overflow-x-auto">
                <span className="no-scrollbar block overflow-x-auto font-heading text-lg font-semibold">
                  {formatAmount(Number(budget_amount))}
                </span>

                <span className="block text-sm text-[#cdcdcd]">
                  Budgeted amount
                </span>
              </p>

              <p className="space-y-0.5 overflow-x-auto">
                <span className="no-scrollbar block overflow-x-auto font-heading text-lg font-semibold">
                  {formatAmount(Number(budgetBalance))}
                </span>

                <span className="block text-sm text-[#cdcdcd]">
                  Budget balance
                </span>
              </p>
            </div>

            <p className="text-xxs text-[#cdcdcd]/70">
              <span className="sr-only">Last updated</span> As at:{' '}
              {/* {formatDate(new Date(updatedAt), 'dd/MM/yyyy | hh:mma')} */}
              {moment(updatedAt).format('DD/MM/YYYY | hh:mma') ?? null}
            </p>
          </div>
        </div>

        <div className="flex grow flex-col justify-between rounded-xl bg-white p-6">
          <div className="flex justify-between gap-2 md:gap-4">
            <svg
              fill="none"
              height={40}
              viewBox="0 0 40 40"
              width={40}
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx={20} cy={20} fill="#EDFFF7" r={20} />
              <path
                d="M22.408 25.184h-7.325c-.433 0-.841-.042-1.208-.125a3.12 3.12 0 0 1-.667-.183c-1.108-.417-2.416-1.442-2.416-3.992v-4.292c0-2.725 1.566-4.291 4.291-4.291h7.325c2.3 0 3.784 1.1 4.184 3.1.075.366.108.758.108 1.191v4.292c0 2.742-1.558 4.3-4.292 4.3Zm-7.316-11.617c-2.05 0-3.042.992-3.042 3.042v4.292c0 1.491.525 2.408 1.6 2.817.167.058.325.1.475.124.317.067.625.1.967.1h7.325c2.05 0 3.041-.991 3.041-3.041v-4.292c0-.35-.025-.658-.083-.942-.283-1.416-1.25-2.1-2.958-2.1h-7.325Z"
                fill="#00A37D"
              />
              <path
                d="M24.909 27.683h-7.325c-.709 0-1.342-.1-1.884-.308-1.225-.459-2.025-1.425-2.3-2.809a.616.616 0 0 1 .75-.725c.275.059.583.092.934.092h7.325c2.05 0 3.041-.992 3.041-3.042V16.6c0-.35-.025-.659-.083-.942a.64.64 0 0 1 .166-.567.597.597 0 0 1 .567-.166c2 .408 3.1 1.891 3.1 4.175v4.291c0 2.734-1.558 4.292-4.291 4.292Zm-9.975-2.5c.266.483.666.825 1.216 1.025.4.15.884.225 1.442.225h7.325c2.05 0 3.041-.992 3.041-3.042V19.1c0-1.317-.408-2.192-1.25-2.65v4.44c0 2.725-1.566 4.292-4.291 4.292h-7.484Z"
                fill="#00A37D"
              />
              <path
                d="M18.75 21.576a2.827 2.827 0 0 1-2.825-2.825 2.827 2.827 0 0 1 2.825-2.825 2.827 2.827 0 0 1 2.825 2.825 2.827 2.827 0 0 1-2.825 2.825Zm0-4.4a1.58 1.58 0 0 0-1.575 1.575 1.58 1.58 0 0 0 1.575 1.575 1.58 1.58 0 0 0 1.575-1.575 1.58 1.58 0 0 0-1.575-1.575Zm-4.766 4.032a.63.63 0 0 1-.625-.625v-3.667a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v3.667a.624.624 0 0 1-.625.625Zm9.524 0a.63.63 0 0 1-.625-.625v-3.667a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v3.667a.624.624 0 0 1-.625.625Z"
                fill="#00A37D"
              />
            </svg>

            <LinkButton
              className="grow-0 rounded-lg px-5 py-1 text-xs font-semibold"
              href={`/spend-management/budgeting/edit-categories?id=${id}&amount=${budget_amount}&name=${name}&companyId=${companyId}`}
              variant="light"
            >
              Allocate
            </LinkButton>
          </div>

          <div className="mt-7 flex flex-wrap justify-between gap-x-2 gap-y-3">
            <p className="space-y-0.5">
              <span className="no-scrollbar block overflow-x-auto font-heading text-lg font-semibold text-main-solid">
                {formatAmount(Number(allocated_amount))}
              </span>

              <span className="block text-sm text-input-placeholder">
                Allocated amount
              </span>
            </p>

            <p className="space-y-0.5">
              <span className="no-scrollbar block overflow-x-auto font-heading text-lg font-semibold text-main-solid">
                {formatAmount(Number(amount_spent))}
              </span>

              <span className="block text-sm text-input-placeholder">
                Amount spent
              </span>
            </p>
          </div>
        </div>

        <div className="flex grow flex-col justify-between rounded-xl bg-white p-6">
          <div className="flex justify-between gap-2 md:gap-4">
            <svg
              fill="none"
              height={40}
              viewBox="0 0 40 40"
              width={40}
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx={20} cy={20} fill="#FFEDED" r={20} />
              <path
                d="M22.408 25.184h-7.325c-.433 0-.841-.042-1.208-.125a3.12 3.12 0 0 1-.667-.183c-1.108-.417-2.416-1.442-2.416-3.992v-4.292c0-2.725 1.566-4.291 4.291-4.291h7.325c2.3 0 3.784 1.1 4.184 3.1.075.366.108.758.108 1.191v4.292c0 2.742-1.558 4.3-4.292 4.3Zm-7.316-11.617c-2.05 0-3.042.992-3.042 3.042v4.292c0 1.491.525 2.408 1.6 2.817.167.058.325.1.475.124.317.067.625.1.967.1h7.325c2.05 0 3.041-.991 3.041-3.041v-4.292c0-.35-.025-.658-.083-.942-.283-1.416-1.25-2.1-2.958-2.1h-7.325Z"
                fill="#EF4444"
              />
              <path
                d="M24.909 27.683h-7.325c-.709 0-1.342-.1-1.884-.308-1.225-.459-2.025-1.425-2.3-2.809a.616.616 0 0 1 .75-.725c.275.059.583.092.934.092h7.325c2.05 0 3.041-.992 3.041-3.042V16.6c0-.35-.025-.659-.083-.942a.64.64 0 0 1 .166-.567.597.597 0 0 1 .567-.166c2 .408 3.1 1.891 3.1 4.175v4.291c0 2.734-1.558 4.292-4.291 4.292Zm-9.975-2.5c.266.483.666.825 1.216 1.025.4.15.884.225 1.442.225h7.325c2.05 0 3.041-.992 3.041-3.042V19.1c0-1.317-.408-2.192-1.25-2.65v4.44c0 2.725-1.566 4.292-4.291 4.292h-7.484Z"
                fill="#EF4444"
              />
              <path
                d="M18.75 21.576a2.827 2.827 0 0 1-2.825-2.825 2.827 2.827 0 0 1 2.825-2.825 2.827 2.827 0 0 1 2.825 2.825 2.827 2.827 0 0 1-2.825 2.825Zm0-4.4a1.58 1.58 0 0 0-1.575 1.575 1.58 1.58 0 0 0 1.575 1.575 1.58 1.58 0 0 0 1.575-1.575 1.58 1.58 0 0 0-1.575-1.575Zm-4.766 4.032a.63.63 0 0 1-.625-.625v-3.667a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v3.667a.624.624 0 0 1-.625.625Zm9.524 0a.63.63 0 0 1-.625-.625v-3.667a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v3.667a.624.624 0 0 1-.625.625Z"
                fill="#EF4444"
              />
            </svg>
          </div>

          <div className="flex items-end justify-between gap-2">
            <p className="mt-7 space-y-0.5 overflow-x-auto">
              <span className="no-scrollbar block overflow-x-auto font-heading text-lg font-semibold text-main-solid">
                {formatAmount(Number(outsideBudgetExpense))}
              </span>

              <span className="block text-sm text-input-placeholder">
                Outside budget expense
              </span>
            </p>
          </div>
        </div>

        <BudgetSummaryTeamCard
          areThereTeams={areThereTeams}
          budgetId={id}
          companyId={companyId}
          companyName={companyName}
          linkedTeam={linkedTeam}
          singleBudgetResponse={singleBudgetResponse}
          teams={teams as CompaniesTeamsEntity[]}
        />
      </div>
    </div>
  );
}
