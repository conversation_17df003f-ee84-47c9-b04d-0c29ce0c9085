'use client';

import * as React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-label';
import type { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import { Controller, useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

import {
  Button,
  ErrorModal,
  FormError,
  Input,
  LinkButton,
  LoaderModal,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/core';
import { listOfStates, stateLGAMap } from '@/data';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';

import { useRegisterUserDetails } from '../api';
import { countries } from '@/app/(dashboard)/stock/misc/utils/countries';
import { cn } from '@/utils/classNames';
import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';

const GetStartedFormSchema = z.object({
  phone_number: z
    .string({ required_error: 'Please enter your phone number.' })
    .trim()
    .min(1, { message: 'Please enter your phone number.' }),
  username: z
    .string({ required_error: 'Please enter a username.' })
    .trim()
    .min(1, { message: 'Please enter a username.' }),
  first_name: z
    .string({ required_error: 'Please enter your first name.' })
    .trim()
    .min(1, { message: 'Please enter your first name.' }),
  last_name: z
    .string({ required_error: 'Please enter your last name.' })
    .trim()
    .min(1, { message: 'Please enter your last name.' }),
  business_name: z.string().trim().optional(),
  email: z
    .string({ required_error: 'Please enter your email.' })
    .trim()
    .min(1, { message: 'Please enter your email.' }),
  gender: z
    .string({ required_error: 'Please select a gender.' })
    .trim()
    .min(1, { message: 'Please select a gender.' }),
  state: z
    .string({ required_error: 'Please select a state.' })
    .trim()
    .min(1, { message: 'Please select a state.' }),
  lga: z
    .string({ required_error: 'Please select a Local Government Area.' })
    .trim()
    .min(1, { message: 'Please select a Local Government Area.' }),
  nearest_landmark: z
    .string({
      required_error: 'Please enter the nearest landmark to your address.',
    })
    .trim()
    .min(1, { message: 'Please enter the nearest landmark to your address.' }),
  street: z
    .string({ required_error: 'Please enter your street.' })
    .trim()
    .min(1, { message: 'Please enter your street.' }),
  country_code: z.
    string().
    optional(),
});

export type GetStartedFormValues = z.infer<typeof GetStartedFormSchema>;

export function GetStartedForm() {

  const [_isTokenCleared, setIsTokenCleared] = React.useState(false);

  React.useEffect(() => {
    try {
      // Clear the token
      tokenStorage.clearToken();
      localStorage.removeItem(`${'MANAGEMENT_PAYBOX'}TOKEN`);

      // Update state based on token status
      setIsTokenCleared(!tokenStorage.getToken());
    } catch (error) {
      console.error("Error during logout:", error);
    }
  }, []);

  const router = useRouter();

  const { state: isLoaderModalOpen, setTrue: openLoaderModal } =
    useBooleanStateControl();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    control,
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<GetStartedFormValues>({
    resolver: zodResolver(GetStartedFormSchema),
  });

  const phoneNameValue = useWatch({
    control,
    name: 'phone_number',
  });


  const selectedStateValue = useWatch({
    control,
    name: 'state',
  }) as (typeof listOfStates)[number];

  const businessNameValue = useWatch({
    control,
    name: 'business_name',
  });

  const lgasInSelectedState = selectedStateValue
    ? stateLGAMap?.[selectedStateValue]
    : undefined;

  const {
    mutate: registerUserDetails,
    isLoading: isRegisterUserDetailsLoading,
  } = useRegisterUserDetails();

  const [countryList, setCountryList] = React.useState<{ name: string, code: string, phoneCode: string }[]>();
  React.useEffect(() => {
    setCountryList(countries);
  }, []);

  const countryOptions = countryList?.map(({ name, phoneCode, code }) => ({
    value: phoneCode,
    label: name,
    code: code,
  }));

  // Inside your component
  const [searchQuery, setSearchQuery] = React.useState('');

  // const filteredOptions = countryOptions?.filter(option =>
  //   option.label.toLowerCase().includes(searchQuery.toLowerCase())
  // );

  const filteredOptions = countryOptions?.filter(option =>
    option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    option.value.includes(searchQuery) ||
    option.code.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const onGetStartedSubmit = (submittedData: GetStartedFormValues) => {
    const { country_code, ...submittedDataWithoutCountryCode } = submittedData
    const formattedPhoneNumber = submittedData.phone_number.startsWith('0') ? `${country_code}${submittedData.phone_number.substring(1)}` : `${country_code}${submittedData.phone_number}`;

    const formattedSignUpData = {
      ...submittedDataWithoutCountryCode,
      phone_number: formattedPhoneNumber,
      type_of_user: 'AGENT', source: 'PAYBOX',
      onboarding_status: 'REGISTRATION'
    }


    registerUserDetails(formattedSignUpData,

      {
        onSuccess: () => {
          openLoaderModal();
          router.push(
            `/sign-up/email-otp/?email=${submittedData.email}&phone=${formattedPhoneNumber}`
          );
        },

        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        },
      }
    );
  };

  return (
    <>
      <LoaderModal isOpen={isLoaderModalOpen} />

      <form
        className="relative z-10"
        onSubmit={handleSubmit(onGetStartedSubmit)}
      >
        <Label className="sr-only" htmlFor="email">
          Email
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="email"
          placeholder="Email"
          type="email"
          {...register('email')}
        />
        {errors?.email && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.email.message}
          />
        )}

        <Label className="sr-only" htmlFor="phone">
          Phone Number
        </Label>
        <div className='flex gap-3  mt-4 items-center'>
          <div>
            <Controller
              control={control}
              name="country_code"
              render={({ field }) => (
                <Select defaultValue={field.value} onValueChange={field.onChange}>
                  <SelectTrigger
                    className={cn(
                      'flex h-10 w-full items-center justify-between gap-2 rounded-md !bg-white/30 px-3 text-base font-medium ring-offset-white transition duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
                      'border !z-[99999999999] bg-transparent !whitespace-nowrap text-xs pr-0.5 pl-4 login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg px-6 py-3.5 font-medium text-white placeholder:text-white focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70'
                    )}
                  >
                    <SelectValue className='!whitespace-nowrap !text-white !placeholder:text-white' placeholder="Select country code" />
                  </SelectTrigger>
                  <SelectContent>
                    <Input
                      className="p-2 mb-2 rounded-md" // Add your styling here
                      placeholder="Search..."
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    {filteredOptions?.map((option) => (
                      <SelectItem key={option.label} value={option.value}>
                        {option.label} {option.value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
          <div className="relative w-full">
            <Input
              className="login-autofill-text w-full login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
              id="phone"
              maxLength={20}
              placeholder="Phone Number"
              type="tel"
              {...register('phone_number')}
              onInput={(e: React.FormEvent<HTMLInputElement>) => {
                const target = e.target as HTMLInputElement;
                const value = target.value;
                target.value = value.replace(/[^0-9+]/g, '');
              }}
            />
            {!phoneNameValue && (
              <p className="absolute right-6 top-1/2 -translate-y-1/2 rounded text-xs text-white/70">
                (required)
              </p>
            )}
          </div>
        </div>

        {errors?.phone_number && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.phone_number.message}
          />
        )}
        <Label className="sr-only" htmlFor="username">
          Username
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="username"
          placeholder="Username"
          type="text"
          {...register('username')}
        />
        {errors?.username && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.username.message}
          />
        )}

        <Label className="sr-only" htmlFor="first-name">
          First name
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="first-name"
          placeholder="First Name"
          type="text"
          {...register('first_name')}
        />
        {errors?.first_name && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.first_name.message}
          />
        )}

        <Label className="sr-only" htmlFor="last-name">
          Last name
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="last-name"
          placeholder="Last Name"
          type="text"
          {...register('last_name')}
        />
        {errors?.last_name && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.last_name.message}
          />
        )}

        <Label className="sr-only" htmlFor="business-name">
          Business name
        </Label>
        <div className="relative mt-4">
          <Input
            className="login-autofill-text login-no-chrome-autofill-bg h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
            id="business-name"
            placeholder="Business Name"
            type="text"
            {...register('business_name')}
          />

          {!businessNameValue && (
            <p className="absolute right-6 top-1/2 -translate-y-1/2 rounded text-xs text-white/70">
              (Optional)
            </p>
          )}
        </div>
        {errors?.business_name && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.business_name.message}
          />
        )}



        <Label className="sr-only" htmlFor="state">
          Gender
        </Label>
        <Controller
          control={control}
          name="gender"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                iconClassName="fill-white"
                id="gender"
                ref={ref}
              >
                <SelectValue placeholder="Gender" />
              </SelectTrigger>
              <SelectContent>
                {['Male', 'Female'].map(gender => {
                  return (
                    <SelectItem key={gender} value={gender}>
                      {gender}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.gender && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.gender.message}
          />
        )}

        <Label className="sr-only" htmlFor="state">
          State
        </Label>
        <Controller
          control={control}
          name="state"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                iconClassName="fill-white"
                id="state"
                ref={ref}
              >
                <SelectValue placeholder="State" />
              </SelectTrigger>
              <SelectContent>
                {listOfStates.map(state => {
                  return (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.state && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.state.message}
          />
        )}

        <Label className="sr-only" htmlFor="lga">
          L.G.A
        </Label>
        <Controller
          control={control}
          name="lga"
          render={({ field: { onChange, value, ref } }) => (
            <Select value={value} onValueChange={onChange}>
              <SelectTrigger
                className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg !bg-white/30  px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A] data-[placeholder]:text-white/70"
                disabled={!lgasInSelectedState}
                iconClassName="fill-white"
                id="lga"
                ref={ref}
              >
                <SelectValue placeholder="L.G.A" />
              </SelectTrigger>
              <SelectContent>
                {lgasInSelectedState?.map(lga => {
                  return (
                    <SelectItem key={lga} value={lga}>
                      {lga}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          )}
        />
        {errors?.lga && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.lga.message}
          />
        )}

        <Label className="sr-only" htmlFor="nearest-landmark">
          Nearest Landmark
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="nearest-landmark"
          placeholder="Nearest Landmark"
          type="text"
          {...register('nearest_landmark')}
        />
        {errors?.nearest_landmark && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.nearest_landmark.message}
          />
        )}

        <Label className="sr-only" htmlFor="street">
          Street
        </Label>
        <Input
          className="login-autofill-text login-no-chrome-autofill-bg mt-4 h-auto rounded-lg  !bg-white/30 px-6 py-3.5 text-base font-medium text-white placeholder:text-white/70 focus:!bg-white/30 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-[#403C3A]"
          id="street"
          placeholder="Street"
          type="text"
          {...register('street')}
        />
        {errors?.street && (
          <FormError
            className="bg-red-900/40 text-white"
            errorMessage={errors.street.message}
          />
        )}

        <Button
          className="my-6 block w-full rounded-[.5625rem] py-[.9375rem] text-base leading-[normal]"
          disabled={isRegisterUserDetailsLoading}
          type="submit"
          variant="white"
        >
          <span className="flex w-full items-center justify-between">
            <span />

            <span>{isRegisterUserDetailsLoading ? 'Loading' : 'Next'}</span>

            <svg
              fill="none"
              height={20}
              viewBox="0 0 25 20"
              width={25}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133-3.433 3.232a.833.833 0 0 1-1.248-1.1l.07-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106-3.433-3.233a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
              />
              <path
                clipRule="evenodd"
                d="M11.522 7.643a3.334 3.334 0 0 1 .126 4.581l-.126.133L8.09 15.59a.833.833 0 0 1-1.247-1.1l.069-.078 3.433-3.232a1.667 1.667 0 0 0 .097-2.251l-.097-.106L6.91 5.589a.833.833 0 0 1 1.1-1.248l.078.07 3.433 3.232Z"
                fill="#032180"
                fillRule="evenodd"
                opacity={0.3}
              />
            </svg>
          </span>
        </Button>

        <LinkButton
          className="mt-6 flex w-full flex-wrap items-center gap-2 rounded-[.5625rem] border-white/30 py-3 leading-[normal] text-white"
          href="/login"
          type="button"
          variant="outlined"
        >
          <span className="text-xxs font-normal md:text-xs">
            Have an account?
          </span>
          <span className="text-sm md:text-base">Login</span>
        </LinkButton>
      </form>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
            size="lg"
            type="button"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </>
  );
}
