'use client';
import React from 'react';
import { Button } from '@/components/core';
import { X, AlertTriangle, Coins } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { convertNumberToNaira } from '@/utils/currency';
import { addCommasToNumber } from '@/utils/numbers';

interface TokenInsufficientModalProps {
  isOpen: boolean;
  onClose: () => void;
  companyId: string;
  companyName: string;
  currentBalance?: number;
  module?: string;
}

const TokenInsufficientModal: React.FC<TokenInsufficientModalProps> = ({
  isOpen,
  onClose,
  companyId,
  companyName,
  currentBalance = 0,
  module
}) => {
  const router = useRouter();

  if (!isOpen) return null;

  const handlePurchaseTokens = () => {
    router.push(`/subscription?companyId=${companyId}&companyName=${companyName}`);
    onClose();
  };

  const suggestedTokens = [100, 250, 500, 1000];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-full">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Insufficient Tokens</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Current Balance */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Coins className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">Current Balance</span>
              </div>
              <span className="text-lg font-bold text-[#032282]">
                {addCommasToNumber(currentBalance)} tokens
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Value: {convertNumberToNaira(currentBalance * 10)}
            </p>
          </div>

          {/* Message */}
          <div className="text-center mb-6">
            <p className="text-gray-700 mb-2">
              You need tokens to access <strong>{module}</strong> features for <strong>{companyName}</strong>.
            </p>
            <p className="text-sm text-gray-600">
              Purchase tokens to continue using our services.
            </p>
          </div>

          {/* Quick Purchase Options */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-gray-800 mb-3">Quick Purchase Options</h3>
            <div className="grid grid-cols-2 gap-2">
              {suggestedTokens.map((tokens) => (
                <div 
                  key={tokens}
                  className="bg-white border border-gray-200 rounded-lg p-3 text-center hover:border-[#032282] hover:bg-[#032282] hover:bg-opacity-5 transition-all cursor-pointer"
                  onClick={handlePurchaseTokens}
                >
                  <div className="text-sm font-semibold text-[#032282]">
                    {addCommasToNumber(tokens)} tokens
                  </div>
                  <div className="text-xs text-gray-600">
                    {convertNumberToNaira(tokens * 10)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Token Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="text-sm font-semibold text-blue-800 mb-2">How Tokens Work</h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• 1 Token = ₦10</li>
              <li>• Tokens never expire</li>
              <li>• Use tokens across all modules</li>
              <li>• Purchase any amount you need</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 border-t border-gray-200">
          <Button
            variant="outlined"
            onClick={onClose}
            className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={handlePurchaseTokens}
            className="flex-1 bg-[#032282] hover:bg-[#032282]/90 text-white"
          >
            Purchase Tokens
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TokenInsufficientModal;
