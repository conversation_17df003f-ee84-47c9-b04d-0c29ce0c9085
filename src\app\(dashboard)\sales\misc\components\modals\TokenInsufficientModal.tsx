'use client';
import React, { useState } from 'react';
import { Button } from '@/components/core';
import { X, AlertTriangle, Coins, Eye, CreditCard } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { convertNumberToNaira } from '@/utils/currency';
import { addCommasToNumber } from '@/utils/numbers';
import { useCreateInvoice } from '@/app/(dashboard)/subscription/misc/api/createSubscribe';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { AxiosError } from 'axios';

interface TokenInsufficientModalProps {
  isOpen: boolean;
  onClose: () => void;
  companyId: string;
  companyName: string;
  currentBalance?: number;
  module?: string;
  onProceedAnyway?: () => void;
}

const TokenInsufficientModal: React.FC<TokenInsufficientModalProps> = ({
  isOpen,
  onClose,
  companyId,
  companyName,
  currentBalance = 0,
  module,
  onProceedAnyway
}) => {
  const router = useRouter();
  const [_selectedTokens, _setSelectedTokens] = useState<number>(100);
  const [isCreatingInvoice, setIsCreatingInvoice] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const { mutate: createInvoice } = useCreateInvoice();

  if (!isOpen) return null;

  const handlePurchaseTokens = () => {
    router.push(`/subscription?companyId=${companyId}&companyName=${companyName}`);
    onClose();
  };

  const handleCreateInvoice = (tokens: number) => {
    setIsCreatingInvoice(true);
    setErrorMessage('');

    // Get module ID based on module name
    const getModuleId = (moduleName?: string) => {
      switch (moduleName?.toLowerCase()) {
        case 'sales': return 4;
        case 'stock':
        case 'inventory': return 1;
        case 'hr': return 2;
        case 'spend management': return 3;
        default: return 4; // Default to sales
      }
    };

    const requestData = {
      company_id: companyId,
      subscription_modules: [
        {
          module_id: getModuleId(module),
          plan: 12 // Default plan ID
        }
      ],
      is_token: true,
      token_units: tokens
    };

    createInvoice(requestData, {
      onSuccess: (data) => {
        setIsCreatingInvoice(false);
        if (data?.payment_link) {
          window.open(data.payment_link, '_blank');
          onClose();
        }
      },
      onError: (error: unknown) => {
        setIsCreatingInvoice(false);
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        setErrorMessage(errorMessage);
      }
    });
  };

  const handleProceedAnyway = () => {
    if (onProceedAnyway) {
      onProceedAnyway();
      onClose();
    }
  };

  const suggestedTokens = [100, 250, 500, 1000];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-100 rounded-full">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900">Insufficient Tokens</h2>
          </div>
          <button
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            onClick={onClose}
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Current Balance */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Coins className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-600">Current Balance</span>
              </div>
              <span className="text-lg font-bold text-[#032282]">
                {addCommasToNumber(currentBalance)} tokens
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Value: {convertNumberToNaira(currentBalance * 10)}
            </p>
          </div>

          {/* Message */}
          <div className="text-center mb-6">
            <p className="text-gray-700 mb-2">
              You need tokens to access <strong>{module}</strong> features for <strong>{companyName}</strong>.
            </p>
            <p className="text-sm text-gray-600">
              Purchase tokens to continue using our services.
            </p>
          </div>

          {/* Error Message */}
          {errorMessage && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">{errorMessage}</p>
            </div>
          )}

          {/* Quick Purchase Options */}
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-gray-800 mb-3">Quick Purchase & Pay Options</h3>
            <div className="grid grid-cols-2 gap-2">
              {suggestedTokens.map((tokens) => (
                <div
                  className="bg-white border border-gray-200 rounded-lg p-3 text-center hover:border-[#032282] hover:bg-[#032282] hover:bg-opacity-5 transition-all cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                  key={tokens}
                  onClick={() => !isCreatingInvoice && handleCreateInvoice(tokens)}
                >
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <CreditCard className="h-3 w-3 text-[#032282]" />
                    <div className="text-sm font-semibold text-[#032282]">
                      {addCommasToNumber(tokens)} tokens
                    </div>
                  </div>
                  <div className="text-xs text-gray-600">
                    {convertNumberToNaira(tokens * 10)}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Pay Now
                  </div>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">
              Click any option to create invoice and pay instantly
            </p>
          </div>

          {/* Token Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="text-sm font-semibold text-blue-800 mb-2">How Tokens Work</h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• 1 Token = ₦10</li>
              <li>• Tokens never expire</li>
              <li>• Use tokens across all modules</li>
              <li>• Purchase any amount you need</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="flex flex-col gap-3 p-6 border-t border-gray-200">
          {/* Primary Actions */}
          <div className="flex gap-3">
            <Button
              className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
              disabled={isCreatingInvoice}
              variant="outlined"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              className="flex-1 bg-[#032282] hover:bg-[#032282]/90 text-white"
              disabled={isCreatingInvoice}
              onClick={handlePurchaseTokens}
            >
              {isCreatingInvoice ? 'Creating Invoice...' : 'Go to Subscription'}
            </Button>
          </div>

          {/* Secondary Action */}
          {onProceedAnyway && (
            <div className="border-t border-gray-200 pt-3">
              <Button
                className="w-full border-blue-300 text-blue-700 hover:bg-blue-50 flex items-center justify-center gap-2"
                disabled={isCreatingInvoice}
                variant="outlined"
                onClick={handleProceedAnyway}
              >
                <Eye className="h-4 w-4" />
                Proceed to View Details (Limited Access)
              </Button>
              <p className="text-xs text-gray-500 text-center mt-2">
                View-only access without token consumption
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TokenInsufficientModal;
