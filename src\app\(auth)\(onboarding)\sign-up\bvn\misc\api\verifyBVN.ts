import { useMutation } from '@tanstack/react-query';

// import { authAxios } from '@/lib/axios';
import { authAxios } from '@/lib/axios';

export interface VerifyBVNDTO {
  bvn_number: string;
  phone_number: string;
  otp_type: string;
  onboarding_status?: string;
}

export interface VerifyBVNResponse {
  status: string;
  message: string;
}

const verifyBVN = async (verifyBVNDTO: VerifyBVNDTO) => {
  const response = await authAxios.post('/kyc/user/verify_bvn/', verifyBVNDTO);
  return response.data as VerifyBVNResponse;
};

export const useVerifyBVN = () => {
  return useMutation({
    mutationFn: verifyBVN,
  });
};
