'use client'

import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { LoaderModal } from '@/components/core';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { ViewBranchesDialog } from '../../../misc/components';

const Page = () => {
  const searchParams = useSearchParams()
  const companyId = searchParams.get('companyId') || '';
  const [loading, setLoading] = useState(false)

  const router = useRouter()

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  const isLoading = isCompanyLoading || isDefaultLoading || loading;

  useEffect(() => {
    setLoading(true)
    if (salesUserDetails) {
      if (!companyId || (salesUserDetails?.data.company_id && (salesUserDetails?.data.company_id === companyId) && salesUserDetails?.data.branch_id)) {
        return router.replace(`/instant-web/manage-tables?companyId=${salesUserDetails?.data.company_id}&companyName=${salesUserDetails?.data.company}&branchId=${salesUserDetails?.data.branch_id}&branchName=${salesUserDetails?.data.branch}`);
      }
    }
    setLoading(false)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])

  return (
    <>
      <LoaderModal isOpen={isLoading} />
      {!isLoading && <ViewBranchesDialog company={companyId}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={companies?.results} link={'/instant-web/add-product'} />}
    </>
  );
};

export default Page;
