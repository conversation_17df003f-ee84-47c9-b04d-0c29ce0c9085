'use client';
import React, { useEffect } from 'react';
import { useModules } from '../api/getModules';
import { Plan, SubScriptionModulePropTypes, useSubscriptionPlan } from '../api/getSubscriptionPlan';
import { Button, Checkbox, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core';
import PayboxModuleIcon from '@/components/icons/PayboxModuleIcon';
import { SmallSpinner, Spinner } from '@/icons/core';
import { formatDuration } from '../util';
import PriorityModuleCard from './PriorityModuleCard';

interface EnhancedSubscriptionModulesProps {
    setSelectedModules: React.Dispatch<React.SetStateAction<SubScriptionModulePropTypes[]>>;
    setIsAnySelected: React.Dispatch<React.SetStateAction<boolean>>;
    setSelectedPlans: React.Dispatch<React.SetStateAction<Map<number, Plan | null>>>;
    selectedModules: SubScriptionModulePropTypes[];
    selectedPlans: Map<number, Plan | null>;
    setShowSummaryModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const EnhancedSubscriptionModules: React.FC<EnhancedSubscriptionModulesProps> = ({
    setIsAnySelected,
    setSelectedModules,
    setSelectedPlans,
    selectedPlans,
    selectedModules,
    setShowSummaryModal
}) => {
    const { data: modulesList, isLoading } = useModules();
    const { data: subscriptionPlanList, isLoading: SubscriptionPlanLoading } = useSubscriptionPlan();

    useEffect(() => {
        if (modulesList?.modules) {
            setSelectedModules([]);
        }

        if (subscriptionPlanList && subscriptionPlanList?.plans?.length > 0) {
            const defaultPlan = subscriptionPlanList.plans[0];
            const initialSelectedPlans = new Map();
            modulesList?.modules?.forEach((module) => {
                initialSelectedPlans.set(module.id, defaultPlan);
            });
            setSelectedPlans(initialSelectedPlans);
        }
    }, [subscriptionPlanList, modulesList]);

    const handleModuleSelection = (module: SubScriptionModulePropTypes) => {
        const updatedSelection = selectedModules.some((item) => item.id === module.id)
            ? selectedModules.filter((item) => item.id !== module.id)
            : [...selectedModules, { ...module, selectedPlan: selectedPlans.get(module.id) }];

        setSelectedModules(updatedSelection);
        setIsAnySelected(updatedSelection.length > 0);
    };

    const handlePlanChange = (moduleId: number, planId: number) => {
        const plan = subscriptionPlanList?.plans?.find((plan) => plan.id === planId);
        setSelectedPlans((prevPlans) => {
            const newSelectedPlans = new Map(prevPlans);
            newSelectedPlans.set(moduleId, plan || null);

            setSelectedModules((prevModules) => {
                return prevModules.map((module) => {
                    if (module.id === moduleId) {
                        return { ...module, selectedPlan: plan || null };
                    }
                    return module;
                });
            });

            return newSelectedPlans;
        });
    };

    const handleSubscribeModule = (module: SubScriptionModulePropTypes) => {
        if (!selectedModules.some((item) => item.id === module.id)) {
            setSelectedModules((prevModules) => [
                ...prevModules,
                { ...module, selectedPlan: selectedPlans.get(module.id) },
            ]);
        }
        setShowSummaryModal(true);
    };

    // Determine priority modules (Stock & Inventory, Sales)
    const isPriorityModule = (module: SubScriptionModulePropTypes) => {
        const name = module.name.toLowerCase();
        return name.includes('stock') || name.includes('inventory') || name.includes('sales');
    };

    // Determine promotional modules (HR, Spend Management)
    const isPromotionalModule = (module: SubScriptionModulePropTypes) => {
        const name = module.name.toLowerCase();
        return name.includes('hr') || name.includes('spend');
    };

    // Sort modules: priority first, then others
    const sortedModules = modulesList?.modules?.sort((a, b) => {
        const aPriority = isPriorityModule(a);
        const bPriority = isPriorityModule(b);
        if (aPriority && !bPriority) return -1;
        if (!aPriority && bPriority) return 1;
        return 0;
    });

    if (isLoading) {
        return (
            <div className="flex justify-center items-center py-16">
                <div className="text-center">
                    <Spinner color="blue" />
                    <p className="mt-4 text-gray-600">Loading modules...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="mt-12">
            {/* Section Header */}
            <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                    Choose Your Business Modules
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                    Select the modules that best fit your business needs. 
                    Priority modules are recommended for maximum business growth.
                </p>
            </div>

            {/* Priority Modules Section */}
            <div className="mb-16">
                <div className="flex items-center justify-center mb-8">
                    <div className="flex items-center gap-3">
                        <span className="text-2xl">⭐</span>
                        <h3 className="text-2xl font-bold text-blue-700">Priority Modules</h3>
                        <span className="text-2xl">⭐</span>
                    </div>
                </div>
                
                <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
                    {sortedModules?.filter(isPriorityModule).map((module) => (
                        <PriorityModuleCard
                            key={module.id}
                            module={module}
                            isSelected={selectedModules.some((item) => item.id === module.id)}
                            selectedPlan={selectedPlans.get(module.id) || null}
                            onModuleSelection={handleModuleSelection}
                            onSubscribe={handleSubscribeModule}
                            isPriority={true}
                            disabled={SubscriptionPlanLoading}
                        />
                    ))}
                </div>
            </div>

            {/* Other Modules Section */}
            <div>
                <div className="text-center mb-8">
                    <h3 className="text-xl font-bold text-gray-700 mb-2">Additional Modules</h3>
                    <p className="text-gray-500">Expand your capabilities with these additional features</p>
                </div>
                
                <div className="grid sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
                    {sortedModules?.filter(module => !isPriorityModule(module)).map((module) => (
                        <PriorityModuleCard
                            key={module.id}
                            module={module}
                            isSelected={selectedModules.some((item) => item.id === module.id)}
                            selectedPlan={selectedPlans.get(module.id) || null}
                            onModuleSelection={handleModuleSelection}
                            onSubscribe={handleSubscribeModule}
                            isPriority={false}
                            isPromotional={isPromotionalModule(module)}
                            disabled={SubscriptionPlanLoading}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default EnhancedSubscriptionModules;
