'use client';

import { useRouter } from 'next/navigation';
import React from 'react';

import {
  <PERSON>ton,
  <PERSON>lientOnly,
  Dialog,
  DialogBody,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  LinkButton,
  DialogClose,
  ErrorModal,
} from '@/components/core';

import { CompanyListEntity } from '../../types';
import { cn } from '@/utils/classNames';
import { XCircle, CheckCircle2, ChevronRight, Clock, CreditCard } from 'lucide-react';
import { useCreateInvoice } from '@/app/(dashboard)/subscription/misc/api/createSubscribe';
import { AxiosError } from 'axios';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { useErrorModalState } from '@/hooks';

interface SelectCompanyModalProps {
  isSelectCompanyModalOpen: boolean;
  setSelectCompanyModalState: React.Dispatch<React.SetStateAction<boolean>>;
  companyLists: CompanyListEntity[];
  url_params?: string;
}

function ModuleSubscriptionModal({ isOpen, onClose, company }: { isOpen: boolean; onClose: () => void; company: CompanyListEntity }) {
  const router = useRouter();

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const allModules = [
    ...company.active_subscriptions.flatMap(sub =>
      sub.modules.map(module => ({
        ...module,
        subscription: sub,
        plan_id: module.plan_id
      }))
    ),
    ...company.not_started_subscriptions.flatMap(sub =>
      sub.modules.map(module => ({
        ...module,
        subscription: sub,
        plan_id: module.plan_id
      }))
    )
  ];

  const hasHRModule = allModules.some(module =>
    module.module.code === 'HR' && module.is_active
  );

  const { mutate: handlePaystackInvoice, isLoading: isLoadingPaystack } = useCreateInvoice();

  const handlePaystack = (moduleSubscriptionId: number, planId: number) => {
    if (!planId) {
      openErrorModalWithMessage('No plan selected for this module');
      return;
    }

    handlePaystackInvoice(
      {
        company_id: String(company.id),
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        requestData: [
          {
            module_id: moduleSubscriptionId,
            plan: planId
          }
        ]
      },
      {
        onSuccess: (data) => {
          if (data) {
            // router.replace(data?.payment_link);
            window.open(data?.payment_link, '_blank');
          }
        },
        onError: (error: unknown) => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage);
        }
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-xl h-[650px] flex flex-col">
        <DialogHeader className="px-6 py-4">
          <DialogTitle className="text-sm font-semibold flex items-center gap-2">
            Module Subscription Details
          </DialogTitle>
          <DialogClose>Close</DialogClose>
        </DialogHeader>

        {/* Scrollable content area */}
        <div className="p-6 overflow-y-auto flex-1">
          {!hasHRModule && (
            <div className="mb-6 p-4 bg-red-50 border border-red-100 rounded-lg flex items-start gap-3">
              <XCircle className="h-5 w-5 text-red-500 mt-0.5 shrink-0" />
              <div>
                <p className="font-medium text-red-700">HR Management is not included</p>
                <p className="text-sm text-red-600 mt-1">
                  Contact your account manager to add this module to your subscription.
                </p>
              </div>
            </div>
          )}

          <h3 className="text-sm font-medium text-slate-700 mb-3">Your current subscription includes:</h3>

          <div className="space-y-3 mb-6">
            {allModules.map((module) => (
              <div
                className="p-3 border rounded-lg hover:bg-slate-50 transition-colors"
                key={module.module_subscription_id}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <CheckCircle2 className="h-5 w-5 text-emerald-500 shrink-0" />
                    <span className="font-medium">{module.module.name}</span>
                  </div>
                  <div
                    className={cn(
                      "flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium",
                      module.is_not_started && "bg-amber-100 text-amber-700",
                      module.status === "active" && !module.is_not_started && "bg-emerald-100 text-emerald-700",
                      module.status === "pending" && !module.is_not_started && "bg-amber-100 text-amber-700",
                      module.status === "expired" && !module.is_not_started && "bg-red-100 text-red-700",
                    )}
                  >
                    {module.is_not_started ? (
                      <>
                        <Clock className="h-3 w-3" />
                        <span>Pending Payment</span>
                      </>
                    ) : module.status === "active" ? (
                      <>
                        <CheckCircle2 className="h-3 w-3" />
                        <span>Active</span>
                      </>
                    ) : module.status === "pending" ? (
                      <>
                        <Clock className="h-3 w-3" />
                        <span>Pending</span>
                      </>
                    ) : (
                      <>
                        <XCircle className="h-3 w-3" />
                        <span>Expired</span>
                      </>
                    )}
                  </div>
                </div>
                {module.module.description && (
                  <p className="text-sm text-slate-500 mt-1 ml-8">{module.module.description}</p>
                )}
                {module.is_not_started && (
                  <div className="mt-3 ml-8">
                    <Button
                      className="gap-1.5"
                      disabled={isLoadingPaystack}
                      size="default"
                      variant="outlined"
                      onClick={() => handlePaystack(module.module.id, module.plan_id)}
                    >
                      <CreditCard className="h-3.5 w-3.5" />
                      Complete Payment
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Fixed button container at the bottom */}
        <div className="p-4 border-t sticky bottom-0 bg-white">
          <Button
            className="gap-1 w-full sm:w-auto"
            onClick={() => {
              router.push(`/subscription?companyId=${company.id}&companyName=${company.company_name}`)
            }}
          >
            Manage Subscription <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </DialogContent>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={errorModalMessage || 'Please check your inputs and try again.'}
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button className="grow bg-red-950 text-base" size="lg" onClick={closeErrorModal}>
            Okay
          </Button>
        </div>
      </ErrorModal>

    </Dialog>
  );
}

export function SelectCompanyDialog({
  isSelectCompanyModalOpen,
  setSelectCompanyModalState,
  companyLists,
  url_params,
}: SelectCompanyModalProps) {
  const router = useRouter();
  const [selectedCompany, setSelectedCompany] = React.useState<CompanyListEntity | null>(null);
  const [showModuleModal, setShowModuleModal] = React.useState(false);

  const handleCompanyClick = (company: CompanyListEntity) => {
    if (!company.is_subscription) {
      router.push(`/subscription?companyId=${company.id}&companyName=${company.company_name}`);
      return;
    }

    const allModules = [
      ...company.active_subscriptions.flatMap(sub =>
        sub.modules.map(module => ({
          ...module,
          subscription: sub,
          plan_id: module.plan_id
        }))
      ),
      ...company.not_started_subscriptions.flatMap(sub =>
        sub.modules.map(module => ({
          ...module,
          subscription: sub,
          plan_id: module.plan_id
        }))
      )
    ];

    const hasHRModule = allModules.some(module =>
      module.module.code === 'HR' && module.is_active
    );

    const isHRRoute = ['employee-view', 'payroll-settings', 'create-employees', 'view-employees', 'time-attendance', 'performance-management'].includes(url_params || '');

    if (isHRRoute && !hasHRModule) {
      setSelectedCompany(company);
      setShowModuleModal(true);
      return;
    }

    let url = '';
    if (url_params === 'employee-view') {
      url = `/payroll/employee-view/${company.id}/${company.company_name}/salary-dashboard`;
    } else if (url_params === 'payroll-settings') {
      url = `/payroll/payroll-settings/${company.id}/${company.company_name}/employer_detail`;
    } else if (url_params === 'create-employees') {
      url = `/payroll/employee-invite/${company.id}/${company.company_name}`;
    } else if (url_params === 'view-employees') {
      url = `/payroll/payroll-dash/${company.id}/employees/${company.company_name}`;
    } else if (url_params === 'time-attendance') {
      url = `/payroll/time-and-attendance/${company.id}/activities-dashboard/${company.company_name}`;
    } else if (url_params === 'performance-management') {
      url = `/performance-management?company_id=${company.id}&company_name=${company.company_name}`;
    }

    if (url) {
      router.push(url);
    }
  };

  return (
    <ClientOnly>
      <Dialog
        open={isSelectCompanyModalOpen}
        onOpenChange={setSelectCompanyModalState}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-base font-semibold">
              Select company
            </DialogTitle>
            <Button
              className="ml-auto bg-[#2D4696] px-6 py-2"
              onClick={() => {
                router.back();
              }}
            >
              Close
            </Button>
          </DialogHeader>

          <DialogBody>
            <DialogDescription className="mb-4 text-light-text">
              Select a company whose details you want to view
            </DialogDescription>

            <ol className="flex flex-col gap-4 h-[400px] overflow-y-auto">
              {companyLists.map((company, index) => (
                <li className="flex items-center gap-2 text-sm" key={company.id}>
                  <span
                    className="inline-flex h-[1.8125rem] w-[1.8125rem] shrink-0 items-center justify-center rounded-full bg-main-bg text-main-solid"
                    aria-hidden
                  >
                    {index + 1}
                  </span>

                  <div className="min-w-0 grow bg-main-bg px-6 py-3">
                    <div className="flex items-center justify-between">
                      <span className="inline-flex items-left flex-col gap-1.5 truncate">
                        <span className="text-xs">
                          {company.is_subscription ? (
                            <span className="text-green-600">Subscribed</span>
                          ) : (
                            <span className="text-red-600">Not Subscribed</span>
                          )}
                        </span>
                        <span className="text-left text-[#566AAA] text-xs">
                          Company name:
                        </span>
                        <span className="truncate text-left text-sm text-main-solid">
                          {company.company_name}
                        </span>
                      </span>

                      <div className="mt-2">
                        {company.is_subscription ? (
                          <Button
                            className="text-xxs"
                            variant="light"
                            onClick={() => handleCompanyClick(company)}
                          >
                            <span className="text-main-solid">View details</span>
                          </Button>
                        ) : (
                          <LinkButton
                            className="text-xxs"
                            href={`/subscription?companyId=${company.id}&companyName=${company.company_name}`}
                            variant="light"
                          >
                            <span className="text-main-solid">Subscribe</span>
                          </LinkButton>
                        )}
                      </div>
                    </div>

                  </div>
                </li>
              ))}
            </ol>
            <div className="">
              <div className="my-6 flex w-full flex-row items-center justify-between gap-3">
                <svg
                  fill="none"
                  height={1}
                  viewBox="0 0 170 1"
                  width={170}
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 0.85L170 0.85"
                    stroke="#5F7DDA"
                    strokeDasharray="7.6 7.6"
                    strokeWidth={0.3}
                  />
                </svg>
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#F5F8FF]">
                  <p className="text-xs text-light-text">OR</p>
                </div>
                <svg
                  fill="none"
                  height={1}
                  viewBox="0 0 170 1"
                  width={170}
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 0.85L170 0.85"
                    stroke="#5F7DDA"
                    strokeDasharray="7.6 7.6"
                    strokeWidth={0.3}
                  />
                </svg>
              </div>
              <LinkButton
                className="flex py-3 text-sm"
                href="/payroll/create-company" size="fullWidth">
                <span>Create company</span>
              </LinkButton>
            </div>
          </DialogBody>
        </DialogContent>
      </Dialog>

      {selectedCompany && (
        <ModuleSubscriptionModal
          company={selectedCompany}
          isOpen={showModuleModal}
          onClose={() => {
            setShowModuleModal(false);
            setSelectedCompany(null);
          }}
        />
      )}
    </ClientOnly>
  );
}
