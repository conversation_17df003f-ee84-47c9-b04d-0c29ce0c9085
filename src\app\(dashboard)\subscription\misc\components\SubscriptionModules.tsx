'use client';
import React, { useEffect } from 'react';
import { useModules } from '../api/getModules';
import { Plan, SubScriptionModulePropTypes, useSubscriptionPlan } from '../api/getSubscriptionPlan';
import { Button, Checkbox, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core';
import PayboxModuleIcon from '@/components/icons/PayboxModuleIcon';
import { SmallSpinner, Spinner } from '@/icons/core';
import { formatDuration } from '../util'; // Assuming this is a utility function for formatting durations

interface prop {
    setSelectedModules: React.Dispatch<React.SetStateAction<SubScriptionModulePropTypes[]>>;
    setIsAnySelected: React.Dispatch<React.SetStateAction<boolean>>;
    setSelectedPlans: React.Dispatch<React.SetStateAction<Map<number, Plan | null>>>;
    selectedModules: SubScriptionModulePropTypes[];
    selectedPlans: Map<number, Plan | null>;
    setShowSummaryModal: React.Dispatch<React.SetStateAction<boolean>>;

}

const SubscriptionModules = ({ setIsAnySelected, setSelectedModules, setSelectedPlans, selectedPlans, selectedModules, setShowSummaryModal }: prop) => {
    const { data: modulesList, isLoading } = useModules();
    const { data: subscriptionPlanList, isLoading: SubscriptionPlanLoading } = useSubscriptionPlan();

    useEffect(() => {
        // Reset selectedModules on load so that no modules are pre-selected
        if (modulesList?.modules) {
            setSelectedModules([]); // No modules should be selected initially
        }

        // Set default selected plan as the first one in the list for each module
        if (subscriptionPlanList && subscriptionPlanList?.plans?.length > 0) {
            const defaultPlan = subscriptionPlanList.plans[0];

            // Initialize selected plans with the default plan for each module
            const initialSelectedPlans = new Map();
            modulesList?.modules?.forEach((module) => {
                initialSelectedPlans.set(module.id, defaultPlan);
            });
            setSelectedPlans(initialSelectedPlans);
        }
    }, [subscriptionPlanList, modulesList]); // Depend on both subscriptionPlanList and modulesList

    const handleModuleSelection = (module: SubScriptionModulePropTypes) => {
        // Toggle module selection via checkbox, but no impact on subscribe button
        const updatedSelection = selectedModules.some((item) => item.id === module.id)
            ? selectedModules.filter((item) => item.id !== module.id)
            : [...selectedModules, { ...module, selectedPlan: selectedPlans.get(module.id) }];

        setSelectedModules(updatedSelection);
        setIsAnySelected(updatedSelection.length > 0); // Enable/Disable global Subscribe button
    };


    // select duration dropdown
    const handlePlanChange = (moduleId: number, planId: number) => {
        const plan = subscriptionPlanList?.plans?.find((plan) => plan.id === planId);
        setSelectedPlans((prevPlans) => {
            // Update the plan in the selectedPlans map
            const newSelectedPlans = new Map(prevPlans);
            newSelectedPlans.set(moduleId, plan || null);

            // Update the selected module with the new plan
            setSelectedModules((prevModules) => {
                return prevModules.map((module) => {
                    if (module.id === moduleId) {
                        return { ...module, selectedPlan: plan || null }; // Update the module's selectedPlan
                    }
                    return module;
                });
            });

            return newSelectedPlans;
        });
    };


    const handleSubscribeModule = (module: SubScriptionModulePropTypes) => {
        // Only add the module to selectedModules for subscription, without toggling checkbox state
        if (!selectedModules.some((item) => item.id === module.id)) {
            setSelectedModules((prevModules) => [
                ...prevModules,
                { ...module, selectedPlan: selectedPlans.get(module.id) },
            ]);
        }
    };

    return (
        <div className="mt-6">
            {isLoading ? (
                <div className="flex justify-center items-center py-8">
                    <Spinner color="blue" />
                </div>
            ) : (
                <div className="grid sm:grid-cols-2  xl:grid-cols-3 2xl:grid-cols-4 gap-8">
                    {modulesList?.modules?.map((module) => (
                        <div className="border-[0.3px] border-[#032180] border-opacity-75 p-4 rounded-lg" key={module?.id}>
                            <div className="flex items-center gap-2">
                                <Checkbox
                                    checked={selectedModules.some((item) => item.id === module.id)} // Checkbox reflects module selection
                                    className="w-4 h-4 sm:w-[1.0625rem] sm:h-[1.0625rem] border border-[rgba(3,33,128,1.5)] rounded-[0.1rem]"
                                    id={module?.id.toString()}
                                    onCheckedChange={() => handleModuleSelection(module)} // Manage selection state on checkbox change
                                />
                                <label className="font-semibold text-base sm:text-lg cursor-pointer" htmlFor={module?.id.toString()}>
                                    <p className="text-[#032180] font-semibold text-sm lg:text-lg leading-[1.8756rem]">{module?.name}</p>
                                </label>
                            </div>
                            <div className="mt-6">
                                <p className="text-[#333333CC]/80 font-semibold text-[1rem] leading-[0.945rem]">Access to:</p>
                                <div className="mt-3">
                                    {module?.description && (
                                        <div className="mt-4">
                                            {module.description?.split(',')?.map((item, idx) => (
                                                <div className="flex items-start gap-2 mt-3" key={idx}>
                                                    <span className="mt-[0.28rem]">
                                                        <PayboxModuleIcon />
                                                    </span>
                                                    <span className="text-sm font-medium">{item?.trim()}</span>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                {!SubscriptionPlanLoading && <div className="mt-6">
                                    <p className="font-semibold text-sm sm:text-base leading-[1.3638rem]">
                                        Amount -{' '}
                                        <span className="text-[#032180] font-extrabold lg:text-[1.0625rem] sm:text-[0.5rem] md:text-[0.7rem] leading-[2.046rem]">
                                            {selectedPlans.get(module.id)?.price || 'Select a plan'}/<span className='text-sm'>
                                                {formatDuration(Number(selectedPlans.get(module.id)?.duration_months) ?? 0)}
                                            </span>
                                        </span>
                                    </p>
                                </div>}
                            </div>

                            {/* Each module has its own Subscribe button */}
                            {SubscriptionPlanLoading ? <div className='flex justify-center items-center py-2'><SmallSpinner color='blue' /></div> : <div className="mt-4 w-full flex justify-between items-center gap-3 max-w-[250px]">
                                <Select value={selectedPlans.get(module.id)?.id?.toString() || ''} onValueChange={(planId) => handlePlanChange(module.id, Number(planId))}>
                                    <SelectTrigger className="truncate border h-8 text-sm" id="delivery_date">
                                        <SelectValue placeholder="Select a Plan" />
                                    </SelectTrigger>
                                    <SelectContent className="z-[99999]">
                                        {subscriptionPlanList?.plans?.map((sub) =>
                                            sub.is_active ? (
                                                <SelectItem key={sub?.id} value={String(sub?.id)}>
                                                    {formatDuration(Number(sub?.duration_months ?? 0))}
                                                </SelectItem>
                                            ) : null
                                        )}
                                    </SelectContent>
                                </Select>

                                <Button
                                    className='w-full h-8'
                                    disabled={selectedModules?.length > 0} // Disable if the module is selected
                                    onClick={() => {
                                        handleSubscribeModule(module);
                                        setShowSummaryModal(true);
                                    }} // Handle module subscription without affecting checkbox state
                                >
                                    Subscribe
                                </Button>
                            </div>}
                        </div>
                    ))}
                </div>
            )}

            {/* Global Subscribe button */}
        </div>
    );
};

export default SubscriptionModules;
