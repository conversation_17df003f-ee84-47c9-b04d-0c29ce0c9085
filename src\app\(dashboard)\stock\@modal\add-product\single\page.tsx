'use client'

import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';


import { CreateProductModal } from '../../../misc/components/modals';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';
const AddProduct = ({
  searchParams,
}: {
  searchParams: {
    category: string;
  };
}) => {
  const categoryId = searchParams.category;

  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies } = companiesResponse as Companies;

  //   const getAllBranchesResponse = await stocksFetchClient<BranchesResponse>(
  //     'stock/branches/',
  //     {
  //       headers: { Authorization: `Bearer ${access}` },
  //     }
  //   );

  //   if (getAllBranchesResponse === 'unauthorized') {
  //     redirect(`/login?callbackUrl=${CALLBACK_URL}`);
  //   }

  //   const allBranches = getAllBranchesResponse?.data?.branches;

  return (
    <>

      <CreateProductModal categoryId={categoryId}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies} />
    </>
  );
};

export default AddProduct;
