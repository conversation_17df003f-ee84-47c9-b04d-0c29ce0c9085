import { CompanyListEntity } from "@/app/(dashboard)/payroll/misc/types";

// USER DEFAULTS COMPANY TYPE
export interface UserDefaultsData {
  status: string;
  status_code: number;
  data: UserDefaultsDataEntity;
  errors: null;
}

export interface UserDefaultsDataEntity {
  status: boolean;
  company: string;
  company_id: string;
  branch: null;
  branch_id: null;
  is_subscription: boolean;
  subscription_info: []
}
export interface Companies {
  count: number;
  next?: number;
  previous?: number;
  total_number_of_teams: number;
  total_number_of_members: number;
  requisition_count: number;
  approved_requisition_count: number;
  pending_requisition_count: number;
  results: CompanyListEntity[];
}
export interface CompaniesResultsEntity {
  id: string;
  overall_running_budget: number;
  company_name: string;
  teams: CompaniesTeamsEntity[];
  created_at: string;
  allocated_amount: number;
  wallet: Wallet;
  spend_mgmt_account_details: CompanyAccountDetails;

  account_type: string;

  payroll_account_details: CompanyAccountDetails;
  no_of_teams: number;
  total_req: number;
  approved_req: number;
  pending_req: number;
  present_month_allocated_amount: number;
  total_current_month_expenses: number | null;
  no_of_members: number;
  budget: CompanyBudgetEntity[];
  budgeted_categories?: null[];
  default?: boolean;
  is_subscription: boolean
  subscription_info: []
}
export interface CompaniesTeamsEntity {
  id: string;
  team_name: string;
  team_type: string;
  company_name: string;
  company_id: string;
  branch_name?: string;
  members?: CompaniesTeamMembersEntity[];
  requisitions: number;
  created_at: string;
  budget?: CompanyTeamBudgetEntity;
  no_of_members: number;
  approved_req: number;
  pending_req: number;
}
export interface CompaniesTeamMembersEntity {
  id: string;
  req_no: number;
  phone_no?: string;
  email: string;
  user_email?: string;
  first_name?: string;
  last_name?: string;
  team_name: string;
  company_name: string;
  branch_name?: null;
  user_phone_no?: string;
  role: string;
  is_registered: boolean;
  created_at: string;
  status: string;
}
export interface CompanyTeamBudgetEntity {
  id: number;
  budget_amount: number;
  budget_name: string;
  budget_type: string;
  is_active: boolean;
  start_date: string;
  end_date: string;
  created_at: string;
}
export interface Wallet {
  wallet_type: string;
  available_balance: number;
  main_balance: number;
  final_balance: number;
}
export interface CompanyAccountDetails {
  account_name: string;
  account_number: string;
  bank_name: string;
}
export interface CompanyBudgetEntity {
  id: number;
  user_id: string;
  company_id: string;
  team_id: string;
  budget_name: string;
  budget_amount: number;
  budget_type: string;
  is_active: boolean;
  is_deleted: boolean;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
}

export interface UnutilizedAllocationsEntity {
  count: number;
  next: null;
  previous: null;
  total_un_utilized: number;
  company: {
    id: string;
    total_un_utilized: number;
    company_name: string;
  };
  teams: Array<{
    team_id: string;
    team__team_name: string;
    running_balance: number;
    end_date: string;
  }>;
}
