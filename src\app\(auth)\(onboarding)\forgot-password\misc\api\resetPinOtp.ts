

import { authAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';

export interface requestResetOtpDTO {
    phone_number: string;
    otp: string;
}

const requestResetPinOtp = async (requestResetOtpDTO: requestResetOtpDTO) => {
    const response = await authAxios.post(
        '/agency/user/second_reset_confirm_login_pin/',
        requestResetOtpDTO
    );
    return response.data;
};

export const useRequestResetPinOtp = () => {
    return useMutation({
        mutationFn: requestResetPinOtp,
    });
};
