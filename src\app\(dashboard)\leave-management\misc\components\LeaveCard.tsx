import { cn } from "@/utils/classNames";
import { CalendarDays, Clock, CheckCircle2, AlertCircle, Calendar, XCircle } from "lucide-react";
import React from "react";

const iconMap = {
    "Total leave": CalendarDays,
    "Active leave": Clock,
    "Approved leave": CheckCircle2,
    "Pending request": <PERSON><PERSON><PERSON><PERSON><PERSON>,
    "Upcoming leave": Calendar,
    "Rejected leave": XCircle,
    "Total active leave": Clock,
    "Total leave request": CalendarDays,
    "Total approved request": CheckCircle2,
    "Total declined request": XCircle,
};

export interface LeaveCardProps {
    number: number;
    title: string;
    className?: string;
}

export function LeaveCard({ number, title, className }: LeaveCardProps) {
    const Icon = iconMap[title as keyof typeof iconMap];

    return (
        <article
            className={cn(
                "group w-full bg-white p-6 flex flex-row gap-4 items-center rounded-xl",
                "transition-all duration-300 ease-in-out",
                "hover:shadow-sm hover:-translate-y-1",
                "border border-[#073c9f8b]",
                className
            )}
        >
            <div className="p-3 rounded-lg bg-primary-light group-hover:bg-primary/10 transition-colors">
                {Icon && (
                    <Icon
                        className="w-6 h-6 text-primary group-hover:text-primary-dark transition-colors"
                        strokeWidth={1.5}
                    />
                )}
            </div>
            <div className="space-y-1">
                <p className="text-2xl font-semibold text-neutral-800 group-hover:text-primary transition-colors">
                    {number}
                </p>
                <p className="text-neutral-500 text-sm font-medium">{title}</p>
            </div>
        </article>
    );
}