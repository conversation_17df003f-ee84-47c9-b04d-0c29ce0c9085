"use client"
import { redirect } from 'next/navigation';
import React, { useEffect } from 'react';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';
// import { Companies, CompaniesResultsEntity, UserDefaultsData } from '@/app/(dashboard)/spend-management/misc/types';


import SelectBranchDialog from '../../misc/components/modals/SelectBranchDialog';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { LoaderModal } from '@/components/core';

const Page = ({
  searchParams,
}: {
  searchParams: {
    company: string;
  };
}) => {
  const company = searchParams.company;

  const { data: defaultData, isLoading } = useDefaultCompanyBranch()

  const { data } = useCompaniesList()
  // console.error(defaultData);

  useEffect(() => {
    if (defaultData?.data.company_id) {
      if (!company || (defaultData?.data.company_id === company && defaultData?.data.branch_id)) {
        return redirect(`/sales/dashboard?branch=${defaultData?.data.branch_id}&branchName=${defaultData?.data.branch}&company=${defaultData?.data.company_id}&companyName=${defaultData?.data.company}`);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultData?.data.company_id])


  if (isLoading) {
    return <LoaderModal isOpen={isLoading} />;
  }
  return (
    <>
      <SelectBranchDialog
        company={company}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={data?.results as CompaniesResultsEntity[]}
        link={'/sales/dashboard'}
      // subLink="sales"                 
      />
    </>
  );
};

export default Page;
