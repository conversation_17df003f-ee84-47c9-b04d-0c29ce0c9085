'use client';

import { PaginationState } from '@tanstack/react-table';
import * as React from 'react';

import {
  But<PERSON>,
  LinkButton,
  <PERSON>bs,
  <PERSON>bs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from '@/components/core';

import { Companies } from '../types';
import { CompanyListItemDropdownMenu } from './CompanyListItemDropdownMenu';
import { CreateSpendTeamModal } from './team/CreateSpendTeamModal';
import TokenBalanceCard from '@/app/(dashboard)/subscription/misc/components/TokenBalanceCard';
import { TokenBalanceInfo } from '@/app/(dashboard)/stock/misc/types';

interface CompanyListItemProps {
  id: string;
  company_name: string;
  no_of_teams: number;
  total_req: number;
  areThereBudgets: boolean;
  is_subscription?: boolean;
  token_balance_info?: TokenBalanceInfo;
}

function CompanyListItem({
  id,
  company_name,
  no_of_teams,
  total_req,
  areThereBudgets,
  is_subscription,
  token_balance_info
}: CompanyListItemProps) {

  const [showCreateSpendTeam, setShowCreateSpendTeam] = React.useState(false)
  return (
    <>
      <li
        className="w-full rounded-10 border border-card-border p-3.5 shadow-card sm:p-5 xl:px-8"
        key={id}
      >
        <div className="flex items-center justify-between">
          <p className={`text-xxs font-wix-display font-medium ${is_subscription ? "text-green-500" : "text-red-500"}`}>  {is_subscription ? "Subcribed" : "Not subscribe"}</p>
          <CompanyListItemDropdownMenu id={id} />
        </div>

        <dl className="mb-4 grid grid-cols-3 gap-3">
          <div className="flex flex-col-reverse gap-1.5">
            <dt className="text-xxs text-label-text">Company</dt>
            <dd className="text-xs text-[#373737]">{company_name}</dd>
          </div>
          <div className="flex flex-col-reverse gap-1.5">
            <dt className="text-xxs text-label-text">Teams</dt>
            <dd className="text-xs text-[#373737]">{no_of_teams}</dd>
          </div>
          <div className="flex flex-col-reverse gap-1.5">
            <dt className="text-xxs text-label-text">Requisitions</dt>
            <dd className="text-xs text-[#373737]">{total_req}</dd>
          </div>
        </dl>

        {/* Token Balance Section */}
        {token_balance_info && (
          <div className="mb-4">
            <TokenBalanceCard
              tokenBalanceInfo={token_balance_info}
              variant="compact"
            />
          </div>
        )}

        {/* CONDITION IF USERS ARE SUBSCRIBED TO COMPANY OR NOT */}

        {is_subscription ? <>
          <div className="flex flex-wrap gap-1.5 sm:grid sm:grid-cols-3 sm:gap-3">
            <Button
              className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
              // href={`/spend-management/teams/create-team?companyId=${id}&companyName=${company_name}`}
              variant="light"
              onClick={() => setShowCreateSpendTeam(true)}
            >
              <span className="text-main-solid">Create team</span>
            </Button>

            <LinkButton
              className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
              href={
                areThereBudgets
                  ? `/spend-management/budgeting/all-budgets/?companyId=${id}`
                  : '/spend-management/budgeting/budget-options'
              }
              variant="light"
            >
              <span className="text-main-solid">
                {areThereBudgets ? 'View budgets' : 'Create budget'}
              </span>
            </LinkButton>

            <LinkButton
              className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
              href={`/spend-management/companies/company/${id}/${company_name}/wallet-history`}
              variant="light"
            >
              <span className="text-main-solid">View details</span>
            </LinkButton>
          </div>
        </> : <LinkButton
          className="grow px-2 py-2.5 text-xxs sm:grow-0 sm:px-3"
          href={`/subscription?companyId=${id}&companyName=${company_name}`}
          variant="light"
        >
          <span className="text-main-solid">Subscribe</span>
        </LinkButton>}

      </li>

      {
        showCreateSpendTeam && (
          <CreateSpendTeamModal
            companyId={id as string}
            companyName={company_name}
            setShowCreateSpendTeam={setShowCreateSpendTeam}
            showCreateSpendTeam={showCreateSpendTeam}
          />
        )
      }
    </>
  );
}

export default CompanyListItem;

interface CompaniesListProps {
  companiesResponse: Companies;
  initialPageSize?: number;
}

export function CompaniesWithoutTabs({
  companiesResponse,
  initialPageSize,
}: CompaniesListProps) {


  const [{ pageIndex, pageSize }] = React.useState<PaginationState>({
    pageIndex: 1,
    pageSize: Number(initialPageSize),
  });

  const _fetchOptions = {
    pageIndex,
    pageSize,
    search: '',
  };


  return (
    <div className="md:px-0" defaultValue="companies">
      <div className="mt-1 rounded-10 bg-white p-6 lg:py-8">
        <ul className="grid grid-cols-1 justify-start gap-4 [@media(min-width:1154px)]:grid-cols-2 [@media(min-width:1614px)]:grid-cols-3">
          {companiesResponse?.results?.map(
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            ({ id, company_name, no_of_teams, total_req, budget, is_subscription, token_balance_info }) => {
              const areThereBudgets = budget && budget?.length > 0;

              return (
                <CompanyListItem
                  areThereBudgets={areThereBudgets}
                  company_name={company_name}
                  id={id}
                  is_subscription={is_subscription}
                  key={id}
                  no_of_teams={no_of_teams}
                  total_req={total_req}
                  token_balance_info={token_balance_info}
                />
              );
            }
          )}
        </ul>
      </div>
    </div>
  );
}

export function CompaniesAndCardsTabs({
  companiesResponse,
  initialPageSize,
}: CompaniesListProps) {
  const [{ pageIndex, pageSize }] = React.useState<PaginationState>({
    pageIndex: 1,
    pageSize: Number(initialPageSize),
  });

  const _fetchOptions = {
    pageIndex,
    pageSize,
    search: undefined,
  };

  // const { data: companiesResponse } = useCompanies(
  //   fetchOptions,
  //   initialCompaniesResponse
  // );

  const { results: companies, count } = companiesResponse || {};

  return (
    <Tabs className="md:px-0" defaultValue="companies">
      <TabsList className="w-full justify-start rounded-10 bg-white pb-0 pl-6 text-main-solid lg:pl-14">
        <TabsTrigger
          className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
          value="companies"
        >
          Companies
        </TabsTrigger>
        <TabsTrigger
          className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
          value="cards"
          disabled
        >
          Cards
        </TabsTrigger>
      </TabsList>

      <TabsContent
        className="mt-1 rounded-10 bg-white p-6 lg:py-8"
        value="companies"
      >
        <div className="mb-6 flex flex-wrap justify-between gap-2">
          <h2 className="flex items-center gap-2">
            <span>Companies</span>

            <span className="flex h-6 w-6 items-center justify-center rounded-full bg-main-bg text-xxs">
              {count}
            </span>

            <span className="sr-only">(in total)</span>
          </h2>

          <LinkButton
            className="px-3 py-2.5"
            href="/spend-management/companies/create-company"
            variant="light"
          >
            Create company
          </LinkButton>
        </div>

        <ul className="grid grid-cols-1 justify-start gap-4 [@media(min-width:1154px)]:grid-cols-2 [@media(min-width:1614px)]:grid-cols-3">
          {companies?.map(
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            ({ id, company_name, no_of_teams, total_req, budget, token_balance_info }) => {
              const areThereBudgets = budget && budget?.length > 0;

              return (
                <CompanyListItem
                  areThereBudgets={areThereBudgets}
                  company_name={company_name}
                  id={id}
                  key={id}
                  no_of_teams={no_of_teams}
                  total_req={total_req}
                  token_balance_info={token_balance_info}
                />
              );
            }
          )}
        </ul>
      </TabsContent>

      <TabsContent
        className="mt-1 rounded-10 bg-white p-6 lg:py-8"
        value="cards"
      >
        Content for cards goes here.
      </TabsContent>
    </Tabs>
  );
}
