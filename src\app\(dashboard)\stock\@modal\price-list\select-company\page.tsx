'use client'

import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';


import { ViewPriceListPerCompany } from '../../../misc/components';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';

const SelectCompany = () => {


  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies } = companiesResponse as Companies;

  return (
    <>
      <ViewPriceListPerCompany
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyList={companies} /*allBranches={allBranches}*/
      />
    </>
  );
};

export default SelectCompany;
