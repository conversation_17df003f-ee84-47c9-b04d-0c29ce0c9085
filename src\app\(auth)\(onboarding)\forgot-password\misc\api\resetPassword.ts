import { authAxios } from '@/lib/axios';

import { useMutation } from '@tanstack/react-query';

export interface resetPasswordDTO {
    pin1: string;
    pin2: string;
    phone_number: string;
    otp_value: string;
}

const resetPassword = async (resetPasswordDTO: resetPasswordDTO) => {
    const response = await authAxios.post(
        '/agency/user/third_reset_create_login_pin/',
        resetPasswordDTO
    );
    return response.data;
};

export const useResetPassword = () => {
    return useMutation({
        mutationFn: resetPassword,
    });
};
