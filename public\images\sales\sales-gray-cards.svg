<svg width="163" height="167" viewBox="0 0 163 167" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.1" filter="url(#filter0_d_513_19173)">
<rect width="50.9521" height="109.9" rx="2" transform="matrix(-0.883373 0.468671 -0.329523 -0.944148 135.225 30.7614)" fill="url(#paint0_linear_513_19173)" shape-rendering="crispEdges"/>
</g>
<g opacity="0.1" filter="url(#filter1_d_513_19173)">
<rect width="50.9521" height="54.1297" rx="2" transform="matrix(-0.883373 0.468671 -0.329523 -0.944148 66.8467 28.1063)" fill="url(#paint1_linear_513_19173)" shape-rendering="crispEdges"/>
</g>
<g opacity="0.1" filter="url(#filter2_d_513_19173)">
<rect width="50.9521" height="54.1297" rx="2" transform="matrix(-0.883373 0.468671 -0.329523 -0.944148 173.847 135.105)" fill="url(#paint2_linear_513_19173)" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_513_19173" x="50.54" y="-72.2834" width="88.145" height="134.208" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_513_19173"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_513_19173" result="shape"/>
</filter>
<filter id="filter1_d_513_19173" x="0.539551" y="-22.2831" width="69.7676" height="81.5521" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_513_19173"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_513_19173" result="shape"/>
</filter>
<filter id="filter2_d_513_19173" x="107.54" y="84.7161" width="69.7676" height="81.5521" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_513_19173"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_513_19173" result="shape"/>
</filter>
<linearGradient id="paint0_linear_513_19173" x1="25.476" y1="0" x2="25.476" y2="109.9" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_513_19173" x1="25.476" y1="0" x2="25.476" y2="54.1297" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_513_19173" x1="25.476" y1="0" x2="25.476" y2="54.1297" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
