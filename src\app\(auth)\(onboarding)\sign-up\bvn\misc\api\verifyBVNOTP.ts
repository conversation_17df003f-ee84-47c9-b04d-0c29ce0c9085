import { authAxios } from '@/lib/axios';
import { useMutation } from '@tanstack/react-query';

// import { authAxios } from '@/lib/axios';

export interface VerifyBvnOtpDto {
  phone_number: string;
  otp: string;
  app_name: string;
  onboarding_status?: string;
}

export interface VerifyBVNOTPResponse {
  message: string;
}

const verifyBVNOTP = async (verifyBVNDTO: VerifyBvnOtpDto) => {
  const response = await authAxios.post(
    '/kyc/user/confirm_bvn_otp/',
    verifyBVNDTO
  );
  return response.data as VerifyBVNOTPResponse;
};

export const useVerifyBVNOTP = () => {
  return useMutation({
    mutationFn: verifyBVNOTP,
  });
};
