"use client";
import React, { useEffect, useState } from 'react'
import { useSubscriptionCards } from '../../api/getSubscriptionCards';
import { usePathname, useSearchParams } from 'next/navigation';
import { <PERSON><PERSON>, LinkButton } from '@/components/core';
import CloseIcon from '@/app/(dashboard)/instant-web/misc/icons/CloseIcon';
import { cn } from '@/utils/classNames';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import WarningIcon from '../../../components/icons/WarningIcon';

const SubscriptionNotificationBanner = () => {
    const searchData = useSearchParams()
    const pathname = usePathname();
    const { data: defaultCompany } = useDefaultCompanyBranch();
    const company_id = searchData.get("companyId") ?? defaultCompany?.data?.company_id
    const companyName = searchData.get("companyName") ?? defaultCompany?.data?.company
    const { data } = useSubscriptionCards(String(company_id))

    const [showFreePlanBanner, setShowFreePlanBanner] = useState(data?.is_free_trial)
    const [hideSubcsribeBtn, setHideSubcsribeBtn] = useState(false)
    useEffect(() => {
        if (data?.is_free_trial) {
            setShowFreePlanBanner(true)
        }
    }, [data?.is_free_trial])
    // const router = useRouter();


    useEffect(() => {
        if (pathname.includes("/subscription")) {
            setHideSubcsribeBtn(true);
        } else {
            setHideSubcsribeBtn(false);
        }
    }, [pathname]);

    return (
        <div className='px-3 md:px-8 mt-2'>
            {(!data?.is_paid_subscription && showFreePlanBanner) && <div className={`${cn(`${Number(data?.free_trial_days_remaining) === Number(0) ? "bg-[#FFE7E7]" : "bg-[#FDF7EB]"}`)} w-full  rounded-[1.25rem] py-[1.4rem] flex flex-col md:flex-row justify-between items-start gap-3 md:items-center px-[2.625rem]`}>
                <div className="">
                    <p className={`${cn(`${Number(data?.free_trial_days_remaining) === Number(0) ? "text-[#EF4444]" : "text-[#EE8911]"}`)} flex items-center gap-x-2 font-bold`}><WarningIcon className='hidden md:block' color={Number(data?.free_trial_days_remaining) === Number(0) ? "#EF4444" : "#EE8911"} /> Your default company, {defaultCompany?.data?.company}, is on a free plan</p>
                    <p className={`${cn(`${Number(data?.free_trial_days_remaining) === Number(0) ? "text-[#242424]" : "text-[#AD5E00]"}`)}  text-sm`}> {Number(data?.free_trial_days_remaining) === Number(0) ? `Your free subscription for ${defaultCompany?.data?.company} has expired.` : `Your free subscription for ${defaultCompany?.data?.company} will expire in ${data?.free_trial_days_remaining ?? 0} days.`}  Click the subscribe button to upgrade.</p>
                </div>
                <div className="flex items-center gap-x-[2.625rem]">
                    {!hideSubcsribeBtn && <LinkButton className='bg-[#EE8911] text-white'
                        href={`/subscription?companyId=${company_id}&companyName=${companyName}`}>Subscribe</LinkButton>}

                    <Button className='p-0 bg-transparent' onClick={() => setShowFreePlanBanner(false)}>
                        <CloseIcon color={Number(data?.free_trial_days_remaining) === Number(0) ? "#EF4444" : "#EE8911"} height={20} width={20} />
                    </Button>
                </div>
            </div>
            }


            {(data?.is_paid_subscription && !showFreePlanBanner && data?.active_subscriptions === 0) && <div className={`${cn(`bg-[#FFE7E7]" `)} w-full  rounded-[1.25rem] py-[1.4rem] flex justify-between items-center px-[2.625rem]`}>
                <div className="">
                    <p className={`${cn(`text-[#EF4444]`)} flex items-center gap-x-2 font-bold`}><WarningIcon color={"#EF4444"} /> Your default company, {defaultCompany?.data?.company}&apos;s subscription has expired</p>
                    <p className={`${cn(`text-[#242424]`)}  text-sm`}> {Number(data?.free_trial_days_remaining) === Number(0) ? `The subscription for ${defaultCompany?.data?.company} has expired.` : `The subscription for ${defaultCompany?.data?.company} will expire in ${data?.free_trial_days_remaining ?? 0} days.`}  Click the subscribe button to upgrade.</p>
                </div>
                <div className="flex items-center gap-x-[2.625rem]">
                    {<LinkButton className='bg-[#00A37D] text-white'
                        href={`/subscription-dashboard?companyId=${company_id}&companyName=${companyName}`}>Renew</LinkButton>}

                    <Button className='p-0 bg-transparent' onClick={() => setShowFreePlanBanner(false)}>
                        <CloseIcon color={Number(data?.free_trial_days_remaining) === Number(0) ? "#EF4444" : "#00A37D"} height={20} width={20} />
                    </Button>
                </div>
            </div>}
        </div>
    )
}

export default SubscriptionNotificationBanner