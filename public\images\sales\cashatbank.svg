<svg width="178" height="154" viewBox="0 0 178 154" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect opacity="0.2" width="50.9777" height="82.7358" rx="2" transform="matrix(0.881831 -0.471566 0.331852 0.943332 63.2266 -0.839844)" fill="url(#paint0_linear_36076_5948)"/>
<g opacity="0.1" filter="url(#filter0_d_36076_5948)">
<rect width="50.9521" height="109.9" rx="2" transform="matrix(-0.883373 0.468671 -0.329523 -0.944148 135.219 32.7617)" fill="url(#paint1_linear_36076_5948)" shape-rendering="crispEdges"/>
</g>
<g opacity="0.1" filter="url(#filter1_d_36076_5948)">
<rect width="50.9521" height="54.1297" rx="2" transform="matrix(-0.883373 0.468671 -0.329523 -0.944148 66.8438 30.1055)" fill="url(#paint2_linear_36076_5948)" shape-rendering="crispEdges"/>
</g>
<g opacity="0.1" filter="url(#filter2_d_36076_5948)">
<rect width="50.9521" height="54.1297" rx="2" transform="matrix(-0.883373 0.468671 -0.329523 -0.944148 173.844 137.105)" fill="url(#paint3_linear_36076_5948)" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_36076_5948" x="50.5352" y="-70.2832" width="88.1445" height="134.207" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_36076_5948"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_36076_5948" result="shape"/>
</filter>
<filter id="filter1_d_36076_5948" x="0.535156" y="-20.2832" width="69.7695" height="81.5508" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_36076_5948"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_36076_5948" result="shape"/>
</filter>
<filter id="filter2_d_36076_5948" x="107.535" y="86.7168" width="69.7695" height="81.5508" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0117647 0 0 0 0 0.133333 0 0 0 0 0.509804 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_36076_5948"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_36076_5948" result="shape"/>
</filter>
<linearGradient id="paint0_linear_36076_5948" x1="25.4889" y1="0" x2="25.4889" y2="82.7358" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint1_linear_36076_5948" x1="25.476" y1="0" x2="25.476" y2="109.9" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.985" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint2_linear_36076_5948" x1="25.476" y1="0" x2="25.476" y2="54.1297" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.1"/>
</linearGradient>
<linearGradient id="paint3_linear_36076_5948" x1="25.476" y1="0" x2="25.476" y2="54.1297" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
