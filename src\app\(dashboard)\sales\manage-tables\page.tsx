"use client"
import { <PERSON>ton, ErrorModal, LoaderModal } from '@/components/core'

import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Spinner } from '@/icons/core'

import { useInfiniteQuery } from '@tanstack/react-query'

import { AxiosError } from 'axios'
import { formatAxiosErrorMessage } from '@/utils/errors'
import { useErrorModalState } from '@/hooks'
import useInfiniteScroll from 'react-infinite-scroll-hook'
import AddTables from './components/AddTables'
import TableList from './components/TableList'
import { managementAxios } from '@/lib/axios'
import { TableProp, TableResult } from '../../instant-web/misc/api/manage-tables/fetchTable'
import { useFetchCompanyBranch } from '../../instant-web/misc/api/qrcode/fetchBranch'
import { MoneyIcon } from '../../instant-web/misc/icons'
import DebounceInput from '../../instant-web/util/DebounceInput'
import GeneralEmptyState from '../../instant-web/misc/components/GeneralEmptyState'
import { ViewBranchesDialog } from '../../instant-web/misc/components'
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch'
import { useRouter, usePathname } from 'next/navigation';
import { useCompaniesList } from '../../instant-web/misc/api'
import SubscriptionErrorModal from '../../spend-management/misc/components/subscription/ErrorSubscriptionModal'

const ManageTables = () => {
    const [tableDetails, setTableDetails] = useState<TableResult>()
    const { isErrorModalOpen, setErrorModalState, closeErrorModal, openErrorModalWithMessage, errorModalMessage } = useErrorModalState();
    const [globalFilter, setGlobalFilter] = useState("");
    const search = useSearchParams()
    const companyId = search.get("company")
    const branch = search.get("branch")
    const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();
    const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
    const pathname = usePathname();

    const router = useRouter();
    // const companyName = search.get("companyName")
    const [showAddTables, setShowAddTables] = useState(false)
    const { data: branchList, isLoading: loadingBranchlist } = useFetchCompanyBranch(String(companyId));
    // const { data:tableList, isLoading } = useFetchTable(String(companyId))
    // const [selectedBranch, setSelectedBranch] = useState("")
    // Set the selected branch to the first branch on page load
    // useEffect(() => {
    //     if (branchList?.data?.branches && branchList.data.branches.length > 0) {
    //         const _firstBranchId = branchList.data.branches[0].id;
    //         const firstBranchName = branchList.data.branches[0].name;
    //         setSelectedBranch(firstBranchName);
    //         setTableDetails(undefined)

    //         // Navigate to the desired URL with the first branch
    //         // router.push(`/instant-web/manage-tables/?companyId=${companyId}&companyName=${companyName}&branch=${firstBranchId}`);
    //     }
    // }, [branchList, companyId, companyName, router]); // Depend on branchList, companyId, and companyName

    useEffect(() => {
        if (!companyId && salesUserDetails?.data) {
            router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [salesUserDetails?.data.company_id])

    interface queryKeyTypes {
        id: string;
        branchId: string;
        search: string;
        pageParam?: number;
        size: number;
    }
    const size = 10;

    const fetchTable = async ({ id, search, pageParam = 1 }: queryKeyTypes) => {
        let response
        if (branch && branch !== "") {

            response = await managementAxios.get(`/instant_web/tables/?company=${id}&branch=${branch}&search=${search}&page=${pageParam}&size=${size}`);
        } else {
            response = await managementAxios.get(`/instant_web/tables/?company=${id}&search=${search}&page=${pageParam}&size=${size}`);

        }
        return response?.data as TableProp

    };

    const id = companyId || '';

    const {
        data,
        error,
        fetchNextPage,
        hasNextPage,
        isFetchingNextPage,
        isLoading,
    } = useInfiniteQuery(
        ['fetch-tables', id, branch, globalFilter],
        ({ pageParam = 1 }) => fetchTable({ id, branchId: branch as string, search: globalFilter, pageParam, size }),
        {
            staleTime: 6000,
            enabled: !branch ? !!id : !!id && !!branch,
            getNextPageParam: (lastPage) => lastPage?.next ? lastPage.next : undefined,
            onError: (error: AxiosError) => {
                const errorMessage = formatAxiosErrorMessage(error);
                openErrorModalWithMessage(errorMessage);
            }
        }
    );
    const allResults = data?.pages.flatMap(page => page.results) || [];

    const dashboardItems = [
        {
            title: 'Total Tables',
            icon: <MoneyIcon />,
            value: data?.pages[0]?.total_tables || 0,
            currencyItem: false,
        },
        {
            title: 'Total scans',
            icon: <MoneyIcon />,
            value: data?.pages[0]?.total_scans || 0,
            currencyItem: false,
        },
    ];



    const nextPage = hasNextPage !== undefined;
    const [sentryRef] = useInfiniteScroll({
        loading: isFetchingNextPage || isLoading,
        hasNextPage: nextPage,
        onLoadMore: fetchNextPage,
        disabled: !!error,
        rootMargin: '0px 0px 400px 0px',
    });
    // useEffect(() => {
    //     if (allResults && allResults[0]) {
    //         setTableDetails(allResults[0]);
    //     }
    // }, []);


    return (
        <>
            <LoaderModal isOpen={isDefaultLoading || !companyId} />

            {
                (!salesUserDetails?.data.is_subscription && !isDefaultLoading) && <SubscriptionErrorModal
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    message={"Access Forbidden"}
                />
            }

            {branch ?
                <div>
                    <div className="flex justify-between flex-wrap gap-2 items-center w-full p-6">
                        <div className="flex items-center gap-2">
                            <div className="flex items-center justify-center flex-wrap gap-2">
                                {/* <div className="flex items-center justify-center gap-x-4 rounded-md bg-white p-2">
                            <p className="text-xs text-primary">{companyName ?? ""}</p>
                            <ManageWebsiteCompanySwitcher path="manage-tables" />
                        </div>
                        {loadingBranchlist ? <Spinner className='w-4 h-4' /> : <div className="flex items-center justify-center gap-x-4 border h-10 rounded-md bg-white p-2">
                            <p className="text-xs text-primary">{branch ? branch : "Branch"}</p>
                            <div className="mt-2">
                                <InstantWebBranchSwitcher
                                    branches={branchList?.data?.branches} // Pass the branch list here
                                    company={companyId as string}
                                    companyName={companyName as string}
                                    path="manage-tables"
                                    setSelectedBranch={setSelectedBranch}
                                />
                            </div>
                        </div>} */}
                            </div>




                        </div>
                        <div className="flex items-center">
                            <div className="flex items-center gap-x-2 rounded-md  px-2">
                                <DebounceInput
                                    className='border-none'
                                    value={globalFilter ?? ""}
                                    onChange={(value) => setGlobalFilter(String(value))}
                                />
                            </div>
                            <div className="">
                                <Button className='py-3' onClick={() => setShowAddTables(true)}> New Table</Button>
                            </div></div>
                    </div>

                    <div className="px-6 flex  flex-col h-full">
                        {
                            isLoading ?
                                <div className='flex justify-center items-center'><Spinner className='w-4 h-4' /></div> :
                                <>
                                    <div className="grid grid-cols-1 gap-4 py-5 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
                                        {dashboardItems?.map((item, index) => (
                                            <div className="flex items-center gap-x-4 rounded-lg bg-white p-5" key={index}>
                                                <div>{item.icon}</div>
                                                <div>
                                                    <p className="text-sm font-bold text-primary">
                                                        {item.currencyItem ? '₦' : ''}
                                                        {item.value ?? 0}
                                                    </p>
                                                    <p className="text-xs text-[#818181]">{item.title}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                    <div className='flex flex-col gap-4 bg-white flex-1'>
                                        {
                                            allResults.length > 0 ? (

                                                <TableList allResults={allResults} branchList={branchList} companyId={companyId as string} loadingBranchlist={loadingBranchlist} setTableDetails={setTableDetails} tableDetails={tableDetails} />
                                            ) :
                                                <div className='flex justify-center flex-col items-center w-full bg-white flex-1 pb-12'>
                                                    <GeneralEmptyState
                                                        body='Add a new product to your store and start selling, also share your store link to your customers to start buying and manage your inventory'
                                                        header='You do not have a Table to manage yet'
                                                    />
                                                    <div className="flex gap-x-3">
                                                        <Button variant='default' onClick={() => setShowAddTables(true)}>New Table</Button>
                                                    </div>
                                                </div>
                                        }
                                        {(isFetchingNextPage || hasNextPage) && (
                                            <div
                                                className=" flex w-full py-7 justify-center items-center"
                                                ref={sentryRef}
                                            >
                                                {isFetchingNextPage && <Spinner color='blue' />}
                                            </div>
                                        )}
                                    </div>


                                </>}

                    </div>


                    {AddTables && <AddTables
                        branchList={branchList}
                        companyId={companyId as string}
                        isOpen={showAddTables}
                        loadingBranchlist={loadingBranchlist}
                        setIsOpen={setShowAddTables}
                        tableDetails={{
                            branch: "",
                            name: "",
                            categories: [],
                            all_categories: false,
                            code_url: "",
                            company: "",
                            id: "",
                            no_of_seats: 0,
                            link: "",
                            scans: 0
                        }}
                        type="Add"
                    />}
                    <ErrorModal
                        isErrorModalOpen={isErrorModalOpen}
                        setErrorModalState={setErrorModalState}
                        subheading={errorModalMessage || 'Please check your inputs and try again.'}
                    >
                        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                            <Button
                                className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                                size="lg"
                                type="button"
                                onClick={closeErrorModal}
                            >
                                Okay
                            </Button>
                        </div>
                    </ErrorModal>
                </div>
                :
                <>
                    {!isCompanyLoading && <ViewBranchesDialog company={companyId as string}
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        companyList={companies?.results} link={'/sales/sell'} />}
                </>
            }
        </>
    )
}

export default ManageTables