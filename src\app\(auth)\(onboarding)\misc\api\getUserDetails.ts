import { authAxios } from '@/lib/axios';
import { User } from '@/types/users';

import { useQuery } from '@tanstack/react-query';

// import { UserEntities } from '../types';

export interface NoPinError {
  error: string;
  message: string;
  create_transaction_pin_link: string;
}

export const getUserDetails = async () => {
  const response = await authAxios.get(`/agency/user/get_user_details/`);
  return response.data as User;
};

export const useUserDetails = ({
  retryCount,
  initialData,
}: {
  retryCount?: number | boolean;
  initialData?: User;
}) => {
  return useQuery({
    queryKey: ['user-details'],
    queryFn: getUserDetails,
    retry: retryCount ?? true,
    initialData,
    staleTime: 0, // Ensures data is always refetched when invalidated
    cacheTime: 10 * 60 * 1000,
    refetchOnWindowFocus: true, // Allows refetching on window focus
    refetchOnMount: true, // Ensures fresh data on mount..
  });
};

// export const getUserDetails = async (): Promise<UserEntities> => {
//   const { data } = await authAxios.get('/agency/user/get_user_details/');
//   return data;
// };

// export const useUser = ({}) =>
//   useQuery('user-details', getAuthenticatedUser, { cacheTime: 1000 * 60 * 5 });x
