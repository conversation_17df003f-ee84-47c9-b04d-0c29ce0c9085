{"name": "libertypay-dashboard", "sideEffects": false, "version": "0.1.0", "private": true, "scripts": {"analyze": "cross-env ANALYZE=true next build", "dev": "next dev", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^9.0.0", "@headlessui/react": "^1.7.15", "@hookform/resolvers": "^3.1.0", "@mono.co/connect.js": "^0.2.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.0.7", "@react-pdf/renderer": "^3.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^4.29.12", "@tanstack/react-table": "^8.9.2", "@types/jspdf": "^2.0.0", "@types/node": "20.1.2", "@types/qrcode": "^1.5.5", "@types/react-dom": "18.2.4", "@types/react-easy-crop": "^2.0.0", "@types/react-image-crop": "^8.1.6", "@types/react-mentions": "^4.4.0", "@types/react-pdf": "^7.0.0", "@types/react-slick": "^0.23.13", "apexcharts": "^4.3.0", "autoprefixer": "10.4.14", "axios": "^1.4.0", "chart.js": "^4.3.0", "chartjs-plugin-datalabels": "^2.2.0", "class-variance-authority": "^0.6.0", "clsx": "^1.2.1", "cmdk": "^0.2.0", "country-state-city": "^3.2.1", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "embla-carousel-react": "^8.3.1", "eslint-config-next": "^14.2.8", "file-saver": "^2.0.5", "firebase": "^10.12.4", "framer-motion": "^10.15.0", "get-image-orientation": "^0.0.4", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "install": "^0.13.0", "jsbarcode": "^3.11.6", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.0", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "lucide-react": "^0.428.0", "moment": "^2.30.1", "multiselect-react-dropdown": "^2.0.25", "next": "^14.0.4", "nextjs-toploader": "^1.4.2", "npm": "^10.8.2", "or": "^0.2.0", "papaparse": "^5.4.1", "pdf-lib": "^1.17.1", "pdfjs-dist": "^4.10.38", "postcss": "8.4.23", "prettier": "^3.3.3", "qrcode": "^1.5.3", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-colorful": "^5.6.1", "react-csv": "^2.2.2", "react-day-picker": "^8.7.1", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.43.9", "react-hot-toast": "^2.4.1", "react-image-crop": "^11.0.6", "react-infinite-scroll-hook": "^4.1.1", "react-intersection-observer": "^9.16.0", "react-mentions": "^4.4.10", "react-minimal-pie-chart": "^8.4.0", "react-moment": "^1.1.3", "react-number-format": "^5.3.1", "react-paystack": "^4.0.3", "react-pdf": "^7.7.3", "react-phone-input-2": "^2.15.1", "react-pin-input": "^1.3.1", "react-qr-code": "^2.0.15", "react-select": "^5.8.0", "react-share": "^5.1.1", "react-slick": "^0.30.2", "react-text-transition": "^3.1.0", "react-to-print": "^2.15.1", "react-use": "^17.5.0", "react-webcam": "^7.2.0", "react-wrap-balancer": "^0.5.0", "recharts": "^2.15.4", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "styled-components": "^6.1.13", "tailwind-merge": "^1.12.0", "tailwindcss": "3.3.2", "tesseract.js": "^4.1.1", "uuid": "^9.0.0", "vaul": "^0.9.1", "xlsx": "^0.18.5", "zod": "^3.21.4", "zustand": "^4.3.8"}, "devDependencies": {"@eslint/js": "^9.9.1", "@ianvs/prettier-plugin-sort-imports": "^3.7.2", "@next/bundle-analyzer": "^13.5.6", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/papaparse": "^5.3.7", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-csv": "^1.1.9", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-check-file": "^2.8.0", "eslint-plugin-react": "^7.35.2", "eslint-plugin-tailwindcss": "^3.17.4", "globals": "^15.9.0", "prettier-plugin-tailwindcss": "^0.2.8", "raw-loader": "^4.0.2", "tailwindcss-animate": "^1.0.5", "typescript": "5.5.2", "typescript-eslint": "^8.3.0"}}