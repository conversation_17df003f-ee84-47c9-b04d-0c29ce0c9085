'use client'

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';

import { CreateCustomerDialog } from '../../misc/components/modals';
// import { CompanyListEntity } from '@/app/(dashboard)/stock/misc/types';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';

// const CALLBACK_URL = '/sales/create-company';

export default function CreateCompany() {

  const { data } = useCompaniesList()
  return <CreateCustomerDialog
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    companies={data?.results as CompaniesResultsEntity[]} />;
}