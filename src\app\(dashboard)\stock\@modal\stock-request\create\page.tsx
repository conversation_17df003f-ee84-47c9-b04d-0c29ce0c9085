'use client'

import { useSearchParams } from 'next/navigation';
import React from 'react';

import {
  MakeStockRequestModal,
  //   CreateProductModalx,
} from '../../../misc/components/modals';
import { LoaderModal } from '@/components/core';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';

const Page = () => {
  const searchParams = useSearchParams();
  const company = searchParams.get('company') || '';

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();

  return (
    <>
      <LoaderModal isOpen={isCompanyLoading} />
      <MakeStockRequestModal
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies?.results || []}
        companyId={company}
      />
    </>
  );
};

export default Page;
