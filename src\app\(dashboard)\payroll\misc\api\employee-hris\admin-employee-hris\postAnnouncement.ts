import { managementAxios } from '@/lib/axios';

import { useMutation } from '@tanstack/react-query';


interface PostAnnouncementParameters {
  dataDto: {
    announcement_type: string,
    announcement_title: string,
    announcement_body: string,
    announcement_date: string,
  };
  company_id: string;
}

const PostAnnouncement = async ({
  dataDto,
  company_id,
}: PostAnnouncementParameters) => {
  const response = await managementAxios.post(
    `/payroll/create_announcement_event/?company_uuid=${company_id}`,
    dataDto,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  return response.data;
};

export const usePostAnnouncement = () => {
  return useMutation({
    mutationFn: PostAnnouncement,
  });
};
