'use client';

import React from 'react';

import { PayrollWorkForceData, SelectDayFilter } from '../../types';
import { WorkForceChart } from './WorkForceChart';


const listOfYears = [
  { id: 1, filter: '', name: 'All time', unavailable: false },
  { id: 2, filter: 'today', name: 'Today', unavailable: false },
  { id: 3, filter: 'this_month', name: 'This month', unavailable: false },
  { id: 4, filter: 'this_week', name: 'This week', unavailable: false },
  { id: 5, filter: 'last_month', name: 'Last month', unavailable: false },
  { id: 6, filter: 'last_week', name: 'Last week', unavailable: false },
  { id: 7, filter: 'last_year', name: 'Last year', unavailable: false },
  { id: 8, filter: 'this_year', name: 'This year', unavailable: false },
];

interface PayrollHistoryChartProps {
  PayrollWorkForceChartResponse: PayrollWorkForceData;
}

export function WorkForceChartContainer({
  PayrollWorkForceChartResponse,
}: PayrollHistoryChartProps) {


  const [tripleBarChartFilter, setTripleBarChartFilter] =
    React.useState<SelectDayFilter>(listOfYears[0]);


  const dashTripleBarChartData = {
    transactionComparative: {
      labels: ['Above 15 years', '8-14 years', '4-7 years', '0-3 years',],
      values: [
        {
          label: 'Work force',
          data: [PayrollWorkForceChartResponse?.above_fifteen_years_employee, PayrollWorkForceChartResponse?.eight_to_fourteen_years_employee, PayrollWorkForceChartResponse?.four_to_seven_years_employee, PayrollWorkForceChartResponse?.zero_to_three_years_employee],
        },

      ],
    },
  };

  return (
    <>
      <WorkForceChart
        change={'up'}
        colors={['#032282',]}
        data={dashTripleBarChartData?.transactionComparative?.values}


        headLabel={'Workforce length of service distribution'}
        labelsTitle={dashTripleBarChartData?.transactionComparative?.labels}

        selectDay={tripleBarChartFilter}
        setSelectDay={setTripleBarChartFilter}

        year={listOfYears}
      />
    </>
  );
}
