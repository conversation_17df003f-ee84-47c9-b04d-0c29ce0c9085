
"use client";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ErrorModal,
    RadioGroup,
    RadioGroupItem,
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from "@/components/core";
import { Sheet, SheetClose, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/core/Sheet";
import { useCreateInvoice } from "../../api/createSubscribe";
import { useState } from "react";
import ArrowDown from "@/components/icons/ArrowDown";
import Copy from "@/components/icons/Copy";
import Image from 'next/image';
import { addCommasToNumber } from "@/utils/numbers";
import { useRouter, useSearchParams } from "next/navigation";
import { useErrorModalState } from "@/hooks";
import { formatAxiosErrorMessage } from "@/utils/errors";
import { AxiosError } from "axios";
import { SubScriptionModulePropTypes, useSubscriptionPlan } from "../../api/getSubscriptionPlan";
import { formatDuration } from "../../util";
import { SmallSpinner } from "@/icons/core";
import { convertNumberToNaira } from "@/utils/currency";

interface UseBooleanStateControlProps {
    selectedModules: SubScriptionModulePropTypes[];
    setShowSummaryModal: React.Dispatch<React.SetStateAction<boolean>>;
    setShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>
    showSummaryModal: boolean;
    setSelectedModules: React.Dispatch<React.SetStateAction<SubScriptionModulePropTypes[]>>
    showSubcriptionPlan?: boolean
}

function SubscriptionSummary({
    selectedModules,
    setShowSummaryModal,
    showSummaryModal,
    setShowSuccessModal,
    setSelectedModules,
    showSubcriptionPlan = false
}: UseBooleanStateControlProps) {

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const [selectedValue, setSelectedValue] = useState<string | undefined>(undefined)
    const search = useSearchParams()
    const company_id = search.get("companyId")
    const [isVisible, setIsVisible] = useState(false)
    //const [isGenerating, setIsGenerating] = useState(false);

    const { mutate: handleCreateInvoice, isLoading } = useCreateInvoice();
    const { mutate: handlePaystackInvoice, isLoading: isLoadingPaystack } = useCreateInvoice();
    const handleViewClick = (e: React.MouseEvent) => {
        e.preventDefault() // Prevent the click from propagating to the label
        setIsVisible(!isVisible)
    }

    // Function to calculate the total selected plan price
    const getTotalSelectedPrice = () => {
        return selectedModules.reduce((total, module) => {
            const selectedPlan = module.selectedPlan; // Get the selected plan for the module
            if (selectedPlan && selectedPlan.is_active) {
                // If the selected plan is active, add its price to the total
                return total + selectedPlan.price;
            }
            return total;
        }, 0);
    };

    // Calculate tokens needed (1 token = 10 naira)
    const getTokensNeeded = () => {
        return Math.ceil(getTotalSelectedPrice() / 10);
    };

    // Check if promotional offer applies
    const hasStockAndSales = selectedModules.some(m => m.name.toLowerCase().includes('stock')) &&
        selectedModules.some(m => m.name.toLowerCase().includes('sales'));

    // Check if HR or Spend Management modules are selected
    const hasPromotionalModules = selectedModules.some(m =>
        m.name.toLowerCase().includes('hr') || m.name.toLowerCase().includes('spend')
    );




    const { data: subscriptionPlanList } = useSubscriptionPlan();



    // select duration dropdown
    const handlePlanChange = (moduleId: number, durationMonths: number) => {
        const plan = subscriptionPlanList?.plans?.find((plan) => plan.duration_months === durationMonths);
        if (plan) {
            setSelectedModules((prevPlans) => {
                const updatedModules = prevPlans.map((module) => {
                    if (module.id === moduleId) {
                        return { ...module, selectedPlan: plan };
                    }
                    return module;
                });
                return updatedModules;
            });
        }
    };



    const requestData = selectedModules?.map((module) => ({
        module_id: module?.id ?? 0,
        plan: Number(module?.selectedPlan?.id), // Ensure plan is always a number
    })) || []; // Ensure it's always an array



    const handleSubscribe = () => {


        handleCreateInvoice(
            {
                company_id: String(company_id),
                requestData: requestData

            }, // Use requestData here
            {
                onSuccess: (data) => {
                    if (data) {
                        setShowSuccessModal(true);
                        setShowSummaryModal(false);

                    }
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                }
            }
        );
    };

    const router = useRouter()
    const handlePaystack = () => {


        handlePaystackInvoice(
            {
                company_id: String(company_id),
                requestData: requestData

            }, // Use requestData here
            {
                onSuccess: (data) => {
                    if (data) {
                        router.replace(data?.payment_link)
                    }
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                }
            }
        );
    };



    return (
        <div className="bg-white h-full !z-[999999999999999999] flex flex-col"> {/* Make the outer container a flex container */}
            <ClientOnly>
                <Sheet

                    open={showSummaryModal}
                    onOpenChange={() => setShowSummaryModal(false)}
                >
                    <SheetContent className="overflow-auto z-[999999999999999999] bg-white rounded-t-[1.125rem] p-0 flex flex-col h-full">
                        <SheetHeader className="flex items-center justify-between gap-24 rounded-t-[1.125rem] bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-4 text-white">
                            <div>
                                <SheetTitle className="text-lg font-bold text-white">
                                    🛒 Subscription Summary
                                </SheetTitle>
                                <p className="text-blue-100 text-sm mt-1">
                                    Token-based pricing • 1 Token = ₦10
                                </p>
                            </div>
                            <SheetClose className="bg-white bg-opacity-20 hover:bg-opacity-30 px-6 py-2 text-white rounded-lg transition-all duration-200">
                                ✕ Close
                            </SheetClose>
                        </SheetHeader>

                        <SheetDescription className='overflow-auto bg-gradient-to-br from-blue-50 to-indigo-50 w-full flex-1'>
                            <div className='px-7 h-full flex flex-col justify-between'>
                                <div className="h-[90vh] overflow-y-auto">
                                    {/* Promotional Banner */}
                                    {hasStockAndSales && (
                                        <div className="mt-4 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl p-4 text-white shadow-lg">
                                            <div className="flex items-center gap-2 mb-2">
                                                <span className="text-lg">🎉</span>
                                                <h3 className="font-bold text-lg">Congratulations!</h3>
                                            </div>
                                            <p className="text-emerald-100 text-sm">
                                                You've selected Stock & Sales modules and earned <strong>30 days FREE</strong> access to HR and Spend Management!
                                            </p>
                                        </div>
                                    )}

                                    <div className="mt-4 border-2 border-blue-200 rounded-xl p-6 bg-white shadow-lg">
                                        <div className="py-4">
                                            <h3 className="text-lg font-bold text-gray-800 mb-4">Selected Modules</h3>
                                            {selectedModules?.map((module, index) => {
                                                const isPriority = module.name.toLowerCase().includes('stock') ||
                                                    module.name.toLowerCase().includes('sales');
                                                const isPromotional = module.name.toLowerCase().includes('hr') ||
                                                    module.name.toLowerCase().includes('spend');
                                                const tokensNeeded = Math.ceil((module?.selectedPlan?.price || 0) / 10);

                                                return (
                                                    <div className={`px-6 py-4 mt-3 rounded-lg border-2 ${isPriority
                                                        ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200'
                                                        : isPromotional
                                                            ? 'bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200'
                                                            : 'bg-gray-50 border-gray-200'
                                                        }`} key={index}>
                                                        <div className="flex items-center justify-between mb-3">
                                                            <div className="flex items-center gap-2">
                                                                <h4 className="text-gray-800 font-bold text-lg">
                                                                    {module?.name}
                                                                </h4>
                                                                {isPriority && <span className="text-xs bg-blue-600 text-white px-2 py-1 rounded-full">⭐ PRIORITY</span>}
                                                                {isPromotional && hasStockAndSales && <span className="text-xs bg-emerald-600 text-white px-2 py-1 rounded-full">🎁 FREE</span>}
                                                            </div>
                                                        </div>

                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <p className="text-sm text-gray-600 mb-1">Amount</p>
                                                                <p className="font-bold text-blue-700 text-lg">
                                                                    {convertNumberToNaira(Number(module?.selectedPlan?.price))}
                                                                    <span className="text-sm text-gray-500 font-normal">
                                                                        /{formatDuration(Number(module?.selectedPlan?.duration_months))}
                                                                    </span>
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <p className="text-sm text-gray-600 mb-1">Tokens Required</p>
                                                                <p className="font-bold text-purple-700 text-lg">
                                                                    {addCommasToNumber(tokensNeeded)} tokens
                                                                    <span className="text-sm text-gray-500 font-normal"> (₦10 each)</span>
                                                                </p>
                                                            </div>
                                                        </div>

                                                        {isPromotional && hasStockAndSales && (
                                                            <div className="mt-3 p-2 bg-emerald-100 rounded-lg">
                                                                <p className="text-emerald-700 text-sm font-medium">
                                                                    🎉 FREE for 30 days with Stock & Sales subscription!
                                                                </p>
                                                            </div>
                                                        )}
                                                    </div>
                                                );
                                            })}

                                            {/* Total Summary */}
                                            <div className="bg-gradient-to-r from-blue-100 to-purple-100 px-6 py-4 mt-6 rounded-lg border-2 border-blue-300">
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <p className="text-sm text-gray-600 mb-1">Total Amount</p>
                                                        <p className="text-2xl font-bold text-blue-700">
                                                            {convertNumberToNaira(getTotalSelectedPrice())}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="text-sm text-gray-600 mb-1">Total Tokens Needed</p>
                                                        <p className="text-2xl font-bold text-purple-700">
                                                            {addCommasToNumber(getTokensNeeded())} tokens
                                                        </p>
                                                        <p className="text-xs text-gray-500">
                                                            {getTokensNeeded()} × ₦10 each
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="mt-8">
                                        <div className="flex justify-center items-center gap-x-3 py-4">
                                            <div className="border-dashed border-2 border-blue-300 w-[120px] h-px"></div>
                                            <div className="text-lg font-bold text-gray-700 bg-white px-4 py-2 rounded-lg shadow-sm">
                                                💳 Payment Options
                                            </div>
                                            <div className="border-dashed border-2 border-blue-300 w-[120px] h-px"></div>
                                        </div>
                                    </div>

                                    <div className="border-2 border-blue-200 rounded-xl p-6 mt-4 bg-white shadow-lg">
                                        <div>
                                            <RadioGroup value={selectedValue} onValueChange={setSelectedValue}>
                                                <div className="flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl py-4 px-6 border border-blue-200 hover:shadow-md transition-all duration-200">
                                                    <RadioGroupItem
                                                        aria-labelledby="main-label"
                                                        className={`w-5 h-5 p-0 rounded-full border-2
                                                        ${selectedValue === "main-wallet" ? "bg-blue-600 border-blue-600" : "bg-white border-blue-400"}
                                                    `}
                                                        id="main-wallet"
                                                        value="main-wallet"
                                                    />
                                                    <label className="sm:flex-row flex flex-col justify-between gap-3.5 cursor-pointer flex-1" htmlFor="main-wallet" id="main-label">
                                                        <div className="flex items-center gap-2">
                                                            <span className="text-xl">💰</span>
                                                            <p className="text-blue-700 text-base font-semibold">Token Wallet</p>
                                                        </div>
                                                        <div>
                                                            <p className="text-sm text-gray-600">Balance: <span className="text-blue-700 font-bold">0 tokens</span></p>
                                                            <p className="text-xs text-gray-500">Purchase tokens to use this option</p>
                                                        </div>
                                                    </label>
                                                </div>

                                                <div className="mt-4">
                                                    <div className="flex items-center space-x-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl py-4 px-6 border border-green-200 hover:shadow-md transition-all duration-200">
                                                        <RadioGroupItem
                                                            aria-labelledby="transfer-label"
                                                            className={`w-5 h-5 p-0 rounded-full border-2
                                                    ${selectedValue === "transfer" ? "bg-green-600 border-green-600" : "bg-white border-green-400"}
                                                    `}
                                                            id="transfer"
                                                            value="transfer"
                                                        />
                                                        <label className="sm:flex-row flex-col flex items-center justify-between gap-4 cursor-pointer flex-1" htmlFor="transfer" id="transfer-label">
                                                            <div className="flex items-center gap-2">
                                                                <span className="text-xl">🏦</span>
                                                                <p className="text-green-700 text-base font-semibold">Bank Transfer</p>
                                                            </div>
                                                            <div className="bg-white flex items-center rounded-lg px-4 py-2 shadow-sm border border-green-200">
                                                                <button className="flex items-center justify-center gap-2 text-green-700 hover:text-green-800 transition-colors" onClick={handleViewClick}>
                                                                    <span className="font-medium text-sm">View Details</span>
                                                                    <ArrowDown />
                                                                </button>
                                                            </div>
                                                        </label>
                                                    </div>

                                                    {isVisible && (
                                                        <div className="mt-4 bg-white rounded-lg p-6 border border-green-200 shadow-sm">
                                                            <h4 className="text-green-700 font-bold text-lg mb-4 flex items-center gap-2">
                                                                <span>🏦</span>
                                                                Bank Transfer Details
                                                            </h4>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                                <div className="space-y-4">
                                                                    <div>
                                                                        <p className="text-gray-600 text-sm font-medium mb-1">Account Name</p>
                                                                        <p className="font-bold text-gray-800 text-lg">Liberty Assured</p>
                                                                    </div>
                                                                    <div>
                                                                        <p className="text-gray-600 text-sm font-medium mb-1">Bank Name</p>
                                                                        <p className="font-bold text-gray-800 text-lg">VFD Microfinance Bank</p>
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <p className="text-gray-600 text-sm font-medium mb-1">Account Number</p>
                                                                    <div className="flex items-center gap-3 bg-gray-50 p-3 rounded-lg border">
                                                                        <p className="font-bold text-gray-800 text-xl font-mono">*********</p>
                                                                        <button className="text-green-600 hover:text-green-700 transition-colors">
                                                                            <Copy />
                                                                        </button>
                                                                    </div>
                                                                    <p className="text-xs text-gray-500 mt-2">Click to copy account number</p>
                                                                </div>
                                                            </div>
                                                            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                                                <p className="text-yellow-800 text-sm font-medium">
                                                                    💡 <strong>Important:</strong> Please use your company ID as the transfer reference for faster processing.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </RadioGroup>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white rounded-xl p-6 shadow-lg border-t-4 border-blue-500">
                                    <h4 className="text-lg font-bold text-gray-800 mb-4 text-center">
                                        🚀 Complete Your Subscription
                                    </h4>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <Button
                                            className='bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex items-center gap-3 justify-center px-6 py-4 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105'
                                            onClick={handleSubscribe}
                                        >
                                            📄 Generate Invoice
                                            {isLoading && <SmallSpinner color="white" />}
                                        </Button>

                                        <Button
                                            className="flex items-center px-6 py-4 gap-3 border-2 border-green-500 bg-white hover:bg-green-50 rounded-xl justify-center text-green-700 font-semibold shadow-lg transition-all duration-200 transform hover:scale-105"
                                            onClick={handlePaystack}
                                        >
                                            <Image alt="Paystack" height={20} src="/images/subscription/Paystack.png" width={20} />
                                            {isLoadingPaystack ? <SmallSpinner color="green" /> : "Pay with Paystack"}
                                        </Button>
                                    </div>

                                    {/* Payment Status Buttons */}
                                    {selectedValue === "transfer" && (
                                        <div className="mt-6 flex items-center justify-center w-full">
                                            <Button className='w-full py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105'>
                                                ✅ I have made the transfer
                                            </Button>
                                        </div>
                                    )}

                                    {selectedValue === "main-wallet" && (
                                        <div className="mt-6 flex items-center justify-center w-full">
                                            <Button className='w-full py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg transition-all duration-200 transform hover:scale-105'>
                                                🚀 Continue with Tokens
                                            </Button>
                                        </div>
                                    )}

                                </div>
                                {/* Buttons Container */}
                            </div>
                        </SheetDescription>
                    </SheetContent>
                </Sheet>
            </ClientOnly>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={errorModalMessage || 'Please check your inputs and try again.'}
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button className="grow bg-red-950 text-base" size="lg" onClick={closeErrorModal}>
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </div >

    );
}


export default SubscriptionSummary;




