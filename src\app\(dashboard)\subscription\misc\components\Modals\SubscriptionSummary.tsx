
"use client";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ErrorModal,
    RadioGroup,
    RadioGroupItem,
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from "@/components/core";
import { Sheet, SheetClose, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/core/Sheet";
import { useCreateInvoice } from "../../api/createSubscribe";
import { useState } from "react";
import ArrowDown from "@/components/icons/ArrowDown";
import Copy from "@/components/icons/Copy";
import Image from 'next/image';
import { addCommasToNumber } from "@/utils/numbers";
import { useRouter, useSearchParams } from "next/navigation";
import { useErrorModalState } from "@/hooks";
import { formatAxiosErrorMessage } from "@/utils/errors";
import { AxiosError } from "axios";
import { SubScriptionModulePropTypes, useSubscriptionPlan } from "../../api/getSubscriptionPlan";
import { formatDuration } from "../../util";
import { SmallSpinner } from "@/icons/core";
import { convertNumberToNaira } from "@/utils/currency";

interface UseBooleanStateControlProps {
    selectedModules: SubScriptionModulePropTypes[];
    setShowSummaryModal: React.Dispatch<React.SetStateAction<boolean>>;
    setShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>
    showSummaryModal: boolean;
    setSelectedModules: React.Dispatch<React.SetStateAction<SubScriptionModulePropTypes[]>>
    showSubcriptionPlan?: boolean
}

function SubscriptionSummary({
    selectedModules,
    setShowSummaryModal,
    showSummaryModal,
    setShowSuccessModal,
    setSelectedModules,
    showSubcriptionPlan = false
}: UseBooleanStateControlProps) {

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const [selectedValue, setSelectedValue] = useState<string | undefined>(undefined)
    const search = useSearchParams()
    const company_id = search.get("companyId")
    const [isVisible, setIsVisible] = useState(false)
    //const [isGenerating, setIsGenerating] = useState(false);

    const { mutate: handleCreateInvoice, isLoading } = useCreateInvoice();
    const { mutate: handlePaystackInvoice, isLoading: isLoadingPaystack } = useCreateInvoice();
    const handleViewClick = (e: React.MouseEvent) => {
        e.preventDefault() // Prevent the click from propagating to the label
        setIsVisible(!isVisible)
    }

    // Function to calculate the total selected plan price
    const getTotalSelectedPrice = () => {
        return selectedModules.reduce((total, module) => {
            const selectedPlan = module.selectedPlan; // Get the selected plan for the module
            if (selectedPlan && selectedPlan.is_active) {
                // If the selected plan is active, add its price to the total
                return total + selectedPlan.price;
            }
            return total;
        }, 0);
    };




    const { data: subscriptionPlanList } = useSubscriptionPlan();



    // select duration dropdown
    const handlePlanChange = (moduleId: number, durationMonths: number) => {
        const plan = subscriptionPlanList?.plans?.find((plan) => plan.duration_months === durationMonths);
        if (plan) {
            setSelectedModules((prevPlans) => {
                const updatedModules = prevPlans.map((module) => {
                    if (module.id === moduleId) {
                        return { ...module, selectedPlan: plan };
                    }
                    return module;
                });
                return updatedModules;
            });
        }
    };



    const requestData = selectedModules?.map((module) => ({
        module_id: module?.id ?? 0,
        plan: Number(module?.selectedPlan?.id), // Ensure plan is always a number
    })) || []; // Ensure it's always an array



    const handleSubscribe = () => {


        handleCreateInvoice(
            {
                company_id: String(company_id),
                requestData: requestData

            }, // Use requestData here
            {
                onSuccess: (data) => {
                    if (data) {
                        setShowSuccessModal(true);
                        setShowSummaryModal(false);

                    }
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                }
            }
        );
    };

    const router = useRouter()
    const handlePaystack = () => {


        handlePaystackInvoice(
            {
                company_id: String(company_id),
                requestData: requestData

            }, // Use requestData here
            {
                onSuccess: (data) => {
                    if (data) {
                        router.replace(data?.payment_link)
                    }
                },
                onError: (error: unknown) => {
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                }
            }
        );
    };



    return (
        <div className="bg-white h-full !z-[999999999999999999] flex flex-col"> {/* Make the outer container a flex container */}
            <ClientOnly>
                <Sheet

                    open={showSummaryModal}
                    onOpenChange={() => setShowSummaryModal(false)}
                >
                    <SheetContent className="overflow-auto z-[999999999999999999] bg-white rounded-t-[1.125rem] p-0 flex flex-col h-full">
                        <SheetHeader className="flex items-center justify-between gap-24 rounded-t-[1.125rem] bg-main-solid px-8 py-4 text-white">
                            <SheetTitle className="text-base font-semibold text-white">
                                Subscription Summary
                            </SheetTitle>
                            <SheetClose className="bg-[#2D4696] px-6 py-2 text-white rounded-[0.5625rem]">
                                close
                            </SheetClose>
                        </SheetHeader>

                        <SheetDescription className='overflow-auto bg-[#F2F5FF] w-full flex-1'>
                            <div className='px-7 h-full flex flex-col justify-between'>
                                <div className="h-[90vh] overflow-y-auto">
                                    <div className="mt-4 border-[0.2px] border-dashed border-[#032180] rounded-lg p-4">
                                        <div className="py-[1.0625rem]">
                                            {selectedModules?.map((module, index) => (
                                                <div className="bg-[#E6EDFF] px-6 py-[1.0625rem] mt-3" key={index}>
                                                    <div>
                                                        <p>
                                                            <span className="text-[#242424CC]/80 font-semibold text-[1rem]">
                                                                Plan Module
                                                            </span>
                                                            :{" "}
                                                            <span className="text-[#242424] font-bold leading-5 text-[1rem]">
                                                                {module?.name}
                                                            </span>
                                                        </p>
                                                    </div>
                                                    <div className="mt-2">
                                                        <p className="font-semibold leading-[1.1938rem] text-[#242424CC]/80 text-[1rem]">
                                                            Amount -{" "}
                                                            <span className="text-[#032180] font-bold text-[1rem]">
                                                                ₦{addCommasToNumber(Number(module?.selectedPlan?.price))}
                                                                /<span className="font-normal text-[0.625rem]">
                                                                    {formatDuration(Number(module?.selectedPlan?.duration_months))}
                                                                </span>
                                                            </span>
                                                        </p>
                                                    </div>

                                                    {showSubcriptionPlan && <Select
                                                        value={module.selectedPlan?.duration_months?.toString() || ''}  // Default value from selectedPlan
                                                        onValueChange={(value) => handlePlanChange(module.id, Number(value))}
                                                    >
                                                        <SelectTrigger className="truncate border border-primary h-11 max-w-[8rem] mt-2 bg-transparent text-sm" id="delivery_date">
                                                            <SelectValue placeholder="Select a Plan" />
                                                        </SelectTrigger>
                                                        <SelectContent className="z-[9999999999999999]">
                                                            {subscriptionPlanList?.plans?.map((sub) =>
                                                                sub.is_active ? (
                                                                    <SelectItem key={sub.id} value={String(sub.duration_months)}>
                                                                        {formatDuration(Number(sub.duration_months))}
                                                                    </SelectItem>
                                                                ) : null
                                                            )}
                                                        </SelectContent>
                                                    </Select>

                                                    }

                                                </div>
                                            ))}

                                            <div className="bg-[#fFF] px-6 py-[1.0625rem] mt-3">
                                                <div className="">
                                                    <p><span className='text-[#242424CC]/80 font-semibold text-[1rem]'>Total Amount -</span>: <span className='text-[#032180] font-extrabold text-[1.25rem] leading-[1.705rem] '>{convertNumberToNaira(getTotalSelectedPrice())}</span></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="mt-6">
                                        <div className="flex justify-center items-center gap-x-2 py-3">
                                            <div className="border-dashed border-[0.2px] border-[#032180] w-[120px] h-px"></div>
                                            <div className="text-[0.875rem] text-[#242424] leading-[1.1938rem] font-normal">Payment Option</div>
                                            <div className="border-dashed border-[0.2px] border-[#032180] w-[120px] h-px"></div>
                                        </div>
                                    </div>

                                    <div className="border-[0.2px] border-dashed border-[#032180] rounded-lg p-4 mt-[1.1875rem]">
                                        <div className="mt-4">
                                            <RadioGroup value={selectedValue} onValueChange={setSelectedValue}>
                                                <div className="flex items-center space-x-2 bg-[#E6EDFF] rounded-10 py-[0.8125rem] pl-6 ">
                                                    <RadioGroupItem
                                                        aria-labelledby="main-label"
                                                        className={`w-4 h-4 p-0 rounded-full border
                                                        ${selectedValue === "bg-[#E6EDFF]" ? "bg-[#407BFF] border-[#407BFF]" : "bg-[#E6EDFF] border-[#032180]"}
                                                    `}
                                                        id="main-wallet"
                                                        value="main-wallet"
                                                    />
                                                    <label className="sm:flex-row flex flex-col justify-between gap-3.5 cursor-pointer" htmlFor="main" id="main-label">
                                                        <p className="text-[#032180] text-[0.875rem] leading-[1.1928rem] font-normal">Main wallet</p>
                                                        <p><span className='text-[0.875rem] leading-[1.1928rem] text-[#032180] font-normal'>Balance:</span> <span className="text-[#032180] font-medium leading-[1.1928rem] text-[0.875rem]">₦0</span></p>
                                                    </label>
                                                </div>

                                                <div className="bg-[#E6EDFF]">
                                                    <div className="flex items-center space-x-2 bg-[#E6EDFF] rounded-10 py-[0.8125rem] pl-6 ">
                                                        <RadioGroupItem
                                                            aria-labelledby="main-label"
                                                            className={`w-4 h-4 p-0 rounded-full border
                                                    ${selectedValue === "bg-[#E6EDFF]" ? "bg-[#407BFF] border-[#407BFF]" : "bg-[#E6EDFF] border-[#032180]"}
                                                    `}
                                                            id="transfer"
                                                            value="transfer"
                                                        />
                                                        <label className=" sm:flex-row flex-col flex item-center justify-between gap-[0.6rem] sm:gap-[8.75rem] cursor-pointer" htmlFor="transfer" id="transfer">
                                                            <p className="text-[#242424] text-[0.875rem] leading-[1.1928rem] font-normal mt-1 ">Transfer</p>
                                                            <div className="bg-white flex items-center rounded-lg w-[4.3125rem] h-[1.6875rem] px-4">
                                                                <button className="flex item-center items-center justify-center gap-2" onClick={handleViewClick}>
                                                                    <span className="font-normal text-[0.75rem] leading-[0.9762rem] mt-1">View</span>
                                                                    <ArrowDown />
                                                                </button>
                                                            </div>
                                                        </label>
                                                    </div>

                                                    {isVisible && (
                                                        <div className="text-[#242424] text-[0.875rem] leading-[1.1928rem] font-normal px-6">
                                                            <article className="flex justify-between ">
                                                                <div className="grid grid-cols-2 py-5 gap-10 w-full">
                                                                    <div className="w-full">
                                                                        <p className="text-[#242424CC]/80 text-xs leading-[16.37px]">Account name</p>
                                                                        <p className="font-sans font-medium text-[1rem] leading-[1.3638rem]">Liberty assured</p>
                                                                    </div>
                                                                    <div className="w-full">
                                                                        <p className="text-[#242424CC]/80 text-xs">Account no</p>
                                                                        <div className="flex items-center gap-3">
                                                                            <p className="font-sans font-medium text-[#242424] text-[1rem] leading-[1.3638rem]">*********</p>
                                                                            <Copy />
                                                                        </div>
                                                                    </div>
                                                                    <div className="gap-x-4 col-span-2">
                                                                        <p className="text-[#242424CC]/80 text-xs">Bank name</p>
                                                                        <p className="font-sans font-medium">VFD Microfinance Bank</p>
                                                                    </div>
                                                                </div>
                                                            </article>
                                                        </div>
                                                    )}
                                                </div>
                                            </RadioGroup>
                                        </div>
                                    </div>
                                </div>

                                <div className="max-md:block">
                                    <div className="max-md:flex-col flex justify-between gap-4 py-4 mt-6">
                                        <Button className='bg-[#F2F5FF] text-[#032282] border border-[#032282] flex item-center gap-x-3 justify-center px-[1.875rem] py-3 rounded-md' onClick={handleSubscribe}>
                                            Generate Invoice {isLoading && <SmallSpinner color="blue" />}
                                        </Button>

                                        {<Button className="flex item-center px-[0.8rem] py-3 gap-2 border border-[#032282]  bg-white rounded-md justify-center  text-[#032282]" onClick={handlePaystack}>
                                            <Image alt="" className="" height={18} src="/images/subscription/Paystack.png" width={18} />
                                            <p className='font-medium text-[0.875rem] leading-[1.1938rem]'>{isLoadingPaystack ? <SmallSpinner color="blue" /> : "Pay with paystack"}</p>
                                        </Button>}
                                    </div>

                                    {/* Payment Status Buttons */}
                                    {selectedValue === "transfer" && (
                                        <div className="py-[1.0625rem]  flex items-center justify-center w-full">
                                            <Button className=' w-full py-[0.6875rem]'>
                                                I have made payment
                                            </Button>
                                        </div>
                                    )}

                                    {selectedValue === "main-wallet" && (
                                        <div className="py-[1.0625rem]  flex items-center justify-center w-full">
                                            <Button className='px-[9.375rem] py-[0.6875rem]'>
                                                Continue
                                            </Button>
                                        </div>
                                    )}

                                </div>
                                {/* Buttons Container */}
                            </div>
                        </SheetDescription>
                    </SheetContent>
                </Sheet>
            </ClientOnly>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={errorModalMessage || 'Please check your inputs and try again.'}
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button className="grow bg-red-950 text-base" size="lg" onClick={closeErrorModal}>
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </div>

    );
}


export default SubscriptionSummary;




