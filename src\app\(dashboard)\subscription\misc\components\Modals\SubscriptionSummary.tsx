
"use client";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ErrorModal,
    RadioGroup,
    RadioGroupItem,
    // Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from "@/components/core";
import { Sheet, SheetClose, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/core/Sheet";
import { useCreateInvoice } from "../../api/createSubscribe";
import { useState } from "react";
import ArrowDown from "@/components/icons/ArrowDown";
import Copy from "@/components/icons/Copy";
import Image from 'next/image';
import { addCommasToNumber } from "@/utils/numbers";
import { useRouter, useSearchParams } from "next/navigation";
import { useErrorModalState } from "@/hooks";
import { formatAxiosErrorMessage } from "@/utils/errors";
import { AxiosError } from "axios";
import { SubScriptionModulePropTypes, useSubscriptionPlan } from "../../api/getSubscriptionPlan";
// import { formatDuration } from "../../util";
import { SmallSpinner } from "@/icons/core";
import { convertNumberToNaira } from "@/utils/currency";

interface UseBooleanStateControlProps {
    selectedModules: SubScriptionModulePropTypes[];
    setShowSummaryModal: React.Dispatch<React.SetStateAction<boolean>>;
    setShowSuccessModal: React.Dispatch<React.SetStateAction<boolean>>
    showSummaryModal: boolean;
    setSelectedModules: React.Dispatch<React.SetStateAction<SubScriptionModulePropTypes[]>>
    showSubcriptionPlan?: boolean
    moduleTokenUnits: Map<number, number>;
}

function SubscriptionSummary({
    selectedModules,
    setShowSummaryModal,
    showSummaryModal,
    setShowSuccessModal,
    setSelectedModules,
    // showSubcriptionPlan = false,
    moduleTokenUnits
}: UseBooleanStateControlProps) {

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        openErrorModalWithMessage,
        errorModalMessage,
    } = useErrorModalState();
    const [selectedValue, setSelectedValue] = useState<string | undefined>(undefined)
    const search = useSearchParams()
    const company_id = search.get("companyId")
    const [isVisible, setIsVisible] = useState(false)
    //const [isGenerating, setIsGenerating] = useState(false);

    const { mutate: handleCreateInvoice, isLoading } = useCreateInvoice();
    const { mutate: handlePaystackInvoice, isLoading: isLoadingPaystack } = useCreateInvoice();
    const handleViewClick = (e: React.MouseEvent) => {
        e.preventDefault() // Prevent the click from propagating to the label
        setIsVisible(!isVisible)
    }

    // Function to calculate the total price based on token units
    const getTotalSelectedPrice = () => {
        return selectedModules.reduce((total, module) => {
            const tokenUnits = moduleTokenUnits.get(module.id) || 0;
            return total + (tokenUnits * 10); // 1 token = ₦10
        }, 0);
    };

    // Calculate total tokens needed from all selected modules
    const getTokensNeeded = () => {
        return selectedModules.reduce((total, module) => {
            return total + (moduleTokenUnits.get(module.id) || 0);
        }, 0);
    };

    // Check if promotional offer applies
    const hasSalesSelected = selectedModules.some(m => m.name.toLowerCase().includes('sales'))
    const hasStockAndSales = hasSalesSelected // Since Stock is always available, we just need Sales

    // Check if HR or Spend Management modules are selected
    const _hasPromotionalModules = selectedModules.some(m =>
        m.name.toLowerCase().includes('hr') || m.name.toLowerCase().includes('spend')
    );




    const { data: subscriptionPlanList } = useSubscriptionPlan();



    // select duration dropdown
    const _handlePlanChange = (moduleId: number, durationMonths: number) => {
        const plan = subscriptionPlanList?.plans?.find((plan) => plan.duration_months === durationMonths);
        if (plan) {
            setSelectedModules((prevPlans) => {
                const updatedModules = prevPlans.map((module) => {
                    if (module.id === moduleId) {
                        return { ...module, selectedPlan: plan };
                    }
                    return module;
                });
                return updatedModules;
            });
        }
    };



    const requestData = {
        company_id: String(company_id),
        subscription_modules: selectedModules?.map((module) => ({
            module_id: module?.id ?? 0,
            plan: Number(module?.selectedPlan?.id), // Ensure plan is always a number
        })) || [], // Ensure it's always an array
        is_token: true,
        token_units: getTokensNeeded()
    };

    // Debug logging
    // console.log('Selected Modules:', selectedModules);
    // console.log('Module Token Units:', moduleTokenUnits);
    // console.log('Request Data:', requestData);



    const handleSubscribe = () => {
        // Validation
        if (!selectedModules || selectedModules.length === 0) {
            openErrorModalWithMessage('Please select at least one module');
            return;
        }

        if (getTokensNeeded() <= 0) {
            openErrorModalWithMessage('Please enter token units for selected modules');
            return;
        }

        // console.log('Submitting subscription with data:', requestData);

        handleCreateInvoice(
            requestData, // Use the new payload structure
            {
                onSuccess: (data) => {
                    // console.log('Invoice creation successful:', data);
                    if (data) {
                        setShowSuccessModal(true);
                        setShowSummaryModal(false);
                    }
                },
                onError: (error: unknown) => {
                    console.error('Invoice creation failed:', error);
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                }
            }
        );
    };

    const router = useRouter()
    const handlePaystack = () => {
        // Validation
        if (!selectedModules || selectedModules.length === 0) {
            openErrorModalWithMessage('Please select at least one module');
            return;
        }

        if (getTokensNeeded() <= 0) {
            openErrorModalWithMessage('Please enter token units for selected modules');
            return;
        }

        // console.log('Submitting Paystack payment with data:', requestData);

        handlePaystackInvoice(
            requestData, // Use the new payload structure
            {
                onSuccess: (data) => {
                    // console.log('Paystack payment successful:', data);
                    if (data) {
                        router.replace(data?.payment_link)
                    }
                },
                onError: (error: unknown) => {
                    console.error('Paystack payment failed:', error);
                    const errorMessage = formatAxiosErrorMessage(error as AxiosError);
                    openErrorModalWithMessage(errorMessage);
                }
            }
        );
    };



    return (
        <div className="bg-white h-full !z-[999999999999999999] flex flex-col"> {/* Make the outer container a flex container */}
            <ClientOnly>
                <Sheet

                    open={showSummaryModal}
                    onOpenChange={() => setShowSummaryModal(false)}
                >
                    <SheetContent className="overflow-auto z-[999999999999999999] bg-white rounded-t-[1.125rem] p-0 flex flex-col h-full">
                        <SheetHeader className="flex items-center justify-between gap-24 rounded-t-[1.125rem] bg-[#032282] px-8 py-4 text-white">
                            <div>
                                <SheetTitle className="text-sm font-bold text-white">
                                    Subscription Summary
                                </SheetTitle>
                                <p className="text-blue-100 text-xs mt-1">
                                    Token-based pricing • 1 Token = ₦10
                                </p>
                            </div>
                            <SheetClose className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 text-white rounded-lg transition-all duration-200 text-sm">
                                Close
                            </SheetClose>
                        </SheetHeader>

                        <SheetDescription className='overflow-auto bg-gray-50 w-full flex-1'>
                            <div className='px-7 h-full flex flex-col justify-between'>
                                <div className="h-[90vh] overflow-y-auto">
                                    {/* Promotional Banner */}
                                    {hasStockAndSales && (
                                        <div className="mt-4 bg-white border border-[#032282] border-opacity-20 rounded-lg p-4 shadow-sm">
                                            <div className="flex items-center gap-2 mb-2">
                                                <span className="text-lg">🎁</span>
                                                <h3 className="font-semibold text-[#032282] text-lg">Special Offer Applied!</h3>
                                            </div>
                                            <p className="text-gray-600 text-sm">
                                                You&apos;ve selected Stock & Sales modules and earned <strong>30 days FREE</strong> access to HR and Spend Management!
                                            </p>
                                        </div>
                                    )}

                                    <div className="mt-4 border border-gray-200 rounded-lg p-6 bg-white shadow-sm">
                                        <div className="py-2">
                                            <h3 className="text-sm font-semibold text-[#032282] mb-4">Selected Modules</h3>
                                            {selectedModules?.map((module, index) => {
                                                const isPriority = module.name.toLowerCase().includes('stock') ||
                                                    module.name.toLowerCase().includes('sales');
                                                const isPromotional = module.name.toLowerCase().includes('hr') ||
                                                    module.name.toLowerCase().includes('spend');
                                                const moduleTokens = moduleTokenUnits.get(module.id) || 0;
                                                const moduleAmount = moduleTokens * 10;

                                                return (
                                                    <div className={`px-6 py-4 mt-3 rounded-lg border ${isPriority
                                                        ? 'bg-[#032282] bg-opacity-5 border-[#032282] border-opacity-20'
                                                        : isPromotional
                                                            ? 'bg-green-50 border-green-200'
                                                            : 'bg-gray-50 border-gray-200'
                                                        }`} key={index}>
                                                        <div className="flex items-center justify-between mb-3">
                                                            <div className="flex items-center gap-2">
                                                                <h4 className="text-gray-800 font-bold text-lg">
                                                                    {module?.name}
                                                                </h4>
                                                                {isPriority && <span className="text-xs bg-[#032282] text-white px-2 py-1 rounded-md">PRIORITY</span>}
                                                                {isPromotional && hasStockAndSales && <span className="text-xs bg-green-600 text-white px-2 py-1 rounded-md">FREE</span>}
                                                            </div>
                                                        </div>

                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <p className="text-sm text-gray-600 mb-1">Token Units</p>
                                                                <p className="font-bold text-[#032282] text-lg">
                                                                    {addCommasToNumber(moduleTokens)} units
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <p className="text-sm text-gray-600 mb-1">Amount</p>
                                                                <p className="font-bold text-[#032282] text-lg">
                                                                    {convertNumberToNaira(moduleAmount)}
                                                                    <span className="text-sm text-gray-500 font-normal"> ({moduleTokens} × ₦10)</span>
                                                                </p>
                                                            </div>
                                                        </div>

                                                        {isPromotional && hasStockAndSales && (
                                                            <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded-lg">
                                                                <p className="text-green-700 text-sm font-medium">
                                                                    FREE for 30 days with Stock & Sales subscription!
                                                                </p>
                                                            </div>
                                                        )}
                                                    </div>
                                                );
                                            })}

                                            {/* Total Summary */}
                                            <div className="bg-[#032282] bg-opacity-5 border border-[#032282] border-opacity-20 px-6 py-4 mt-6 rounded-lg">
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <div>
                                                        <p className="text-xs text-gray-600 mb-1">Total Amount</p>
                                                        <p className="text-xl font-bold text-[#032282]">
                                                            {convertNumberToNaira(getTotalSelectedPrice())}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p className="text-xs text-gray-600 mb-1">Total Tokens Needed</p>
                                                        <p className="text-xl font-bold text-[#032282]">
                                                            {addCommasToNumber(getTokensNeeded())} tokens
                                                        </p>
                                                        <p className="text-xs text-gray-500">
                                                            {getTokensNeeded()} × ₦10 each
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="mt-8">
                                        <div className="flex justify-center items-center gap-x-3 py-4">
                                            <div className="border-dashed border border-[#032282] border-opacity-30 w-[120px] h-px"></div>
                                            <div className="text-sm font-semibold text-[#032282] bg-white px-4 py-2 rounded-lg border border-gray-200 whitespace-nowrap">
                                                Payment Options
                                            </div>
                                            <div className="border-dashed border border-[#032282] border-opacity-30 w-[120px] h-px"></div>
                                        </div>
                                    </div>

                                    <div className="border border-gray-200 rounded-lg p-6 mt-4 bg-white shadow-sm">
                                        <div>
                                            <RadioGroup value={selectedValue} onValueChange={setSelectedValue}>
                                                <div className="flex items-center space-x-3 bg-[#032282] bg-opacity-5 rounded-lg py-4 px-6 border border-[#032282] border-opacity-20 hover:shadow-sm transition-all duration-200">
                                                    <RadioGroupItem
                                                        aria-labelledby="main-label"
                                                        className={`w-5 h-5 p-0 rounded-full border-2
                                                        ${selectedValue === "main-wallet" ? "bg-[#032282] border-[#032282]" : "bg-white border-gray-400"}
                                                    `}
                                                        id="main-wallet"
                                                        value="main-wallet"
                                                    />
                                                    <label className="sm:flex-row flex flex-col justify-between gap-3.5 cursor-pointer flex-1" htmlFor="main-wallet" id="main-label">
                                                        <div className="flex items-center gap-2">
                                                            <p className="text-[#032282] text-xs font-semibold">Token Wallet</p>
                                                        </div>
                                                        <div>
                                                            <p className="text-xs text-gray-600">Balance: <span className="text-[#032282] font-bold">0 tokens</span></p>
                                                            <p className="text-xs text-gray-500">Purchase tokens to use this option</p>
                                                        </div>
                                                    </label>
                                                </div>

                                                <div className="mt-4">
                                                    <div className="flex items-center space-x-3 bg-gray-50 rounded-lg py-4 px-6 border border-gray-200 hover:shadow-sm transition-all duration-200">
                                                        <RadioGroupItem
                                                            aria-labelledby="transfer-label"
                                                            className={`w-5 h-5 p-0 rounded-full border-2
                                                    ${selectedValue === "transfer" ? "bg-[#032282] border-[#032282]" : "bg-white border-gray-400"}
                                                    `}
                                                            id="transfer"
                                                            value="transfer"
                                                        />
                                                        <label className="sm:flex-row flex-col flex items-center justify-between gap-4 cursor-pointer flex-1" htmlFor="transfer" id="transfer-label">
                                                            <div className="flex items-center gap-2">
                                                                <p className="text-[#032282] text-[13px] font-semibold">Bank Transfer</p>
                                                            </div>
                                                            <div className="bg-white flex items-center rounded-lg px-4 py-2 shadow-sm border border-gray-200">
                                                                <button className="flex items-center justify-center gap-2 text-[#032282] hover:text-[#032282]/80 transition-colors" onClick={handleViewClick}>
                                                                    <span className="font-medium text-xs">View Details</span>
                                                                    <ArrowDown />
                                                                </button>
                                                            </div>
                                                        </label>
                                                    </div>

                                                    {isVisible && (
                                                        <div className="mt-4 bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
                                                            <h4 className="text-[#032282] font-semibold text-sm mb-4">
                                                                Bank Transfer Details
                                                            </h4>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                                <div className="space-y-4">
                                                                    <div>
                                                                        <p className="text-gray-600 text-xs font-medium mb-1">Account Name</p>
                                                                        <p className="font-bold text-gray-800 text-xs">Liberty Assured</p>
                                                                    </div>
                                                                    <div>
                                                                        <p className="text-gray-600 text-xs font-medium mb-1">Bank Name</p>
                                                                        <p className="font-bold text-gray-800 text-xs">VFD Microfinance Bank</p>
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <p className="text-gray-600 text-sm font-medium mb-1">Account Number</p>
                                                                    <div className="flex items-center gap-3 bg-gray-50 p-3 rounded-lg border">
                                                                        <p className="font-bold text-gray-800 text-sm font-mono">*********</p>
                                                                        <button className="text-[#032282] hover:text-[#032282]/80 transition-colors">
                                                                            <Copy className="w-2 h-2" />
                                                                        </button>
                                                                    </div>
                                                                    <p className="text-xs text-gray-500 mt-2">Click to copy account number</p>
                                                                </div>
                                                            </div>
                                                            <div className="mt-4 p-3 bg-[#032282] bg-opacity-5 border border-[#032282] border-opacity-20 rounded-lg">
                                                                <p className="text-[#032282] text-xs font-medium">
                                                                    <strong>Important:</strong> Please use your company ID as the transfer reference for faster processing.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </RadioGroup>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                                    <h4 className="text-sm font-semibold text-[#032282] mb-4 text-center">
                                        Complete Your Subscription
                                    </h4>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <Button
                                            className='bg-[#032282] hover:bg-[#032282]/90 text-white flex items-center gap-2 justify-center px-6 py-3 rounded-lg font-medium transition-all duration-200'
                                            onClick={handleSubscribe}
                                        >
                                            Generate Invoice
                                            {isLoading && <SmallSpinner color="white" />}
                                        </Button>

                                        <Button
                                            className="flex items-center px-6 py-3 gap-2 border border-[#032282] bg-white hover:bg-[#032282] hover:text-white rounded-lg justify-center text-[#032282] font-medium transition-all duration-200"
                                            onClick={handlePaystack}
                                        >
                                            <Image alt="Paystack" height={18} src="/images/subscription/Paystack.png" width={18} />
                                            {isLoadingPaystack ? <SmallSpinner color="blue" /> : "Paystack"}
                                        </Button>
                                    </div>

                                    {/* Payment Status Buttons */}
                                    {selectedValue === "transfer" && (
                                        <div className="mt-6 flex items-center justify-center w-full">
                                            <Button className='w-full py-3 bg-[#032282] hover:bg-[#032282]/90 text-white font-medium rounded-lg transition-all duration-200'>
                                                I have made the transfer
                                            </Button>
                                        </div>
                                    )}

                                    {selectedValue === "main-wallet" && (
                                        <div className="mt-6 flex items-center justify-center w-full">
                                            <Button className='w-full py-3 bg-[#032282] hover:bg-[#032282]/90 text-white font-medium rounded-lg transition-all duration-200'>
                                                Continue with Tokens
                                            </Button>
                                        </div>
                                    )}

                                </div>
                                {/* Buttons Container */}
                            </div>
                        </SheetDescription>
                    </SheetContent>
                </Sheet>
            </ClientOnly>

            <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={errorModalMessage || 'Please check your inputs and try again.'}
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button className="grow bg-red-950 text-base" size="lg" onClick={closeErrorModal}>
                        Okay
                    </Button>
                </div>
            </ErrorModal>
        </div >

    );
}


export default SubscriptionSummary;




