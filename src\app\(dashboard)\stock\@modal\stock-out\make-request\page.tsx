'use client'

import { useSearchParams } from 'next/navigation';
import React from 'react';

import { CreateStockOutRequestModal } from '../../../misc/components';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { LoaderModal } from '@/components/core';

export default function CreateBranch() {
  const searchParams = useSearchParams();

  const company = searchParams.get('company') || '';
  const branch = searchParams.get('branch') || '';

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();

  return (
    <>
      <LoaderModal isOpen={isCompanyLoading} />
      <CreateStockOutRequestModal
        branch={branch}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies?.results || []}
        company={company}
      />
    </>
  );
}
