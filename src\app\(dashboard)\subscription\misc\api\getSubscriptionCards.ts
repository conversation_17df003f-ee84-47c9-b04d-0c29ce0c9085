import { managementAxios } from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import { subPropTypes } from "./getCompanySubscriptionTable";






export interface SubscriptionCards {
    company: string;
    total_subscriptions: number;
    active_subscriptions: number;
    expired_subscriptions: number;
    subscriptions: SubscriptionDataProp;
    is_free_trial: boolean
    is_paid_subscription: boolean
    free_trial_days_remaining: number
}

export interface SubscriptionDataProp {
    paid: Paid;
    free_trial: Freetrial;
    free_merchant: Freetrial;
}

interface Freetrial {
    count: number;
    items: ModuleProp[];
}

export interface Paid {
    count: number;
    items: ModuleProp[];
}

export interface ModuleProp {
    module_id: number;
    module_name: string;
    module_code: string;
    module_status: string;
    module_plan: string;
    is_auto_renewal: boolean;
    promotional_offer: null;
    pending_balance: string;
    is_active: boolean;
    start_date: string;
    end_date: string;
    days_remaining: number;
    company_subscription_status: string;
    created_at: string;
    updated_at: string;
}




export const getSubscriptionCards = async (companyId: string) => {


    try {
        const response = await managementAxios.get(`/subscription_and_invoicing/company_subscription?company_id=${companyId}`);

        const subscriptionCards = response.data;

        // Merge items from paid, free_trial, and free_merchant into one array
        const modules: ModuleProp[] = [
            ...(subscriptionCards?.subscriptions.paid?.items || []),
            // ...(subscriptionCards?.subscriptions.free_trial?.items || []),
            ...(subscriptionCards?.subscriptions.free_merchant?.items || [])
        ];

        return {
            is_free_trial: subscriptionCards?.is_free_trial,
            is_paid_subscription: subscriptionCards?.is_paid_subscription,
            free_trial_days_remaining: subscriptionCards?.free_trial_days_remaining,
            company: subscriptionCards.company,
            total_subscriptions: subscriptionCards.total_subscriptions,
            active_subscriptions: subscriptionCards.active_subscriptions,
            expired_subscriptions: subscriptionCards.expired_subscriptions,
            subscriptions: modules // Return all modules merged into one array
        } as subPropTypes;

    } catch (error) {
        // console.error('Error fetching company subscription table:', error);
        return {
            company: 'Error',
            total_subscriptions: 0,
            active_subscriptions: 0,
            expired_subscriptions: 0,
            free_trial_days_remaining: 0,
            is_free_trial: false,
            is_paid_subscription: false,
            subscriptions: [] // Return an empty array in case of error
        } as subPropTypes;
    }

};

export const useSubscriptionCards = (companyId: string) => {
    return useQuery({
        queryKey: ['SubscriptionCards', companyId],
        queryFn: () => getSubscriptionCards(companyId),
        enabled: !!companyId

    });
};