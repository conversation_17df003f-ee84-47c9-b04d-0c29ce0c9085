'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import * as React from 'react';

import {
  Button,
  LinkButton,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/core';
import { useBooleanStateControl, useRouteChangeEvent } from '@/hooks';

import { useCompanies } from '@/app/(dashboard)/stock/misc/api';
import { CompanyListEntity } from '@/app/(dashboard)/payroll/misc/types';
import { ModuleSubscriptionModal } from '@/app/(dashboard)/payroll/misc/components/headerCompanyPayrollSwitch';

const pathsWithCompanySwitcher = ['/instant-web'];
const pathsWithoutCompanySwitcher = [
  '/instant-web/select-branch',
  '/instant-web/set-company-branch-stock',
  '/instant-web/product-details',
  "/instant-web/customize-website",
  "/instant-web/customize-website/select-branch",
  '/instant-web/product-details/customize-product',
  '/instant-web/product-details/inventory',
  '/instant-web/product-details/product-orders',
  '/instant-web/product-details/post-purchase',
  '/instant-web/product-categories',
  '/instant-web/manage-website',
  '/instant-web/qr-code'
];

export function HeaderCompanyInstantWebSwitcher() {
  const { data: companiesResponse } = useCompanies();

  const pathname = usePathname();
  const params = useSearchParams();
  const companyId = params.get('company');
  const companyName = params.get('companyName');
  const [selectedCompany, setSelectedCompany] = React.useState<CompanyListEntity | null>(null);
  const [showModuleModal, setShowModuleModal] = React.useState(false);


  const {
    state: isPopoverOpen,
    setState: setPopoverState,
    setFalse: closePopover,
  } = useBooleanStateControl();

  useRouteChangeEvent(() => closePopover());

  const hasSwitcher = React.useMemo(
    () =>
      pathsWithCompanySwitcher.some(page => pathname.includes(page)),
    [pathname]
  );

  const hasNoSwitcher = React.useMemo(
    () =>
      pathsWithoutCompanySwitcher.includes(pathname),
    [pathname]
  );

  const shouldShowSwitcher = hasSwitcher && !hasNoSwitcher;

  const { results: companies } = companiesResponse || {};
  const sortedCompanies = companies?.sort((a, b) => {
    if (a.default && !b.default) return -1; // a with default true comes before b with default false
    if (!a.default && b.default) return 1;  // b with default true comes before a with default false
    if (a.id === companyId) return -1;       // a with matching id comes before b
    if (b.id === companyId) return 1;        // b with matching id comes before a
    return 0;                               // No change in order for other cases
  });

  const companyNameAlt = companyName || companies?.find(company => company.id === companyId)?.company_name
  const router = useRouter();

  const handleCompanyClick = (company: CompanyListEntity) => {
    if (!company.is_subscription) {
      router.push(`/subscription?companyId=${company.id}&companyName=${company.company_name}`);
      return;
    }

    const allModules = [
      ...company.active_subscriptions.flatMap(sub =>
        sub.modules.map(module => ({
          ...module,
          subscription: sub,
          plan_id: module.plan_id
        }))
      ),
      ...company.not_started_subscriptions.flatMap(sub =>
        sub.modules.map(module => ({
          ...module,
          subscription: sub,
          plan_id: module.plan_id
        }))
      )
    ];

    const hasHRModule = allModules.some(module =>
      module.module.code === 'SALES' && module.is_active
    );

    if (!hasHRModule) {
      setSelectedCompany(company);
      setShowModuleModal(true);
      return;
    }
    router.push(`${pathname}?companyId=${company.id}&companyName=${company.company_name || ''}`);
  };

  const getSubscriptionStatus = (company: CompanyListEntity) => {
    const hasNotStartedSubscriptions = company?.not_started_subscriptions?.length > 0;

    if (hasNotStartedSubscriptions) {
      return {
        text: "Pending Payment",
        className: "text-amber-600 text-xxs"
      };
    }
    if (company.is_free_trial) {
      return {
        text: "Free Trial",
        className: "text-amber-600 text-xxs"
      };
    }
    if (company.is_paid_subscription) {
      return {
        text: "Paid Subscription",
        className: "text-green-600 text-xxs"
      };
    }
    if (company.is_subscription) {
      return {
        text: "Subscribed",
        className: "text-green-600 text-xxs"
      };
    }
    return {
      text: "Not Subscribed",
      className: "text-red-600 text-xxs"
    };
  };
  return !!companies && shouldShowSwitcher ? (
    <>
      <Popover open={isPopoverOpen} onOpenChange={setPopoverState}>
        <PopoverTrigger className="rounded-10 bg-white py-1 text-sm">
          <span className="rounded-lg bg-blue-700/10 px-3 py-1.5 text-xs text-main-solid md:bg-main-bg">
            Switch Company: <span className='uppercase font-semibold'>{companyNameAlt}</span>
          </span>

        </PopoverTrigger>
        <PopoverContent
          align="start"
          className="mt-2 w-full rounded-10 border-none p-0 pb-2 text-xs [@media(min-width:408px)]:max-w-[18.375rem]"
        >
          <div className='py-[11px] px-4 bg-[#EBF1FF] mt-3 flex items-center justify-between gap-2 w-full'>
            <p className='text-main-solid text-xxs'>Tap to <span className='font-bold'>change</span> default company</p>

            <LinkButton className='bg-[#DAE3FF] rounded-xl py-[5px] px-4 text-main-solid text-xxs' href={'/stock/set-company-branch-stock'}>Change</LinkButton>
          </div>
          <p className="px-4 pb-2.5 pt-4">Select company to change view.</p>
          <ul className="space-y-1 px-2">
            {sortedCompanies?.map((company) => {
              const status = getSubscriptionStatus(company);

              return <li key={company.id}>
                <Button
                  className="block w-full rounded-[.75rem] px-6 py-2.5 text-left text-label-text transition ease-in-out hover:bg-[#F8FAFF]"
                  variant="white"
                  onClick={() => handleCompanyClick(company)}
                >


                  <div className="flex flex-col gap-1">
                    <span>{company.company_name}</span>
                    <span className={status.className}>{status.text}</span>
                    <span>{company.default && <span className='bg-main-bg text-main-solid py-1 px-2 rounded-xl'>default</span>}
                    </span>
                  </div>
                </Button>
              </li>
            }
            )}
          </ul>
        </PopoverContent>
      </Popover>
      {
        selectedCompany && (
          <ModuleSubscriptionModal
            company={selectedCompany}
            isOpen={showModuleModal}
            onClose={() => {
              setShowModuleModal(false);
              setSelectedCompany(null);
            }}
          />
        )
      }
    </>

  ) : null;
}
