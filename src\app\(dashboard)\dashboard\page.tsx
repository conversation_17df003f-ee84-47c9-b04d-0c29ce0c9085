"use client"



import * as React from "react"
import { LinkButton } from "@/components/core"
import { WelcomeHeadingName } from "./misc/components"
import RedirectingModal from "./misc/components/RedirectingModal"
import {
  Package,
  TrendingUp,
  Database,
  Users,
  CreditCard,
  FileText,
  Calculator,
  Smartphone,
  Send,
  Receipt,
  Building2,
  Clock,
  UserCheck,
  Target,
  Star,
  Search,
  Settings,
  BarChart3,
  ShoppingCart,
  Truck,
  DollarSign,
  Calendar
} from "lucide-react"
import { cn } from "@/utils/classNames"

// Main featured modules (Stock, Sales, Inventory)
const featuredModules = [
  {
    name: "Stock Management",
    description: "Complete inventory control with real-time tracking and automated stock management.",
    icon: Package,
    gradient: "from-blue-200 to-blue-300",
    bgGradient: "from-blue-50 to-blue-50",
    features: ["Stock Inventory", "Stock Management", "Real-time Tracking", "Low Stock Alerts"],
    link: "/stock",
    stats: { label: "Items Tracked", value: "2,847" },
  },
  {
    name: "Sales Operations",
    description: "Powerful sales tools with customer management and transaction processing.",
    icon: TrendingUp,
    gradient: "from-emerald-200 to-emerald-300",
    bgGradient: "from-emerald-50 to-emerald-50",
    features: ["Sales Transactions", "Customer Management", "Price Lists", "Sales Analytics"],
    link: "/sales",
    stats: { label: "Monthly Sales", value: "₦2.4M" },
  },
  {
    name: "Inventory Management",
    description: "Optimize stock levels and improve order fulfillment with advanced inventory tools.",
    icon: Database,
    gradient: "from-purple-200 to-purple-300",
    bgGradient: "from-purple-50 to-purple-50",
    features: ["Inventory Tracking", "Order Management", "Supplier Management", "Stock Reports"],
    link: "/inventory",
    stats: { label: "Branches", value: "12" },
  },
]

// Other modules organized by category
const otherModules = {
  "HR Management": {
    icon: Users,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    features: [
      { name: "Employee Benefits", icon: UserCheck },
      { name: "Quick Access", icon: Clock },
      { name: "Companies", icon: Building2 },
      { name: "Employee Dashboard", icon: Users },
      { name: "Time & Attendance", icon: Clock },
      { name: "Leave Management", icon: Calendar },
      { name: "Performance MGT", icon: Target },
      { name: "Appraisal", icon: Star },
      { name: "Recruitment", icon: Search },
      { name: "Request Talent", icon: Users },
      { name: "Payroll Settings", icon: Settings },
    ],
    // link: "/payroll",
    externalUrl: "https://www.home.paybox360.com/payroll",
  },
  "Spend Management": {
    icon: CreditCard,
    color: "text-red-600",
    bgColor: "bg-red-50",
    features: [
      { name: "Overview", icon: BarChart3 },
      { name: "Companies", icon: Building2 },
      { name: "Teams", icon: Users },
      { name: "Requisitions", icon: FileText },
      { name: "Procurement", icon: ShoppingCart },
      { name: "Assets", icon: Package },
      { name: "Suppliers", icon: Truck },
      { name: "Invoices", icon: Receipt },
      { name: "Budgeting", icon: Calculator },
      { name: "Expenses", icon: DollarSign },
      { name: "Purchase Orders", icon: FileText },
    ],
    // link: "/spend-management",
    externalUrl: "https://www.home.paybox360.com/spend-management",
  },
  "Financial Services": {
    icon: DollarSign,
    color: "text-green-600",
    bgColor: "bg-green-50",
    features: [
      { name: "Invoicing", icon: Receipt },
      { name: "Pay Bills", icon: CreditCard },
      { name: "Send Money", icon: Send },
      { name: "POS Management", icon: Smartphone },
    ],
    // link: "/financial",
    externalUrl: "https://www.home.paybox360.com/send-money",
  },
}

export default function Dashboard() {
  const [modalState, setModalState] = React.useState<{
    isOpen: boolean;
    moduleName: string;
    redirectUrl: string;
  }>({
    isOpen: false,
    moduleName: '',
    redirectUrl: ''
  });

  const handleViewModule = (moduleName: string, externalUrl: string) => {
    setModalState({
      isOpen: true,
      moduleName,
      redirectUrl: externalUrl
    });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      moduleName: '',
      redirectUrl: ''
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-100">
        <div className="p-6 md:px-7 lg:px-11">
          <h2 className="mb-6">
            <span className="text-sm text-gray-600 md:text-lg">Welcome back, </span>
            <span className="text-xl font-bold text-gray-900 md:text-2xl">
              <React.Suspense fallback={<span>User</span>}>
                <WelcomeHeadingName />
              </React.Suspense>{" "}
            </span>
          </h2>

          {/* Hero Card */}
          <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-white via-blue-50/50 to-indigo-50/30 border border-gray-200/50 p-8 shadow-sm">
            {/* Sophisticated Background Patterns */}
            <div className="absolute inset-0 overflow-hidden">
              {/* Geometric Grid Pattern */}
              <div className="absolute inset-0 opacity-[0.03]">
                <svg className="h-full w-full" preserveAspectRatio="none" viewBox="0 0 100 100">
                  <defs>
                    <pattern height="8" id="dashboard-grid" patternUnits="userSpaceOnUse" width="8">
                      <path d="M 8 0 L 0 0 0 8" fill="none" stroke="#032282" strokeWidth="0.5" />
                    </pattern>
                  </defs>
                  <rect fill="url(#dashboard-grid)" height="100%" width="100%" />
                </svg>
              </div>

              {/* Floating Dots */}
              <div className="absolute inset-0">
                {[...Array(8)].map((_, i) => (
                  <div
                    className="absolute w-1 h-1 bg-blue-300/40 rounded-full animate-pulse"
                    key={i}
                    style={{
                      left: `${15 + i * 12}%`,
                      top: `${20 + (i % 3) * 25}%`,
                      animationDelay: `${i * 0.5}s`,
                      animationDuration: `${3 + i * 0.5}s`,
                    }}
                  />
                ))}
              </div>

              {/* Diagonal Lines Pattern */}
              <div className="absolute top-0 right-0 w-1/3 h-full opacity-[0.02]">
                <svg className="w-full h-full" preserveAspectRatio="none" viewBox="0 0 100 100">
                  <defs>
                    <pattern height="15" id="dashboard-diagonals" patternUnits="userSpaceOnUse" width="15">
                      <path d="M0,15 L15,0" stroke="#032282" strokeWidth="1" />
                    </pattern>
                  </defs>
                  <rect fill="url(#dashboard-diagonals)" height="100%" width="100%" />
                </svg>
              </div>

              {/* Large Decorative Circle */}
              <div className="absolute -top-16 -right-16 w-32 h-32 rounded-full border border-blue-200/30">
                <div className="absolute inset-4 rounded-full border border-blue-200/20">
                  <div className="absolute inset-4 rounded-full bg-gradient-to-br from-blue-100/20 to-transparent"></div>
                </div>
              </div>

              {/* Bottom Left Accent Shape */}
              <div className="absolute -bottom-8 -left-8 w-24 h-24 opacity-[0.06]">
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <path d="M50,10 L90,50 L50,90 L10,50 Z" fill="none" stroke="#032282" strokeWidth="2" />
                  <path d="M50,25 L75,50 L50,75 L25,50 Z" fill="none" stroke="#032282" strokeWidth="1" />
                </svg>
              </div>

              {/* Subtle Wave Pattern */}
              <div className="absolute bottom-0 left-0 w-full h-16 opacity-[0.03]">
                <svg className="w-full h-full" preserveAspectRatio="none" viewBox="0 0 400 100">
                  <path d="M0,50 Q100,20 200,50 T400,50 L400,100 L0,100 Z" fill="#032282" />
                </svg>
              </div>
            </div>

            <div className="relative z-10">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="inline-flex items-center gap-2 rounded-full bg-blue-100/80 px-4 py-2 text-sm font-medium text-blue-700 mb-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    Business Dashboard
                  </div>
                  <h3 className="mb-3 text-2xl font-bold text-gray-900 ">
                    Transform Your Business Operations
                  </h3>
                  <p className="max-w-2xl text-gray-600 lg:text-sm leading-relaxed">
                    Streamline your stock, sales, and inventory management with our comprehensive business solutions.
                    Get real-time insights and make data-driven decisions.
                  </p>
                </div>

                {/* Stats Cards */}
                <div className="hidden lg:flex gap-4 ml-8">
                  <div className="bg-white/80 backdrop-blur-0 rounded-xl p-4 border border-gray-200/50 shadow-">
                    <div className="text-xl font-bold text-gray-900">2.4M</div>
                    <div className="text-xs text-gray-600">Monthly Revenue</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 shadow-sm">
                    <div className="text-xl font-bold text-gray-900">847</div>
                    <div className="text-xs text-gray-600">Active Products</div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-4 mt-6">
                <LinkButto className="inline-flex items-center gap-2 rounded-xl bg-[#032282] px-6 py-3 text-sm font-medium text-white shadow-sm transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M13 7l5 5m0 0l-5 5m5-5H6" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
                  </svg>
                  Create company
                </button>
                <LinkButton href={'/analytics/select-company'} className="inline-flex items-center gap-2 rounded-xl bg-white border-[#03238293]  border-[0.3px] px-6 py-3 text-sm font-medium text-gray-700 hover:bg-white transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                    />
                  </svg>
                  View Analytics
                </LinkButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Modules Section */}
      <div className="px-6 py-12 md:px-7 lg:px-11">
        <div className="mb-8">
          <h2 className="mb-2 text-2xl font-bold text-gray-900">Core Business Operations</h2>
          <p className="text-gray-600 text-[13px]">Manage your essential business functions with powerful, integrated tools</p>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {featuredModules.map((module) => (
            <div className="group relative" key={module.name}>
              <LinkButton
                className="block h-full w-full rounded-2xl bg-white p-0 shadow-sm ring-1 ring-gray-200 transition-all duration-300 hover:ring-2 hover:ring-blue-500/20 text-left"
                href={module.link}
                size="unstyled"
                variant="unstyled"
              >
                <div className={cn("rounded-t-2xl bg-gradient-to-br p-6", module.bgGradient)}>
                  <div className="flex items-center justify-between">
                    <div className={cn("rounded-xl bg-gradient-to-r p-3 text-white shadow-lg", module.gradient)}>
                      <module.icon className="h-4 w-4" />
                    </div>
                    <div className="text-right">
                      <div className="text-[13px] font-medium text-gray-600">{module.stats.label}</div>
                      <div className="text-base font-bold text-gray-900">{module.stats.value}</div>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="mb-2 text-xl font-semibold text-gray-900">{module.name}</h3>
                  <p className="mb-4 text-sm text-gray-600 leading-relaxed">{module.description}</p>

                  <div className="mb-6">
                    <div className="grid grid-cols-2 gap-2">
                      {module.features.map((feature) => (
                        <div className="flex items-center gap-2 text-xs text-gray-500" key={feature}>
                          <div className="h-1.5 w-1.5 rounded-full bg-gray-300"></div>
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-[#032282]">Get Started</span>
                    <div className="rounded-full bg-blue-50 p-2 transition-colors group-hover:bg-blue-100">
                      <svg className="h-4 w-4 text-[#032282]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path d="M9 5l7 7-7 7" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
                      </svg>
                    </div>
                  </div>
                </div>
              </LinkButton>
            </div>
          ))}
        </div>
      </div>

      {/* Other Modules Section */}
      <div className="bg-gray-50 px-6 py-12 md:px-7 lg:px-11">
        <div className="mb-8">
          <h2 className="mb-2 text-2xl font-bold text-gray-900">Additional Modules</h2>
          <p className="text-gray-600 text-[13px]">Explore our comprehensive suite of business management tools</p>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {Object.entries(otherModules).map(([categoryName, category]) => (
            <div className="rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-200" key={categoryName}>
              <div className="mb-6">
                <div className={cn("mb-4 inline-flex rounded-xl p-3", category.bgColor)}>
                  <category.icon className={cn("h-6 w-6", category.color)} />
                </div>
                <h3 className="mb-2 text-xl font-semibold text-gray-900">{categoryName}</h3>
                <button
                  className="inline-flex items-center gap-2 text-sm font-medium text-[#032282] hover:text-[#032282]/80 transition-colors"
                  onClick={() => handleViewModule(categoryName, category.externalUrl)}
                >
                  View Module
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path d="M9 5l7 7-7 7" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
                  </svg>
                </button>
              </div>

              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700">Key Features:</h4>
                <div className="grid grid-cols-1 gap-2">
                  {category.features.slice(0, 6).map((feature) => (
                    <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-2 text-sm" key={feature.name}>
                      <feature.icon className="h-4 w-4 text-gray-400" />
                      <span className="text-gray-700">{feature.name}</span>
                    </div>
                  ))}
                  {category.features.length > 6 && (
                    <div className="text-xs text-gray-500 pt-2">+{category.features.length - 6} more features</div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions Section */}
      <div className="px-6 py-12 md:px-7 lg:px-11">
        <div className="rounded-2xl bg-gradient-to-r from-[#032282] to-[#032382a8] p-8 text-white">
          <div className="flex flex-col items-center text-center lg:flex-row lg:text-left">
            <div className="flex-1">
              <h3 className="mb-2 text-2xl font-bold">Ready to get started?</h3>
              <p className="text-blue-100">Set up your business operations in minutes with our guided setup process.</p>
            </div>
            <div className="mt-6 flex gap-4 lg:mt-0">
              <LinkButton
                className="rounded-xl bg-white px-6 py-3 font-medium text-blue-600 hover:bg-blue-50"
                href="/setup"
                size="unstyled"
                variant="unstyled"
              >
                Create company
              </LinkButton>
              <LinkButton
                className="rounded-xl border border-white/20 px-6 py-3 font-medium text-white hover:bg-white/10"
                href="/support"
                size="unstyled"
                variant="unstyled"
              >
                Get Support
              </LinkButton>
            </div>
          </div>
        </div>
      </div>

      {/* Redirecting Modal */}
      <RedirectingModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        moduleName={modalState.moduleName}
        redirectUrl={modalState.redirectUrl}
      />
    </div>
  )
}
