'use client'

import React from 'react';

import { CreateSupplierModal } from '../../misc/components/modals';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { LoaderModal } from '@/components/core';

const Page = () => {
  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();

  return (
    <>
      {isCompanyLoading ?
        <LoaderModal isOpen={true} /> :
        <CreateSupplierModal
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          companies={companies?.results} />
      }
    </>
  );
};

export default Page;
