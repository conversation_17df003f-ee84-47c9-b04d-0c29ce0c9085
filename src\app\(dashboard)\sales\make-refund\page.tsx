'use client';

import clsx from 'clsx';
import { format as formatDate, parseISO } from 'date-fns';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';
import { shallow } from 'zustand/shallow';

import { RefundModal } from '@/app/(dashboard)/sales/misc/components/modals/RefundModal';
import {
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  LoaderModal,
} from '@/components/core';
import { useBooleanStateControl } from '@/hooks';
import { useSwapStore } from '@/stores/swapStore';
import { addCommasToNumber } from '@/utils/numbers';

import { CustomerMultiSelectCombobox } from '../misc/components/CustomerMultiSelectCombobox';
import { InvoiceItemsTable } from '../misc/components/tables/InvoiceItemsTable';
import { InvoiceItemsDto, SearchedInvoicesDto } from '../misc/types';
import { ViewBranchesDialog } from '../../stock/misc/components';
import { useCompaniesList } from '../../instant-web/misc/api';
import { useDefaultCompanyBranch } from '../../instant-web/misc/api/companies/getDefaultBranch';

const Page = () => {
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();


  const { setInvoiceItemsPayload } = useSwapStore(
    state => ({
      invoiceItemsPayload: state.invoiceItemsPayload,
      setInvoiceItemsPayload: state.setInvoiceItemsPayload,
      // removeRestockPayloadPerId: state.removeRestockPayloadPerId,
    }),
    shallow
  );

  const branchId = searchParams.get('branch') || '';
  const companyId = searchParams.get('company') || '';

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();
  const { data: salesUserDetails, isLoading: isDefaultLoading } = useDefaultCompanyBranch();

  useEffect(() => {
    if (!companyId && salesUserDetails?.data) {
      router.replace(`${pathname}?company=${salesUserDetails?.data.company_id}&branch=${salesUserDetails?.data.branch_id}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesUserDetails?.data.company_id])



  const {
    state: isAddInvoiceOpen,
    setState: setAddInvoiceState,
    setTrue: openAddInvoice,
  } = useBooleanStateControl();

  const {
    state: isRefundModalOpen,
    setState: setRefundModalState,
    setTrue: openRefundModal,
  } = useBooleanStateControl();

  const [selectedCustomerRecords, setSelectedCustomerRecords] = React.useState<
    SearchedInvoicesDto[]
  >([]);

  const [selectedInvoiceItems, setSelectedInvoiceItems] = React.useState<
    InvoiceItemsDto[] | undefined
  >();

  const emptyRestockCountRef = React.useRef(1);

  const [selectedInvoiceId, setSelectedInvoiceId] = React.useState('');

  const [selectedCustomerRow, setSelectedCustomerRow] =
    React.useState<SearchedInvoicesDto>();

  const handleAddOrRemoveCustomer = (newCategory: SearchedInvoicesDto) => {
    const newProductExists = selectedCustomerRecords.findIndex(
      obj => obj.invoice_id === newCategory.invoice_id
    );

    if (newProductExists != -1) {
      const newRecord = selectedCustomerRecords?.filter(
        item => item.invoice_id != newCategory.invoice_id
      );

      setSelectedCustomerRecords(newRecord);
    } else {
      setSelectedCustomerRecords(prevState => [...prevState, newCategory]);
    }
  };

  useEffect(() => {
    if (!!selectedCustomerRecords.length) {
      setSelectedInvoiceId(selectedCustomerRecords[0].invoice_reference);
      setSelectedCustomerRow(selectedCustomerRecords[0]);
    } else {
      setSelectedInvoiceId('');
      setSelectedCustomerRow(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCustomerRecords.length]);

  const customerId = selectedCustomerRow?.customer_id;

  return (
    <>
      <LoaderModal isOpen={isDefaultLoading || !companyId} />
      {branchId ?
        <div className="mt-4 md:px-7 lg:px-11">
          <div className="flex items-center justify-between rounded-[10px] bg-[#ECF1FF] p-4">
            <p className="text-sm text-[#242424]">
              Click to enter the customer’s name or the invoice no
              <br /> to log return/refund
            </p>

            <div>
              <Button className="flex items-center" onClick={openAddInvoice}>
                <span className="inline-block">
                  <svg
                    fill="none"
                    height="18"
                    viewBox="0 0 19 18"
                    width="19"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M8.7085 8.25V3.75H10.2918V8.25H15.0418V9.75H10.2918V14.25H8.7085V9.75H3.9585V8.25H8.7085Z"
                      fill="#FDFDFD"
                      fillOpacity="0.992157"
                    />
                  </svg>
                </span>
                <span className="inline-block">Add Name/Invoice no</span>
              </Button>
            </div>
          </div>

          <div className="my-4 rounded-[10px] bg-[#EBEFFB] p-4">
            <div className="flex w-full grid-cols-12 flex-col gap-x-4 rounded-[12px] bg-faded-white px-4 py-6 lg:grid">
              <div className="col-span-3 lg:border-r">
                <p className="text-sm font-[500]">
                  {selectedCustomerRow?.name || ''}
                </p>
                <p className="text-xs">Name</p>
              </div>

              <div className="col-span-3 lg:border-r">
                <p className="text-sm font-[500]">
                  {selectedCustomerRow?.email || ''}
                </p>
                <p className="text-xs">Email</p>
              </div>
              <div className="col-span-3">
                <p className="text-sm font-[500]">
                  {selectedCustomerRow?.phone || ''}
                </p>
                <p className="text-xs">Phone Number</p>
              </div>
            </div>
          </div>

          <Dialog open={isAddInvoiceOpen} onOpenChange={setAddInvoiceState}>
            <DialogTrigger
              className="hidden whitespace-nowrap px-4"
              variant="light"
            >
              View details
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Return/Refund</DialogTitle>
                <DialogClose>Close</DialogClose>
              </DialogHeader>

              <div className=" my-6 px-6">
                <div>
                  <CustomerMultiSelectCombobox
                    branchId={branchId}
                    companyId={companyId}
                    handleAddOrRemoveCustomer={handleAddOrRemoveCustomer}
                    id="category"
                    placeholder="Search Name or Invoice"
                    products={selectedCustomerRecords}
                  />
                </div>

                <div className="my-4">
                  <Button
                    className="py-3"
                    disabled={!selectedCustomerRecords.length}
                    size={'fullWidth'}
                    onClick={() => setAddInvoiceState(false)}
                  >
                    Okay
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <section className="flex min-h-[200px] grid-cols-12 flex-col items-stretch gap-4 lg:grid">
            <div className="col-span-6 h-auto rounded-[10px] bg-white p-4">
              <table className="mt-4 w-full border-collapse">
                <thead className="">
                  <tr className="">
                    <th className="p-2 text-left">S/N</th>
                    <th className="p-2 text-left">Date</th>
                    <th className="p-2 text-left">Invoice </th>
                    <th className="p-2 text-left">Total</th>
                    <th className="p-2 text-left">Action</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedCustomerRecords.map((item, index) => {
                    const date = parseISO(item.created_at);
                    const formatted = formatDate(date, 'd/M/yyyy - hh:mma');

                    return (
                      <tr
                        className={clsx(
                          selectedCustomerRow?.invoice_reference ==
                          item.invoice_reference && 'bg-gray-100',
                          selectedCustomerRecords.length - 1 == index && 'border-b'
                        )}
                        key={item.invoice_id}
                      >
                        <td className=" p-2 py-6 text-left text-sm">{++index}</td>
                        <td className=" p-2 text-left text-sm">{formatted}</td>
                        <td className=" p-2 text-left text-sm">
                          {item.invoice_reference}
                        </td>
                        <td className=" p-2 text-left text-sm">
                          ₦{addCommasToNumber(Number(item.amount))}
                        </td>

                        <td className=" p-2 text-left text-sm">
                          <Button
                            onClick={() => {
                              setSelectedInvoiceId(item.invoice_reference);
                              setSelectedCustomerRow(item);
                            }}
                          >
                            View Details
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>

              {!selectedCustomerRecords.length ? (
                <div className="my-10 flex flex-col items-center justify-center">
                  <div>
                    <svg
                      fill="none"
                      height="118"
                      viewBox="0 0 119 118"
                      width="119"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect
                        fill="white"
                        height="95.1529"
                        rx="13.1078"
                        stroke="#E2E6EE"
                        strokeWidth="0.970948"
                        transform="rotate(-10.9436 0.568809 23.3298)"
                        width="88.3562"
                        x="0.568809"
                        y="23.3298"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        transform="rotate(-10.9436 15.6787 42.6592)"
                        width="63.1116"
                        x="15.6787"
                        y="42.6592"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        transform="rotate(-10.9436 19.7344 63.6328)"
                        width="35.9251"
                        x="19.7344"
                        y="63.6328"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        transform="rotate(-10.9436 23.7891 84.6045)"
                        width="35.9251"
                        x="23.7891"
                        y="84.6045"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        transform="rotate(-10.9436 59.7725 55.8906)"
                        width="22.3318"
                        x="59.7725"
                        y="55.8906"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        transform="rotate(-10.9436 63.8271 76.8613)"
                        width="22.3318"
                        x="63.8271"
                        y="76.8613"
                      />
                      <rect
                        fill="white"
                        height="95.1529"
                        rx="13.1078"
                        stroke="#E2E6EE"
                        strokeWidth="0.970948"
                        width="88.3562"
                        x="30.1583"
                        y="0.485474"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        width="63.1116"
                        x="41.3242"
                        y="22.332"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        width="35.9251"
                        x="41.3242"
                        y="43.6924"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        width="35.9251"
                        x="41.3242"
                        y="65.0527"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        width="22.3318"
                        x="82.1035"
                        y="43.6924"
                      />
                      <rect
                        fill="#E2E6EE"
                        height="9.70948"
                        rx="4.85474"
                        width="22.3318"
                        x="82.1035"
                        y="65.0527"
                      />
                    </svg>
                  </div>

                  <div className="space-y-1">
                    <p className="text-center text-xs">No data to display yet</p>

                    <Button className="flex items-center" onClick={openAddInvoice}>
                      <span className="inline-block">
                        <svg
                          fill="none"
                          height="18"
                          viewBox="0 0 19 18"
                          width="19"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.7085 8.25V3.75H10.2918V8.25H15.0418V9.75H10.2918V14.25H8.7085V9.75H3.9585V8.25H8.7085Z"
                            fill="#FDFDFD"
                            fillOpacity="0.992157"
                          />
                        </svg>
                      </span>
                      <span className="inline-block">Add Name/Invoice no</span>
                    </Button>
                  </div>
                </div>
              ) : null}
            </div>

            <div className="col-span-6 rounded-[10px] bg-white p-4">
              <div className="flex items-baseline justify-between">
                <div className="my-2 space-y-1">
                  <p className="text-sm">
                    <span className="">Reference:</span>
                    <span className="text-sm font-[700]">
                      {' '}
                      {selectedCustomerRow?.invoice_reference}
                    </span>
                  </p>

                  <p className="text-sm">
                    <span className="">Date:</span>
                    <span className="text-sm font-[700]">
                      {' '}
                      {selectedCustomerRow?.created_at &&
                        formatDate(
                          parseISO(selectedCustomerRow?.created_at),
                          'd/M/yyyy - hh:mma'
                        )}
                    </span>
                  </p>
                </div>

                <div className="flex items-center justify-start gap-2">
                  <Button
                    disabled={!selectedInvoiceItems?.length}
                    variant={'outlined'}
                    onClick={() => {
                      setInvoiceItemsPayload(selectedInvoiceItems);
                      router.push(
                        `/sales/make-refund/swap-item?invoice=${selectedCustomerRow?.invoice_id}&customer=${customerId}&branch=${branchId}&company=${companyId}`
                      );
                    }}
                  // onClick={openSwapModal}
                  >
                    <span className="inline-block">
                      <svg
                        fill="none"
                        height="18"
                        viewBox="0 0 18 18"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M13.0651 3.30758H4.04258L5.45256 1.89762C5.67006 1.68012 5.67006 1.32008 5.45256 1.10258C5.23506 0.885078 4.87506 0.885078 4.65756 1.10258L2.28756 3.4726C2.23506 3.5251 2.19758 3.5851 2.16758 3.6526C2.13758 3.7201 2.12256 3.79508 2.12256 3.87008C2.12256 3.94508 2.13758 4.02011 2.16758 4.08761C2.19758 4.15511 2.23506 4.2151 2.28756 4.2676L4.65756 6.63758C4.77006 6.75008 4.91256 6.80261 5.05506 6.80261C5.19756 6.80261 5.34006 6.75008 5.45256 6.63758C5.67006 6.42008 5.67006 6.06008 5.45256 5.84258L4.04258 4.43258H13.0651C13.9951 4.43258 14.7526 5.19008 14.7526 6.12008V8.61009C14.7526 8.91759 15.0076 9.17259 15.3151 9.17259C15.6226 9.17259 15.8776 8.91759 15.8776 8.61009V6.12008C15.8776 4.56758 14.6176 3.30758 13.0651 3.30758Z"
                          fill="#032282"
                        />
                        <path
                          d="M15.8776 14.1297C15.8776 14.0547 15.8626 13.9796 15.8326 13.9121C15.8026 13.8446 15.7651 13.7846 15.7126 13.7321L13.3426 11.3622C13.1251 11.1447 12.7651 11.1447 12.5476 11.3622C12.3301 11.5797 12.3301 11.9397 12.5476 12.1572L13.9576 13.5672H4.93506C4.00506 13.5672 3.24756 12.8097 3.24756 11.8797V9.38965C3.24756 9.08215 2.99256 8.82715 2.68506 8.82715C2.37756 8.82715 2.12256 9.08215 2.12256 9.38965V11.8797C2.12256 13.4322 3.38256 14.6922 4.93506 14.6922H13.9576L12.5476 16.1021C12.3301 16.3196 12.3301 16.6797 12.5476 16.8972C12.6601 17.0097 12.8026 17.0621 12.9451 17.0621C13.0876 17.0621 13.2301 17.0097 13.3426 16.8972L15.7126 14.5271C15.7651 14.4746 15.8026 14.4146 15.8326 14.3471C15.8626 14.2796 15.8776 14.2047 15.8776 14.1297Z"
                          fill="#032282"
                          opacity="0.4"
                        />
                      </svg>
                    </span>
                    <span className="inline-block text-sm">Swap</span>
                  </Button>

                  <Button
                    disabled={!selectedInvoiceItems?.length}
                    onClick={openRefundModal}
                  >
                    <span className="inline-block">
                      <svg
                        fill="none"
                        height="18"
                        viewBox="0 0 18 18"
                        width="18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M14.4752 5.93962V9.80214C14.4752 12.1121 13.1552 13.1021 11.1752 13.1021H4.58271C4.24521 13.1021 3.92271 13.0722 3.62271 13.0047C3.43521 12.9747 3.25522 12.9222 3.09022 12.8622C1.96522 12.4422 1.28271 11.4671 1.28271 9.80214V5.93962C1.28271 3.62962 2.60271 2.63965 4.58271 2.63965H11.1752C12.8552 2.63965 14.0627 3.35214 14.3852 4.97964C14.4377 5.27964 14.4752 5.58712 14.4752 5.93962Z"
                          fill="#FDFDFD"
                          fillOpacity="0.992157"
                          opacity="0.4"
                        />
                        <path
                          d="M16.7251 8.19044V12.053C16.7251 14.363 15.4051 15.3529 13.4251 15.3529H6.83256C6.27756 15.3529 5.77506 15.278 5.34007 15.113C4.44756 14.783 3.84006 14.1005 3.62256 13.0055C3.92256 13.073 4.24506 13.1029 4.58256 13.1029H11.1751C13.1551 13.1029 14.4751 12.113 14.4751 9.80296V5.94044C14.4751 5.58794 14.4451 5.27297 14.3851 4.98047C15.8101 5.28047 16.7251 6.28544 16.7251 8.19044Z"
                          fill="#FDFDFD"
                          fillOpacity="0.992157"
                        />
                        <path
                          d="M7.87503 9.85455C8.96855 9.85455 9.85504 8.96807 9.85504 7.87454C9.85504 6.78102 8.96855 5.89453 7.87503 5.89453C6.78151 5.89453 5.89502 6.78102 5.89502 7.87454C5.89502 8.96807 6.78151 9.85455 7.87503 9.85455Z"
                          fill="#FDFDFD"
                          fillOpacity="0.992157"
                        />
                        <path
                          d="M3.58496 6.1875C3.27746 6.1875 3.02246 6.4425 3.02246 6.75V9C3.02246 9.3075 3.27746 9.5625 3.58496 9.5625C3.89246 9.5625 4.14746 9.3075 4.14746 9V6.75C4.14746 6.4425 3.89996 6.1875 3.58496 6.1875Z"
                          fill="#FDFDFD"
                          fillOpacity="0.992157"
                        />
                        <path
                          d="M12.1577 6.1875C11.8502 6.1875 11.5952 6.4425 11.5952 6.75V9C11.5952 9.3075 11.8502 9.5625 12.1577 9.5625C12.4652 9.5625 12.7202 9.3075 12.7202 9V6.75C12.7202 6.4425 12.4727 6.1875 12.1577 6.1875Z"
                          fill="#FDFDFD"
                          fillOpacity="0.992157"
                        />
                      </svg>
                    </span>
                    <span className="inline-block text-sm">Refund</span>
                  </Button>

                  {/* <SwapModal
                branchId={branchId}
                companyId={companyId}
                emptyRestockCountRef={emptyRestockCountRef}
                invoiceId={selectedCustomerRow?.invoice_id || ''}
                isModalOpen={isSwapModalOpen}
                selectedInvoiceItems={selectedInvoiceItems}
                setStateAction={setSwapModalState}
              /> */}

                  <RefundModal
                    branchId={branchId}
                    companyId={companyId}
                    customerId={customerId}
                    emptyRestockCountRef={emptyRestockCountRef}
                    invoiceId={selectedCustomerRow?.invoice_id || ''}
                    isModalOpen={isRefundModalOpen}
                    selectedInvoiceItems={selectedInvoiceItems}
                    setStateAction={setRefundModalState}
                  />
                </div>
              </div>
              <InvoiceItemsTable
                branchId={branchId}
                companyId={companyId}
                referenceId={selectedInvoiceId}
                selectedInvoiceItems={selectedInvoiceItems}
                setSelectedInvoiceItems={setSelectedInvoiceItems}
              />
            </div>
          </section>
        </div>
        :
        <>
          {!isCompanyLoading && <ViewBranchesDialog company={companyId}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            companyList={companies?.results} link={'/sales/make-refund'} />}
        </>
      }
    </>
  );
};

export default Page;
