'use client';

import {
  <PERSON><PERSON>,
  LinkButton,
  Popover,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from '@/components/core';
import { useBooleanStateControl, useRouteChangeEvent } from '@/hooks';
import { CompanyListEntity } from '@/app/(dashboard)/payroll/misc/types';
import { ModuleSubscriptionModal } from '@/app/(dashboard)/payroll/misc/components/headerCompanyPayrollSwitch';
import { addCommasToNumber } from '@/utils/numbers';
import { convertNumberToNaira } from '@/utils/currency';

import { usePathname, useRouter } from 'next/navigation';

import * as React from 'react';

import { useSearchParams } from 'next/navigation';

import { Coins } from 'lucide-react';

import { useCompanies } from '../api';

import TokenInsufficientModal from './modals/TokenInsufficientModal';

const pathsWithCompanySwitcher = [
  '/sales/invoicing',
  '/sales/sell',
  '/sales/create-sales',
  '/sales/customers',
  '/sales/make-refund',
  '/sales/return-refund',
  '/sales/team-members',
  '/sales/teams',
  '/sales/dashboard',
  '/sales/transactions',


];
const pathsWithoutCompanySwitcher = [
  '/sales/company-overview',
  '/sales'
];

const fetchOptions = {
  pageIndex: 1,
  pageSize: 50,
  search: undefined,
};

export function HeaderCompanySalesSwitcher() {
  const { data: companiesResponse } = useCompanies(fetchOptions);

  const pathname = usePathname();
  const params = useSearchParams();
  const companyName = params.get("companyName")
  const companyId = params.get('company');
  const [selectedCompany, setSelectedCompany] = React.useState<CompanyListEntity | null>(null);
  const [showModuleModal, setShowModuleModal] = React.useState(false);
  const [showTokenModal, setShowTokenModal] = React.useState(false);
  const [tokenModalCompany, setTokenModalCompany] = React.useState<CompanyListEntity | null>(null);

  const {
    state: isPopoverOpen,
    setState: setPopoverState,
    setFalse: closePopover,
  } = useBooleanStateControl();

  useRouteChangeEvent(() => closePopover());

  const router = useRouter();

  const hasSwitcher = React.useMemo(
    () =>
      pathsWithCompanySwitcher.some(page => pathname.includes(page)) ||
      pathsWithCompanySwitcher.includes(pathname),
    [pathname]
  );

  const hasNoSwitcher = React.useMemo(
    () =>
      pathsWithoutCompanySwitcher.includes(pathname),
    [pathname]
  );

  const shouldShowSwitcher = hasSwitcher && !hasNoSwitcher;

  const { results: companies } = companiesResponse || {};
  const sortedCompanies = companies?.sort((a, b) => {
    if (a.default && !b.default) return -1; // a with default true comes before b with default false
    if (!a.default && b.default) return 1;  // b with default true comes before a with default false
    if (a.id === companyId) return -1;       // a with matching id comes before b
    if (b.id === companyId) return 1;        // b with matching id comes before a
    return 0;                               // No change in order for other cases
  });

  const companyNameAlt = companyName || companies?.find(company => company.id === companyId)?.company_name

  const handleCompanyClick = (company: CompanyListEntity) => {
    if (!company.is_subscription) {
      router.push(`/subscription?companyId=${company.id}&companyName=${company.company_name}`);
      return;
    }

    const allModules = [
      ...company.active_subscriptions.flatMap(sub =>
        sub.modules.map(module => ({
          ...module,
          subscription: sub,
          plan_id: module.plan_id
        }))
      ),
      ...company.not_started_subscriptions.flatMap(sub =>
        sub.modules.map(module => ({
          ...module,
          subscription: sub,
          plan_id: module.plan_id
        }))
      )
    ];

    const _hasSalesModule = allModules.some(module =>
      module.module.code === 'SALES' && module.is_active
    );

    // if (!hasSalesModule) {
    //   setSelectedCompany(company);
    //   setShowModuleModal(true);
    //   return;
    // }

    // Check if user has sufficient tokens
    const hasTokens = company.token_balance_info && company.token_balance_info.current_balance > 0;

    if (!hasTokens) {
      setTokenModalCompany(company);
      setShowTokenModal(true);
      return;
    }

    router.push(`${pathname}?company=${company.id}&companyName=${company.company_name || ''}`);
  };

  const getSubscriptionStatus = (company: CompanyListEntity) => {
    const hasNotStartedSubscriptions = company?.not_started_subscriptions?.length > 0;

    if (hasNotStartedSubscriptions) {
      return {
        text: "Pending Payment",
        className: "text-amber-600 text-xxs"
      };
    }
    if (company.is_free_trial) {
      return {
        text: "Free Trial",
        className: "text-amber-600 text-xxs"
      };
    }
    if (company.is_paid_subscription) {
      return {
        text: "Paid Subscription",
        className: "text-green-600 text-xxs"
      };
    }
    if (company.is_subscription) {
      return {
        text: "Subscribed",
        className: "text-green-600 text-xxs"
      };
    }
    return {
      text: "Not Subscribed",
      className: "text-red-600 text-xxs"
    };
  };
  return !!companies && shouldShowSwitcher ? (
    <>
      <Popover open={isPopoverOpen} onOpenChange={setPopoverState}>
        <PopoverTrigger className="rounded-10 bg-white py-1 text-sm">
          <span className="rounded-lg bg-blue-700/10 px-3 py-1.5 text-xs text-main-solid md:bg-main-bg">
            Switch Company: <span className='uppercase font-semibold'>{companyNameAlt}</span>
          </span>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          className="mt-2 w-full rounded-10 border-none p-0 pb-2 text-xs [@media(min-width:408px)]:max-w-[18.375rem]"
        >
          <div className='py-[11px] px-4 bg-[#EBF1FF] mt-3 flex items-center justify-between gap-2 w-full'>
            <p className='text-main-solid text-xxs'>Tap to <span className='font-bold'>change</span> default company</p>

            <LinkButton className='bg-[#DAE3FF] rounded-xl py-[5px] px-4 text-main-solid text-xxs' href={'/stock/set-company-branch-stock'}>Change</LinkButton>
          </div>
          <p className="px-4 pb-2.5 pt-4">Select company to change view.</p>
          {/* <ul className="space-y-1 px-2">
            {sortedCompanies?.map(({ id, company_name, default: isDefaultCompany }) => (
              <li key={id}>
                <LinkButton
                  className="flex rounded-[.75rem] px-6 py-2.5 text-left text-label-text transition ease-in-out hover:bg-[#F8FAFF] justify-between"
                  href={`${pathname}?company=${id}&companyName=${company_name || ''}`}
                  size="lg"
                  variant="white"
                >
                  <span>{company_name}</span>
                  {isDefaultCompany && <span className='bg-main-bg text-main-solid py-1 px-2 rounded-xl'>default</span>}
                </LinkButton>
              </li>
            ))}
          </ul> */}
          <ul className="space-y-1 px-2">
            {sortedCompanies?.map((company) => {
              const status = getSubscriptionStatus(company);
              return (
                <li key={company.id}>
                  <Button
                    className="block w-full rounded-[.75rem] px-6 py-2.5 text-left text-label-text transition ease-in-out hover:bg-[#F8FAFF]"
                    variant="white"
                    onClick={() => handleCompanyClick(company)}
                  >
                    <div className="flex flex-col gap-1">
                      <span>{company.company_name}</span>
                      {/* <span className={status.className}>{status.text}</span> */}

                      {/* Token Balance Display */}
                      {company.token_balance_info && (
                        <div className="flex items-center gap-2 mt-1">
                          <div className="flex items-center gap-1">
                            <Coins className="h-3 w-3 text-[#032282]" />
                            <span className="text-xs font-medium text-[#032282]">
                              {addCommasToNumber(company.token_balance_info.current_balance)} tokens
                            </span>
                          </div>
                          <span className="text-xs text-gray-500">
                            ({convertNumberToNaira(company.token_balance_info.current_balance * 10)})
                          </span>
                          {/* <span className={`text-xs px-2 py-0.5 rounded-full ${company.token_balance_info.status === 'active'
                            ? 'bg-green-100 text-green-700'
                            : 'bg-gray-100 text-gray-600'
                            }`}>
                            {company.token_balance_info.status}
                          </span> */}
                        </div>
                      )}

                      <span>{company.default && <span className='bg-main-bg text-main-solid py-1 px-2 rounded-xl'>default</span>}
                      </span>
                    </div>
                  </Button>
                </li>
              );
            })}
          </ul>
        </PopoverContent>
      </Popover>
      {
        selectedCompany && (
          <ModuleSubscriptionModal
            company={selectedCompany}
            isOpen={showModuleModal}
            onClose={() => {
              setShowModuleModal(false);
              setSelectedCompany(null);
            }}
          />
        )
      }

      {
        tokenModalCompany && (
          <TokenInsufficientModal
            isOpen={showTokenModal}
            onClose={() => {
              setShowTokenModal(false);
              setTokenModalCompany(null);
            }}
            companyId={tokenModalCompany.id}
            companyName={tokenModalCompany.company_name}
            currentBalance={tokenModalCompany.token_balance_info?.current_balance || 0}
            module="Sales"
            onProceedAnyway={() => {
              router.push(`${pathname}?company=${tokenModalCompany.id}&companyName=${tokenModalCompany.company_name || ''}`);
              setShowTokenModal(false);
              setTokenModalCompany(null);
            }}
          />
        )
      }
    </>
  ) : null;
}