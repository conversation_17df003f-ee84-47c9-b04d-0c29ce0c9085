'use client'

import { <PERSON><PERSON>, ClientOnly, Dialog, DialogBody, DialogClose, DialogContent, DialogHeader, DialogTitle, ErrorModal, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SuccessModal } from '@/components/core'
import { Input } from '@/components/ui';
import React, { ReactNode } from 'react'
import { UsePostAccountChart } from '../../api/postCreateChart';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { AxiosError } from 'axios';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { UseGetAccountType } from '../../api/getAccountType';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';
import { useSearchParams } from 'next/navigation';


const chartSchema = z.object({
  account_type: z.string().min(1, 'Account type is required'),
  name: z.string().min(1, 'Account name is required'),
  statement_type: z.string().min(1, 'Statement type is required'),
  statement_category: z.string().min(1, 'Statement category is required'),
  company: z.string().min(1, 'Company is required'),
});

type ChartFormData = z.infer<typeof chartSchema>;

interface CreateChartModalProps {
  isCreateChartModal: boolean;
  setIsCreateChartModal: React.Dispatch<React.SetStateAction<boolean>>;
  heading: string;
  subsection: ReactNode;
  refreshChartTable: () => void;
}

const STATEMENT_CATEGORY_OPTIONS =
  ["Current Assets", "Non-Current Assets", "Cash and Cash Equivalents", "Current Liabilities", "Non-Current Liabilities", "Equity", "Cost of Goods Sold", "Revenue", "Operating Expenses", "Other Income", "Other Expenses"]

const STATEMENT_TYPE_OPTIONS = ["Balance Sheet", "Income Statement"]


function CreateChartModal({
  isCreateChartModal,
  setIsCreateChartModal,
  heading,
  subsection,
  refreshChartTable,
}: CreateChartModalProps) {
  // const { data: companyList } = useCompaniesList()

  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
  } = useBooleanStateControl();

  const { data: getAccType } = UseGetAccountType()

  const all_account_type = getAccType?.map(({ id, name }: { id: string; name: string }) => ({
    id,
    name,
  }));


  const { data: allCompanies } = useCompaniesList()


  const get_all_company_list = allCompanies?.results?.map(({ id: company_id, company_name }: { id: string; company_name: string }) => ({
    company_id,
    company_name,
  }))

  const { data: salesUserDetails } = useDefaultCompanyBranch();
  React.useEffect(() => {
    //
  }, [])

  const params = useSearchParams();

  const companyname = params.get('company') || salesUserDetails?.data.company_id;

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const handleSuccessOkay = () => {
    setSuccessModalState(false);
    setIsCreateChartModal(false);
    refreshChartTable();
  };


  const { mutate: createChart } = UsePostAccountChart()

  const { control, handleSubmit, formState: { errors } } = useForm<ChartFormData>({
    resolver: zodResolver(chartSchema),
    defaultValues: {
      company: `${companyname}` || "",
      name: "",
      statement_category: "Current Assets",
      statement_type: "Balance Sheet",
    }
  });
  const onSubmit = (data: ChartFormData) => {
    createChart(data, {
      onSuccess: () => {
        // setIsCreateChartModal(false)
        setSuccessModalState(true)
      },
      onError: (error: unknown) => {
        const errorMessage = formatAxiosErrorMessage(error as AxiosError);
        openErrorModalWithMessage(errorMessage);
      },
    });
  };

  return (
    <div>
      <ClientOnly>
        {isCreateChartModal && !isSuccessModalOpen && (
          <Dialog open={isCreateChartModal}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{heading}</DialogTitle>
                <DialogClose><button onClick={() => setIsCreateChartModal(false)}>close</button></DialogClose>
              </DialogHeader>
              <DialogBody>
                <section className=''>
                  <p>{subsection}</p>
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <div className={`relative mt-3`}>
                      <label className="mb-1 text-xs">
                        Account Type <span className='text-red-700'>*</span>
                      </label>
                      <Controller
                        control={control}
                        name="account_type"
                        render={({ field: { onChange, value, ref } }) => (
                          <Select value={value} onValueChange={onChange}>
                            <SelectTrigger
                              className='h-12 rounded-lg'
                              iconClassName="fill-black"
                              id="account_type"
                              ref={ref}
                            >
                              <SelectValue className='text-black' placeholder="Select Account Type" />
                            </SelectTrigger>
                            <SelectContent>
                              {all_account_type?.map(({ id, name }) => (
                                <SelectItem key={id} value={id}>
                                  {name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                      {errors.account_type && <p className="text-red-500 text-xs mt-1">{errors.account_type.message}</p>}
                    </div>

                    <div className={`relative mt-3`}>
                      <label className="mb-1 text-xs">
                        Account Name <span className='text-red-700'>*</span>
                      </label>
                      <Controller
                        control={control}
                        name="name"
                        render={({ field }) => (
                          <Input
                            {...field}
                            className='h-12 rounded-lg'
                            placeholder="Enter account name"
                            type="text"
                          />
                        )}
                      />
                      {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>}
                    </div>

                    <div className={`relative mt-3`}>
                      <label className="mb-1 text-xs">
                        Statement Type <span className='text-red-700'>*</span>
                      </label>
                      <Controller
                        control={control}
                        name="statement_type"
                        render={({ field: { onChange, value, ref } }) => (
                          <Select value={value} onValueChange={onChange}>
                            <SelectTrigger
                              className='h-12 rounded-lg'
                              iconClassName="fill-black"
                              id="statement_type"
                              ref={ref}
                            >
                              <SelectValue className='text-black' placeholder="Select Statement Type" />
                            </SelectTrigger>
                            <SelectContent>
                              {STATEMENT_TYPE_OPTIONS?.map((type, index) => (
                                <SelectItem key={index} value={type}>
                                  {type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                      {errors.statement_type && <p className="text-red-500 text-xs mt-1">{errors.statement_type.message}</p>}
                    </div>
                    <div className={`relative mt-3`}>
                      <label className="mb-1 text-xs">
                        Statement Category <span className='text-red-700'>*</span>
                      </label>
                      <Controller
                        control={control}
                        name="statement_category"
                        render={({ field: { onChange, value, ref } }) => (
                          <Select value={value} onValueChange={onChange}>
                            <SelectTrigger
                              className='h-12 rounded-lg'
                              iconClassName="fill-black"
                              id="statement_category"
                              ref={ref}
                            >
                              <SelectValue className='text-black' placeholder="Select Statement Category" />
                            </SelectTrigger>
                            <SelectContent>
                              {STATEMENT_CATEGORY_OPTIONS?.map((type, index) => (
                                <SelectItem key={index} value={type}>
                                  {type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                      {errors.statement_category && <p className="text-red-500 text-xs mt-1">{errors.statement_category.message}</p>}
                    </div>
                    <div className="relative mt-4">
                      <label className="mb-1 text-xs">Company</label>
                      <Controller
                        control={control}
                        name="company"
                        render={({ field: { onChange, value, ref } }) => (
                          <Select value={value} onValueChange={onChange}>
                            <SelectTrigger
                              className='h-12 rounded-lg'
                              iconClassName="fill-black"
                              id="company"
                              ref={ref}
                            >
                              <SelectValue className='text-black' placeholder="Select Company" />
                            </SelectTrigger>
                            <SelectContent>
                              {get_all_company_list?.map(({ company_id, company_name }) => (
                                <SelectItem key={company_id} value={company_id}>
                                  {company_name}
                                </SelectItem>
                              ))}

                            </SelectContent>
                          </Select>
                        )}
                      />
                      {errors.company && <p className="text-red-500 text-xs mt-1">{errors.company.message}</p>}
                    </div>
                    <Button className='w-full py-3.5 mt-12' type="submit">Create Account</Button>
                  </form>
                </section>
              </DialogBody>
            </DialogContent>
          </Dialog>
        )}

      </ClientOnly>
      {isSuccessModalOpen &&
        <SuccessModal
          heading="Chart Created Successfully"
          isSuccessModalOpen={isSuccessModalOpen}
          setSuccessModalState={setSuccessModalState}
          subheading=""
        >
          <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
            <Button
              className="grow text-base"
              onClick={handleSuccessOkay}
            >
              Okay
            </Button>
          </div>
        </SuccessModal>
      }
      {isErrorModalOpen &&
        <ErrorModal
          isErrorModalOpen={isErrorModalOpen}
          setErrorModalState={setErrorModalState}
          subheading={errorModalMessage || 'Please check your inputs and try again.'}
        >
          <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
            <Button
              className="grow bg-red-950 text-base"
              size="lg"
              onClick={closeErrorModal}

            >
              Okay
            </Button>
          </div>
        </ErrorModal>}
    </div>
  )
}

export default CreateChartModal

