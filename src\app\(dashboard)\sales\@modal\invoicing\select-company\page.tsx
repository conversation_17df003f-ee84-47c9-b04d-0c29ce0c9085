"use client"
import { redirect } from 'next/navigation';
import React from 'react';

import { CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';
// import { Companies, CompaniesResultsEntity } from '@/app/(dashboard)/spend-management/misc/types';


import { SelectCompanyDialog } from '../../../misc/components/modals';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';
import { LoaderModal } from '@/components/core';
import { useDefaultCompanyBranch } from '@/app/(dashboard)/instant-web/misc/api/companies/getDefaultBranch';

const Page = () => {

  const { data: defaultData, isLoading } = useDefaultCompanyBranch()

  const { data } = useCompaniesList()
  // console.error(defaultData);

  if (defaultData?.data.company_id !== null && defaultData?.data.branch_id !== null && !isLoading) {
    return redirect(`/sales/invoicing/company-overview/?companyName=${defaultData?.data.company}&company=${defaultData?.data.company_id}`);
  }

  if (isLoading) {
    return <LoaderModal isOpen={isLoading} />;
  } return (
    <>
      <SelectCompanyDialog
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companyLists={data?.results as CompaniesResultsEntity[]}
        link={'/sales/invoicing/company-overview/'}
      />
    </>
  );
};

export default Page;
