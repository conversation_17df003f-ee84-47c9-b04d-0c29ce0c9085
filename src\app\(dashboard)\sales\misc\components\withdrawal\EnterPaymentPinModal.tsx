'use client';

import type { AxiosError } from 'axios';

import React from 'react';

import PinInput from 'react-pin-input';
import {
  Button,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  ErrorModal,
  // LinkButton,
  LoaderModal,
} from '@/components/core';
import { useBooleanStateControl, useErrorModalState } from '@/hooks';
import { formatAxiosErrorMessage } from '@/utils/errors';
import { SuccessPayrollModal } from '@/app/(dashboard)/payroll/misc/components';
import { usePostWithdrawSales } from '../../api/postSalesWithdrawal';
// import { SuccessPayrollModal } from '../../../SuccessPayrollModal';
// import { usePostWithdrawEmployee } from '../../../../api';
// import { usePostWithdrawEmployer } from '../../../../api/withdrawal/postWithdrawEmployer';
// import { usePostWithdrawEmployee } from '../../../../api';

export interface WithdrawEntity {
  disburse_pin: string;
  payout_choice: string;
  amount: number;
  account_number: string;
  account_name: string;
  bank_code: string;
  bank_name: string;
}

export interface bankDetails {
  bankName: string;
  bankCode: string;
  bankAccount: string;
  bankAccountName: string;
  withdrawAmount: number;
  narration?: string;
}

interface EnterPaymentPinModalProps {
  isPinModalOpen: boolean;
  setPinModalState: React.Dispatch<React.SetStateAction<boolean>>;
  employeeBankDetails: bankDetails;
  heading: string;
  isBeneficiarySaved?: boolean;
  company: string;
  branch: string;
  withdrawal_type: string;
}

export function EnterPaymentPinModal({
  isPinModalOpen,
  setPinModalState,
  heading,
  employeeBankDetails,
  isBeneficiarySaved,
  branch,
  company,
  withdrawal_type,
}: EnterPaymentPinModalProps) {
  const {
    state: isSuccessModalOpen,
    setState: setSuccessModalState,
    setTrue: openSuccessModal,
  } = useBooleanStateControl();
  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();

  const {
    mutate: postWithdrawSales,
    isLoading: isPostWithdrawEmployeeLoading,
  } = usePostWithdrawSales();

  // {
  //   "withdrawal_type": "EXTERNAL",
  //     "company": "512e3053-ee95-4782-a53d-22842a2d7de5",
  //       "branch": "f89e7656-25b3-4e0f-981a-388680f39e69",
  //         "bank_name": "OPAY DIGITAL SERVICES LIMITED",
  //           "bank_code": "100004",
  //             "account_name": "Augustine Chukwudi Jibunoh",
  //               "account_number": "**********",
  //                 "amount": 50,
  //                   "transaction_pin": 1234,
  //                     "is_saved": true
  // }
  const handlePinSubmit = (value: string) => {
    postWithdrawSales(
      {
        dataDto: {
          transaction_pin: value,
          company,
          branch,
          amount: employeeBankDetails.withdrawAmount,
          account_number: employeeBankDetails.bankAccount,
          account_name: employeeBankDetails.bankAccountName,
          bank_code: employeeBankDetails.bankCode,
          bank_name: employeeBankDetails.bankName,
          // narration: employeeBankDetails.narration,
          withdrawal_type: withdrawal_type,
          is_saved: isBeneficiarySaved as boolean,
        },
        // companyid: companyid
      },
      {
        onSuccess: () => {
          openSuccessModal();
        },

        onError: error => {
          const errorMessage = formatAxiosErrorMessage(error as AxiosError);
          openErrorModalWithMessage(errorMessage as string);
        },
      }
    );
  };

  if (isPostWithdrawEmployeeLoading) {
    return (
      <div>
        <LoaderModal />
      </div>
    );
  }

  return (
    <Dialog open={isPinModalOpen} onOpenChange={setPinModalState}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="font-heading text-xl">{heading}</DialogTitle>
          <DialogClose className="ml-auto">Close</DialogClose>
        </DialogHeader>

        <DialogBody className="py-10 text-center">
          <form>
            <div className="flex flex-col justify-center">
              <h1 className="text-xl font-medium">Transaction Pin</h1>
              <p className="mx-auto mt-1 max-w-[226px] text-center text-[14px] text-[#6C727F] ">
                Enter transaction pin to process your transaction
              </p>
            </div>
            <div className="pin-input-container mx-auto mt-[27px] flex w-full justify-between gap-4 text-xl">
              <PinInput
                autoSelect={false}
                initialValue="o"
                inputFocusStyle={{ borderColor: '#4C1961' }}
                inputMode="number"
                inputStyle={{
                  marginRight: '10px',
                  background: '#F5F7F9',
                  borderRadius: '14px',
                  border: '#ffffff',
                  fontSize: '14px',
                }}
                length={4}
                style={{ padding: '10px', margin: 'auto' }}
                type="numeric"
                onComplete={handlePinSubmit}
              />
            </div>
          </form>
        </DialogBody>
      </DialogContent>

      <SuccessPayrollModal
        funText='"Enjoyyyyy!!!! 🙌🙌"'
        heading="Withdrawal Successful"
        isSuccessPayrollModalOpen={isSuccessModalOpen}
        setSuccessPayrollModalState={setSuccessModalState}
        subheading={`You have successfully withdrawn the sum of ₦${employeeBankDetails.withdrawAmount} from your wallet`}
      >
        <div className="flex gap-3 rounded-2xl bg-dash-light-bg px-8 py-6">
          <Button
            className="grow text-base"
            size="lg"
            variant="default"
            onClick={() => {
              location.reload();
            }}
          >
            Done
          </Button>
        </div>
      </SuccessPayrollModal>

      <ErrorModal
        isErrorModalOpen={isErrorModalOpen}
        setErrorModalState={setErrorModalState}
        subheading={
          errorModalMessage || 'Please check your inputs and try again.'
        }
      >
        <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
          <Button
            className="grow bg-red-950 text-base"
            size="lg"
            onClick={closeErrorModal}
          >
            Okay
          </Button>
        </div>
      </ErrorModal>
    </Dialog>
  );
}
