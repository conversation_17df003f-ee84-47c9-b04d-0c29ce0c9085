'use client'

import { useSpendManagementCompanies } from '../../../misc/api/getSpendManagementCompanies';
import { RecordExpenseDialog } from '../../../misc/components';
import { Companies } from '../../../misc/types';


export default function RecordExpense({
  searchParams,
}: {
  searchParams: { companyId: string };
}) {
  const { companyId } = searchParams;

  const _CALLBACK_URL = `/spend-management/expenses/record-expense?companyId=${companyId}`;



  const { data: companiesResponse } = useSpendManagementCompanies();

  const { results: companies } = companiesResponse || {} as Companies;

  return <RecordExpenseDialog
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    companies={companies} />;
}
