import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import LeaveTypes from '../leave-settings/LeaveType';
import LeavePolicy from '../leave-settings/LeavePolicy';

export type LeaveSettingsProps = {
    userId: string
    company_id: string
    company_name: string
}

function LeaveSettings({ company_id, company_name, userId }: LeaveSettingsProps) {

    return (
        <div className=" mx-auto px-4 py-8">
            <Tabs className="w-full" defaultValue="leave-types">
                <TabsList className="grid w-full grid-cols-2 mb-8">
                    <TabsTrigger value="leave-types">Leave Types</TabsTrigger>
                    <TabsTrigger value="leave-policy">Leave Policy</TabsTrigger>
                </TabsList>
                <TabsContent value="leave-types">
                    <LeaveTypes company_id={company_id} company_name={company_name} userId={userId} />
                </TabsContent>
                <TabsContent value="leave-policy">
                    <LeavePolicy company_id={company_id} company_name={company_name} userId={userId} />
                </TabsContent>
            </Tabs>
        </div>
    )
}

export default LeaveSettings

