import { useAuth } from '@/contexts/authentication';
import { authAxios, managementAxios, savingsAxios, setAxiosDefaultToken, tokenlessAuthAxios } from '@/lib/axios';

import { useMutation } from '@tanstack/react-query';

import type { AxiosResponse } from 'axios';

import { RefreshLoginDto } from '../types';
import { refreshTokenStorage, tokenStorage } from '../utils';

import { getUserDetails } from './index';

interface TokenResponse {
  access: string;
  refresh: string;
}

const refreshLogin = (refreshLogin: RefreshLoginDto): Promise<AxiosResponse<TokenResponse>> =>
  tokenlessAuthAxios.post('/user/login/refresh/', refreshLogin);

export const useRefreshLogin = () => {
  const { authDispatch } = useAuth();

  return useMutation(refreshLogin, {
    onSuccess: async ({ data }) => {
      const { access: token, refresh: tokenRefresh } = data;

      tokenStorage.setToken(token);
      refreshTokenStorage.setToken(tokenRefresh)
      setAxiosDefaultToken(token, authAxios);
      setAxiosDefaultToken(token, savingsAxios);
      setAxiosDefaultToken(token, managementAxios);

      const user = await getUserDetails();

      if (authDispatch) {
        authDispatch({ type: 'LOGIN', payload: user });

        authDispatch({ type: 'STOP_LOADING' });
      }
    },
  });
};
