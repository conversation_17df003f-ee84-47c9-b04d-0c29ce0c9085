"use client"

import React, { useState } from "react"
import Total from "@/components/icons/Subscription-icons/Total"
import Active from "@/components/icons/Subscription-icons/Active"
import Due from "@/components/icons/Subscription-icons/Due"
import {
    Button,
    Checkbox,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuPortal,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuTrigger,
} from "@/components/core"
import Subarrowdown from "@/components/icons/Subscription-icons/Subarrowdown"
import type { ColumnDef, PaginationState } from "@tanstack/react-table"

import { cn } from "@/utils/classNames"
// import { useBooleanStateControl } from "@/hooks"
import SubscriptionPlan from "../subscription/misc/components/Modals/SubscriptionPlan"
import PlanModuleDetails from "../subscription/misc/components/Modals/PlanModuleDetails"
import { useCompanySubscriptionTable } from "../subscription/misc/api/getCompanySubscriptionTable"
import { ModuleProp, useSubscriptionCards } from "../subscription/misc/api/getSubscriptionCards"
import { SmallSpinner } from "@/components/icons"
import { addCommasToNumber } from "@/utils/numbers"
import SubscriptionCards from "./Components/SubscriptionCards"
import { ExportSubscriptionData } from "./Components/ExportSubscriptionData"
import { DataTableExcelFormat } from "@/components/core/DataTableExcelFormat"
import { useSearchParams } from "next/navigation"
import moment from "moment"
import SubscriptionSummary from "../subscription/misc/components/Modals/SubscriptionSummary"
import { Plan, SubScriptionModulePropTypes } from "../subscription/misc/api/getSubscriptionPlan"
import GenerateInvoice from "../subscription/misc/components/Modals/GenerateInvoice"
import DebounceInput from "../instant-web/util/DebounceInput"



export default function Page() {

    const [selectedModuleFilter, setSelectedModuleFilter] = useState<string>("")
    const [selectedStatusFilter, setSelectedStatusFilter] = useState<boolean | null>(null)
    const [search, setSearch] = useState("")
    const [isRenewSubcription, setIsRenewSubcribtion] = useState(false)
    const [showDetailsModal, setShowDetailsModal] = useState(false)
    const [selectedModule, setSelectedModule] = useState<ModuleProp | null>(null)
    const [showSuccessModal, setShowSuccessModal] = useState(false)
    const [showUpgradePlan, setShowUpgradePlan] = useState(false)

    const searchData = useSearchParams()
    const company_id = searchData.get("companyId")
    const companyName = searchData.get("companyName")
    const fetchOptions = {
        module_filter: selectedModuleFilter as string,
        is_active: selectedStatusFilter as boolean,
        company_id: String(company_id)
    }

    const {
        data: subscriptionTable,
        isLoading: isSubscriptionTableLoading,
        isFetching: isSubscriptionTableFetching,
    } = useCompanySubscriptionTable(fetchOptions)
    const { data: SubscriptionCardsList, isLoading: isSubscriptionCardsLoading } = useSubscriptionCards(String(company_id))




    const [showSummaryModal, setShowSummaryModal] = useState(false)
    const [{ pageIndex, pageSize }, setPagination] = React.useState<PaginationState>({
        pageIndex: 1,
        pageSize: 100,
    })

    const [selectedPlans, setSelectedPlans] = useState<Map<number, Plan | null>>(new Map()); // Track selected plan for each module

    const [selectedModules, setSelectedModules] = useState<SubScriptionModulePropTypes[]>([]);

    const handleCheckboxChange = (value: boolean, module: ModuleProp) => {
        if (value) {
            // Add the module to selectedModules if checked
            const myPlan: SubScriptionModulePropTypes = {
                id: Number(module.module_id),
                name: module.module_name,
                code: module.module_code || "",
                description: "",
                is_premium: false,
                requires_subscription: false,
                created_at: module.start_date || "",
                selectedPlan: {
                    id: 2,
                    name: module.module_name,
                    description: "",
                    price: Number(module.pending_balance) || 0,
                    duration_months: String(module?.module_plan) === "Semi-Annual Subscription" ? 6 : 12,
                    is_active: true, // Assuming the selected plan is active, update accordingly
                    created_at: module.start_date || "", // Use 'created_at' from module or adjust
                    updated_at: module.start_date || "", // Adjust if needed
                },
            };

            setSelectedModules((prev) => [...prev, myPlan]); // Add to selected modules
        } else {
            // Remove the module from selectedModules if unchecked
            setSelectedModules((prev) =>
                prev.filter((m) => m.id !== module.module_id) // Remove based on module_id
            );
        }
    };

    // Function to check if the module is expired based on days_remaining
    const isModuleExpired = (module: number) => {
        return Number(module) > 0; // Check if days_remaining is 0 (expired)
    };

    const columns: ColumnDef<ModuleProp>[] = [
        {
            id: "select",
            header: ({ table }) => (
                <Checkbox
                    aria-label="Select all expired"
                    checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
                    onCheckedChange={(value) => {
                        // Get all rows in the table
                        const allRows = table.getRowModel().rows;

                        // Only toggle selection for expired rows (i.e., days_remaining === 0)
                        allRows.forEach((row) => {
                            const modules = row.original;

                            if (modules && modules?.days_remaining === 0) {
                                row.toggleSelected(!!value); // Toggle selection on expired rows only
                            }
                        });
                    }}
                />
            ),

            cell: ({ row }) => {
                const modules = row.original;
                const isExpired = modules ? isModuleExpired(Number(modules?.days_remaining)) : false; // Check if the module is expired

                // Disable checkbox if days_remaining is 0 (expired)
                const isDisabled = isExpired; // Disable if expired

                // Check if the current module is selected
                const isChecked = selectedModules.some((m) => m?.id === modules?.module_id);

                return (
                    <Checkbox
                        aria-label="Select row"
                        checked={isChecked && !isDisabled} // Don't check if disabled
                        disabled={isDisabled} // Disable if the module is expired
                        onCheckedChange={(value) => {
                            if (!isDisabled) {
                                handleCheckboxChange(Boolean(value), modules); // Pass the correct module object
                            }
                        }}
                    />
                );
            }
            ,
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: "module_name",
            header: "Module",
            cell: ({ row }) => {
                const modules = row.original.module_name;
                return <div>{module ? modules : "N/A"}</div>; // Display module name
            },
        },
        {
            accessorKey: "days_remaining",
            header: () => <p className="text-[#0B0A0B] font-medium">Days Remaining</p>,
            cell: ({ row }) => {
                const modules = row.original?.days_remaining;
                return <div>{module ? modules : "N/A"} day(s)</div>; // Display days remaining
            },
        },
        {
            accessorKey: "start_date",
            header: "Start Date",
            cell: ({ row }) => {
                const startDate = moment(row.original?.start_date);
                return <div>{startDate.isValid() ? startDate.format("DD MMMM, YYYY h:mm A") : "Invalid Date"}</div>;
            },
        },
        {
            accessorKey: "end_date",
            header: "Due Date",
            cell: ({ row }) => {
                const endDate = moment(row.original?.end_date);
                return <div>{endDate.isValid() ? endDate.format("DD MMMM, YYYY h:mm A") : "Invalid Date"}</div>;
            },
        },
        {
            accessorKey: "pending_balance",
            header: () => <p className="text-[#0B0A0B] font-medium">Amount</p>,
            cell: ({ row }) => {
                const modules = row.original;
                return <div>{module ? "₦" + addCommasToNumber(Number(modules?.pending_balance ?? 0)) : "0"}</div>;
            },
        },
        {
            accessorKey: "module_status",
            header: () => <p className="text-[#0B0A0B] font-medium">Status</p>,
            cell: ({ row }) => {
                const modules = row.original;

                const isActive = modules?.module_status === "active"
                return (
                    <span
                        className={cn(
                            "text-xs rounded-[0.625rem] capitalize px-3 h-[2rem] flex items-center justify-center",
                            isActive ? "text-[#099976]  bg-[#cdf8ee]" : "text-[#EF4444] bg-red-500/[0.15]"
                        )}
                    >
                        {modules?.module_status ?? ""}
                    </span>
                );
            },
        },
        {
            id: "actions",
            enableHiding: false,
            header: () => <p className="text-[#0B0A0B] font-medium">Action</p>,
            cell: ({ row }) => {
                const modules = row.original;
                const selectedModule = modules; // Get the first module
                return (
                    <button
                        className="bg-[#F2F5FF] rounded-lg text-[#032282] py-2 px-6 text-[0.75rem] font-normal leading-[1.0231rem]"
                        onClick={() => {
                            if (selectedModule) {
                                setSelectedModule(selectedModule); // Pass the correct module object
                                setShowDetailsModal(true); // Open the modal or page to show module details
                            }
                        }}
                    >
                        View details
                    </button>
                );
            },
        },
    ];


    const uniquePlanModules = Array.from(
        new Map(
            SubscriptionCardsList?.subscriptions?.flatMap((sub) => sub) // Ensure it's PlanModule[]
                ?.map((m) => [m.module_code, m]) // Use module_code as the key
        ).values()
    );


    // const pageCount = Math.ceil(filteredPlan.length / pageSize); // Use filteredPlan length
    // const displayedModules = filteredPlan.slice(pageIndex * pageSize, (pageIndex + 1) * pageSize); // Slice based on pagination state

    // console.log(displayedModules); // Log the displayed modules to verify the data being displayed

    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    const handleFilterSelect = (filter: string) => {
        setSelectedModuleFilter(filter);
        setIsDropdownOpen(false);  // Close dropdown when an option is selected
    };

    const handleStatusSelect = (status: boolean | null) => {
        setSelectedStatusFilter(status);
        setIsDropdownOpen(false);  // Close dropdown when an option is selected
    };


    return (
        <div className="">
            <div className="mt-6 bg-white py-8">
                <div className="px-8 bg-[#EBEFFB] py-[1.1875rem]">
                    <div className="sm:flex-row flex-wrap flex gap-4">
                        <SubscriptionCards
                            count={isSubscriptionCardsLoading ? <SmallSpinner color="blue" /> : SubscriptionCardsList?.total_subscriptions || 0}
                            image={<Total />}
                            plantype={"Total Module(s)"}
                        />

                        <SubscriptionCards
                            count={isSubscriptionCardsLoading ? <SmallSpinner color="blue" /> : SubscriptionCardsList?.active_subscriptions || 0}
                            image={<Active />}
                            plantype={"Active Module(s)"}
                        />

                        <SubscriptionCards
                            count={isSubscriptionCardsLoading ? <SmallSpinner color="blue" /> : SubscriptionCardsList?.expired_subscriptions || 0}
                            image={<Due />}
                            plantype={"Inactive Module(s)"}
                        />
                    </div>
                </div>
            </div>

            <div className="px-8 mt-[1.125rem]">
                <div className="bg-[#F3F5FC] px-8 py-3.5">
                    <div className="sm:flex-row flex-wrap flex justify-between gap-4">
                        <div className="flex sm:flex-row items-center flex-wrap gap-4 ">
                            <DebounceInput className="h-9 py-0" value={search ?? ""} onChange={(value) => setSearch(String(value))}

                            />

                            <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
                                <DropdownMenuTrigger className="">
                                    <p className="flex item-center bg-[#F0F5FF] px-[1.3125rem] h-9 justify-center items-center rounded-md text-[0.9rem] font-medium gap-3 font-sans text-[#032282] border-[0.3px] border-[#032282]">
                                        Filter <Subarrowdown />
                                    </p>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                    <DropdownMenuSub>
                                        <DropdownMenuSubTrigger>Plan Module</DropdownMenuSubTrigger>
                                        <DropdownMenuPortal>
                                            <DropdownMenuSubContent>
                                                <div
                                                    className={`rounded-xl hover:bg-[#F8FAFF] cursor-pointer px-6 text-[#07112D] text-[0.75rem] py-2 ${selectedModuleFilter === null ? "bg-[#E5EDFF]" : ""}`}
                                                    onClick={() => handleFilterSelect("")}
                                                >
                                                    All
                                                </div>
                                                {uniquePlanModules.length > 0 ? (
                                                    uniquePlanModules.map((module, idx) => (
                                                        <div
                                                            className="rounded-xl hover:bg-[#F8FAFF] cursor-pointer text-[#07112D] text-[0.75rem] px-6 py-2"
                                                            key={idx}
                                                            onClick={() => handleFilterSelect(module.module_name)}
                                                        >
                                                            {module.module_name}
                                                        </div>
                                                    ))
                                                ) : (
                                                    <div className="px-4 py-2 text-gray-500">No modules available</div>
                                                )}
                                            </DropdownMenuSubContent>
                                        </DropdownMenuPortal>
                                    </DropdownMenuSub>

                                    <DropdownMenuSub>
                                        <DropdownMenuSubTrigger>Status</DropdownMenuSubTrigger>
                                        <DropdownMenuPortal>
                                            <DropdownMenuSubContent className="py-2">
                                                <div
                                                    className={`rounded-xl hover:bg-[#F8FAFF] cursor-pointer text-[#07112D] text-[0.75rem] px-6 py-2 ${selectedStatusFilter === null ? "bg-[#E5EDFF]" : ""}`}
                                                    onClick={() => handleStatusSelect(null)}
                                                >
                                                    All
                                                </div>
                                                <div
                                                    className={`rounded-xl hover:bg-[#F8FAFF] cursor-pointer text-[#07112D] text-[0.75rem] py-2 px-6 ${selectedStatusFilter === true ? "bg-[#E5EDFF]" : ""}`}
                                                    onClick={() => handleStatusSelect(true)}
                                                >
                                                    Active
                                                </div>
                                                <div
                                                    className={`rounded-xl hover:bg-[#F8FAFF] cursor-pointer text-[#07112D] text-[0.75rem] px-6 ${selectedStatusFilter === false ? "bg-[#E5EDFF]" : ""}`}
                                                    onClick={() => handleStatusSelect(false)}
                                                >
                                                    Inactive
                                                </div>
                                            </DropdownMenuSubContent>
                                        </DropdownMenuPortal>
                                    </DropdownMenuSub>
                                </DropdownMenuContent>
                            </DropdownMenu>


                            <ExportSubscriptionData data={subscriptionTable?.subscriptions} fileName={"Subscription-History"} />
                        </div>

                        <div className={cn(selectedModules.length < 1 ? "hidden" : "block")}>
                            <Button className="h-9 px-[1.3125rem] rounded-10" onClick={() => {
                                setShowSummaryModal(true)
                                setIsRenewSubcribtion(true)
                            }}>
                                Renew plan
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            <div className="px-8">
                <DataTableExcelFormat
                    columns={columns}
                    isFetching={isSubscriptionTableFetching}
                    isLoading={isSubscriptionTableLoading}
                    pageCount={1}
                    pageIndex={pageIndex}
                    pageSize={pageSize}
                    rows={subscriptionTable?.subscriptions}
                    setPagination={setPagination}
                />
            </div>

            {showDetailsModal && <PlanModuleDetails
                selectedModule={selectedModule}
                setShowDetailsModal={setShowDetailsModal}
                setShowUpgradePlan={setShowUpgradePlan}
                showDetailsModal={showDetailsModal}
            />}

            {showSummaryModal &&
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                <SubscriptionSummary
                    // moduleTokenUnits={ }
                    selectedModules={selectedModules}
                    setSelectedModules={setSelectedModules}
                    setShowSuccessModal={setShowSuccessModal}
                    setShowSummaryModal={setShowSummaryModal}
                    showSubcriptionPlan={isRenewSubcription ? true : false}
                    showSummaryModal={showSummaryModal}
                />}



            {showSuccessModal && <GenerateInvoice
                company_id={company_id as string}
                companyName={companyName as string}
                description="An invoice has been sent to your registered email"
                heading='Invoice Sent Successfuly'
                setShowSuccessModal={setShowSuccessModal}
                showSuccessModal={showSuccessModal}
            />}
            {showUpgradePlan && <SubscriptionPlan
                selectedModule={selectedModule}
                selectedPlans={selectedPlans}
                setSelectedModule={setSelectedModule}
                setSelectedModules={setSelectedModules}
                setSelectedPlans={setSelectedPlans}
                setShowSummaryModal={setShowSummaryModal}
                setShowUpgradePlan={setShowUpgradePlan}
                showUpgradePlan={showUpgradePlan}

            />}


        </div>
    )
}

