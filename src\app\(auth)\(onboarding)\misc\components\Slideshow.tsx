"use client"

import { motion, AnimatePresence } from "framer-motion"
import * as React from "react"
import { LinkButton } from "@/components/core/LinkButton"
import { cn } from "@/utils/classNames"
import {
  Package,
  TrendingUp,
  Database,
  CreditCard,
  Users,
  FileText,
  Calculator,
  Repeat,
  Smartphone,
  Receipt,
  DollarSign,
} from "lucide-react"

const slideshowContent = [
  {
    category: "Stock Management",
    title: "Complete Inventory Control",
    description: "Streamline your stock operations with comprehensive inventory tracking and management tools.",
    features: [
      { name: "Stock Inventory", icon: Package, color: "text-blue-400" },
      { name: "Stock Management", icon: Database, color: "text-green-400" },
      { name: "Real-time Tracking", icon: TrendingUp, color: "text-purple-400" },
    ],
    gradient: "from-blue-600/20 to-purple-600/20",
    accentColor: "border-blue-400/30",
    patternColor: "text-blue-400/20",
  },
  {
    category: "Sales Operations",
    title: "Powerful Sales Solutions",
    description: "Boost your revenue with advanced sales tools and customer management features.",
    features: [
      { name: "Sales Transactions", icon: CreditCard, color: "text-emerald-400" },
      { name: "Customer Management", icon: Users, color: "text-cyan-400" },
      { name: "Price Lists", icon: FileText, color: "text-orange-400" },
    ],
    gradient: "from-emerald-600/20 to-cyan-600/20",
    accentColor: "border-emerald-400/30",
    patternColor: "text-emerald-400/20",
  },
  {
    category: "Financial Tools",
    title: "Smart Financial Management",
    description: "Handle all your financial operations with integrated lending and payment solutions.",
    features: [
      { name: "Business Loans", icon: DollarSign, color: "text-yellow-400" },
      { name: "Invoice Financing", icon: Receipt, color: "text-pink-400" },
      { name: "Stock Financing", icon: Calculator, color: "text-indigo-400" },
    ],
    gradient: "from-yellow-600/20 to-pink-600/20",
    accentColor: "border-yellow-400/30",
    patternColor: "text-yellow-400/20",
  },
  {
    category: "Point of Sale",
    title: "Advanced POS System",
    description: "Complete point-of-sale solution with invoicing, returns, and ledger management.",
    features: [
      { name: "POS Access", icon: Smartphone, color: "text-red-400" },
      { name: "Invoicing", icon: FileText, color: "text-teal-400" },
      { name: "Returns & Refunds", icon: Repeat, color: "text-violet-400" },
    ],
    gradient: "from-red-600/20 to-violet-600/20",
    accentColor: "border-red-400/30",
    patternColor: "text-red-400/20",
  },
]

// Floating Pattern Component
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const FloatingPattern = ({ currentSlide }: { currentSlide: any }) => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Geometric Grid Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" preserveAspectRatio="none" viewBox="0 0 100 100">
          <defs>
            <pattern height="10" id="grid" patternUnits="userSpaceOnUse" width="10">
              <path
                className={currentSlide.patternColor}
                d="M 10 0 L 0 0 0 10"
                fill="none"
                stroke="currentColor"
                strokeWidth="0.5"
              />
            </pattern>
          </defs>
          <rect fill="url(#grid)" height="100%" width="100%" />
        </svg>
      </div>

      {/* Floating Circles */}
      {[...Array(6)].map((_, i) => (
        <motion.div
          animate={{
            y: [0, -20, 0],
            x: [0, 10, 0],
            rotate: [0, 180, 360],
          }}
          className={cn("absolute rounded-full border border-current opacity-20", currentSlide.patternColor)}
          key={i}
          style={{
            width: `${20 + i * 15}px`,
            height: `${20 + i * 15}px`,
            left: `${10 + i * 15}%`,
            top: `${20 + i * 10}%`,
          }}
          transition={{
            duration: 8 + i * 2,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
            delay: i * 0.5,
          }}
        />
      ))}

      {/* Diagonal Lines Pattern */}
      <div className="absolute top-0 right-0 w-1/2 h-full opacity-5">
        <svg className="w-full h-full" preserveAspectRatio="none" viewBox="0 0 100 100">
          <defs>
            <pattern height="20" id="diagonals" patternUnits="userSpaceOnUse" width="20">
              <path className={currentSlide.patternColor} d="M0,20 L20,0" stroke="currentColor" strokeWidth="1" />
            </pattern>
          </defs>
          <rect fill="url(#diagonals)" height="100%" width="100%" />
        </svg>
      </div>

      {/* Animated Dots */}
      {[...Array(12)].map((_, i) => (
        <motion.div
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 0.7, 0.3],
          }}
          className={cn("absolute w-2 h-2 rounded-full opacity-30", `bg-current ${currentSlide.patternColor}`)}
          key={`dot-${i}`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          transition={{
            duration: 3 + Math.random() * 2,
            repeat: Number.POSITIVE_INFINITY,
            delay: Math.random() * 2,
          }}
        />
      ))}

      {/* Large Decorative Shape */}
      <motion.div
        animate={{
          rotate: [0, 360],
          scale: [1, 1.1, 1],
        }}
        className="absolute -top-20 -right-20 w-40 h-40 md:w-60 md:h-60 rounded-full border-2 border-current opacity-10"
        style={{ borderColor: "currentColor" }}
        transition={{
          duration: 20,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
      >
        <div className={cn("w-full h-full rounded-full border border-current opacity-50", currentSlide.patternColor)} />
      </motion.div>

      {/* Bottom Left Accent */}
      <motion.div
        animate={{
          rotate: [0, -360],
        }}
        className="absolute -bottom-10 -left-10 w-32 h-32 md:w-48 md:h-48"
        transition={{
          duration: 25,
          repeat: Number.POSITIVE_INFINITY,
          ease: "linear",
        }}
      >
        <svg className="w-full h-full" viewBox="0 0 100 100">
          <path
            className={cn(currentSlide.patternColor, "opacity-20")}
            d="M50,10 L90,50 L50,90 L10,50 Z"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
          />
          <path
            className={cn(currentSlide.patternColor, "opacity-15")}
            d="M50,20 L80,50 L50,80 L20,50 Z"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
          />
        </svg>
      </motion.div>
    </div>
  )
}

export function EnhancedSlideshow() {
  const [currentIndex, setCurrentIndex] = React.useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = React.useState(true)

  React.useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % slideshowContent.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const currentSlide = slideshowContent[currentIndex]

  return (
    <div
      className="relative mx-auto w-full max-w-[32.375rem] shrink-0 overflow-hidden text-white md:h-screen-small md:max-w-none md:basis-1/2 md:pb-0"
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
      onTouchEnd={() => setIsAutoPlaying(true)}
      onTouchStart={() => setIsAutoPlaying(false)}
    >
      {/* Header - Desktop Only */}
      <header className="absolute top-0 z-20 hidden w-full items-center justify-between px-6 py-11 md:flex xl:px-10">
        <motion.div animate={{ opacity: 1, y: 0 }} initial={{ opacity: 0, y: -20 }} transition={{ duration: 0.6 }}>
          <svg fill="none" height={54} viewBox="0 0 124 54" width={124} xmlns="http://www.w3.org/2000/svg">
            <path
              d="M6.976 30H.96V8.56h11.872c5.088 0 8.288 2.912 8.288 8s-3.2 8.032-8.288 8.032H6.976V30Zm5.184-16.032H6.976v5.216h5.184c2.112 0 2.976-.416 2.976-2.624 0-2.176-.864-2.592-2.976-2.592ZM27.613 30.32c-3.648 0-5.695-1.664-5.695-4.416 0-2.272 1.567-3.904 5.216-4.256l6.56-.64v-.32c0-1.632-.705-1.888-2.849-1.888-1.983 0-2.591.384-2.591 1.728v.128h-6.017v-.064c0-4.288 3.585-7.04 9.057-7.04 5.631 0 8.351 2.752 8.351 7.264V30h-5.632v-3.392h-.32c-.607 2.272-2.591 3.712-6.08 3.712Zm.352-4.8c0 .512.512.608 1.44.608 2.913 0 4.096-.352 4.256-1.792l-4.928.576c-.544.064-.768.256-.768.608Zm17.016 9.92h-2.912V30h4.672c.48 0 .864-.064 1.12-.192l-7.712-15.936h6.848l2.784 6.336 1.152 3.712h.416l1.056-3.776 2.4-6.272h6.72L53.877 30.64c-1.728 3.808-4.192 4.8-8.896 4.8ZM67.81 30h-5.632V8.56h6.016v9.472h.32c.48-2.688 2.272-4.48 6.08-4.48 4.736 0 7.392 3.136 7.392 8.384 0 5.216-2.72 8.384-7.648 8.384-3.776 0-5.696-1.472-6.208-4.672h-.32V30Zm.384-8.032c0 2.336 1.28 2.848 3.968 2.848 2.784 0 3.744-.736 3.744-2.88s-.96-2.912-3.744-2.912c-2.688 0-3.968.448-3.968 2.72v.224Zm24.318 8.352c-5.6 0-9.408-3.168-9.408-8.384 0-5.248 3.808-8.384 9.408-8.384s9.408 3.136 9.408 8.384c0 5.216-3.808 8.384-9.408 8.384Zm0-5.312c2.72 0 3.456-.736 3.456-3.072 0-2.336-.736-3.104-3.456-3.104-2.72 0-3.456.768-3.456 3.104 0 2.336.736 3.072 3.456 3.072ZM109.35 30h-7.264l6.016-7.808v-.32l-6.016-8h7.36l3.296 4.864h.32l3.168-4.864h7.264l-6.016 7.904v.32L123.494 30h-7.36l-3.296-4.736h-.32L109.35 30ZM1.488 50H.72v-8.04h.852v3.768h.06c.252-1.02 1.104-1.776 2.532-1.776 1.86 0 2.88 1.284 2.88 3.084s-1.02 3.084-2.952 3.084c-1.32 0-2.268-.684-2.544-1.872h-.06V50Zm.084-2.868c0 1.404.888 2.208 2.316 2.208 1.416 0 2.292-.588 2.292-2.304 0-1.716-.9-2.292-2.268-2.292-1.5 0-2.34.816-2.34 2.28v.108Zm7.3 4.908h-.745v-.78h.9c.6 0 .828-.18 1.056-.672l.288-.6-2.94-5.916h.936l1.74 3.528.66 1.452h.072l.636-1.464 1.644-3.516h.936l-3.216 6.756c-.432.912-.984 1.212-1.968 1.212ZM23.031 50h-5.953v-8.04h.852v7.26h5.1V50Zm1.813-6.684h-.852V41.96h.852v1.356Zm0 6.684h-.852v-5.928h.852V50Zm2.202 0h-.768v-8.04h.852v3.768h.06c.252-1.02 1.104-1.776 2.532-1.776 1.86 0 2.88 1.284 2.88 3.084s-1.02 3.084-2.952 3.084c-1.32 0-2.268-.684-2.544-1.872h-.06V50Zm.084-2.868c0 1.404.888 2.208 2.316 2.208 1.416 0 2.292-.588 2.292-2.304 0-1.716-.9-2.292-2.268-2.292-1.5 0-2.34.816-2.34 2.28v.108Zm9.339 2.988c-1.908 0-3.12-1.2-3.12-3.084 0-1.8 1.2-3.084 3.108-3.084 1.74 0 2.976 1.008 2.976 2.772 0 .216-.024.396-.06.552h-5.22c.048 1.332.732 2.136 2.304 2.136 1.392 0 2.04-.516 2.04-1.38v-.084h.852v.084c0 1.236-1.224 2.088-2.88 2.088Zm-.024-5.46c-1.536 0-2.232.792-2.292 2.088h4.476v-.18c0-1.248-.792-1.908-2.184-1.908Zm4.97 5.34h-.852v-5.928h.768v1.62h.06c.18-.948.852-1.74 2.088-1.74 1.368 0 1.968 1.008 1.968 2.112v.588h-.852v-.456c0-1.008-.42-1.5-1.452-1.5-1.2 0-1.728.756-1.728 2.088V50Zm8.655 0h-1.284c-1.176 0-1.932-.492-1.932-1.884v-3.312h-1.056v-.732h1.056v-1.416h.864v1.416h2.352v.732h-2.352v3.36c0 .828.408 1.056 1.272 1.056h1.08V50Zm1.714 2.04h-.744v-.78h.9c.6 0 .828-.18 1.056-.672l.288-.6-2.94-5.916h.936l1.74 3.528.66 1.452h.072l.636-1.464 1.644-3.516h.936l-3.216 6.756c-.432.912-.984 1.212-1.968 1.212ZM58.642 50h-.852v-8.04h3.696c1.74 0 2.964.996 2.964 2.712 0 1.728-1.224 2.724-2.964 2.724h-2.844V50Zm2.772-7.26h-2.772v3.876h2.772c1.416 0 2.172-.576 2.172-1.944 0-1.344-.756-1.932-2.172-1.932Zm5.602 7.38c-1.164 0-1.956-.552-1.956-1.512 0-.972.804-1.392 1.908-1.512l2.82-.312v-.456c0-1.152-.504-1.608-1.8-1.608-1.272 0-1.944.456-1.944 1.5v.048h-.852v-.048c0-1.248 1.032-2.268 2.856-2.268 1.8 0 2.568 1.032 2.568 2.34V50h-.768v-1.596h-.06c-.348 1.092-1.392 1.716-2.772 1.716Zm-1.104-1.572c0 .6.396.912 1.296.912 1.44 0 2.58-.636 2.58-2.064v-.048l-2.556.288c-.888.084-1.32.3-1.32.912Zm6.885 3.492h-.744v-.78h.9c.6 0 .828-.18 1.056-.672l.288-.6-2.94-5.916h.936l1.74 3.528.66 1.452h.072l.636-1.464 1.644-3.516h.936l-3.216 6.756c-.432.912-.984 1.212-1.968 1.212Z"
              fill="#fff"
            />
          </svg>
        </motion.div>

        <LinkButton
          className="hidden gap-2 px-5 py-2 text-base backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 transition-all duration-300"
          href="/"
          size="unstyled"
          variant="white"
        >
          <span>
            <svg fill="none" height={24} viewBox="0 0 24 24" width={24} xmlns="http://www.w3.org/2000/svg">
              <path
                d="m20.83 8.01-6.55-5.24C13 1.75 11 1.74 9.73 2.76L3.18 8.01c-.94.75-1.51 2.25-1.31 3.43l1.26 7.54C3.42 20.67 4.99 22 6.7 22h10.6c1.69 0 3.29-1.36 3.58-3.03l1.26-7.54c.18-1.17-.39-2.67-1.31-3.42Z"
                fill="#032282"
              />
              <path
                d="M12 18.75c-.41 0-.75-.34-.75-.75v-3c0-.41.34-.75.75-.75s.75.34.75.75v3c0 .41-.34.75-.75.75Z"
                fill="#fff"
              />
            </svg>
          </span>
          <span>Home</span>
        </LinkButton>
      </header>

      {/* Main Content - Mobile and Desktop */}
      <div className="relative h-full min-h-[400px] md:h-screen">
        <AnimatePresence mode="wait">
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className={cn(
              "absolute inset-0 flex flex-col justify-center p-4 md:p-6 xl:px-10",
              "bg-gradient-to-br",
              currentSlide.gradient,
            )}
            exit={{ opacity: 0, x: -100 }}
            initial={{ opacity: 0, x: 100 }}
            key={currentIndex}
            transition={{ duration: 0.8, ease: "easeInOut" }}
          >
            {/* Background Patterns */}
            <FloatingPattern currentSlide={currentSlide} />

            {/* Content Overlay */}
            <div className="relative z-10">
              {/* Category Badge */}
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className={cn(
                  "inline-flex w-fit items-center rounded-full border px-3 py-1.5 text-xs md:px-4 md:py-2 md:text-sm font-medium backdrop-blur-sm",
                  currentSlide.accentColor,
                  "bg-white/10",
                )}
                initial={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                {currentSlide.category}
              </motion.div>

              {/* Title */}
              <motion.h2
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 md:mt-6 font-clash text-2xl md:text-4xl xl:text-5xl font-bold leading-tight"
                initial={{ opacity: 0, y: 30 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {currentSlide.title}
              </motion.h2>

              {/* Description */}
              <motion.p
                animate={{ opacity: 1, y: 0 }}
                className="mt-3 md:mt-4 max-w-lg text-sm md:text-lg leading-relaxed text-white/90"
                initial={{ opacity: 0, y: 30 }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                {currentSlide.description}
              </motion.p>

              {/* Features Grid */}
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="mt-6 md:mt-8 grid grid-cols-1 gap-3 md:gap-4 sm:grid-cols-3"
                initial={{ opacity: 0, y: 40 }}
                transition={{ delay: 0.5, duration: 0.6 }}
              >
                {currentSlide.features.map((feature, index) => (
                  <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex items-center gap-2 md:gap-3 rounded-lg bg-white/10 p-3 md:p-4 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300"
                    initial={{ opacity: 0, scale: 0.8 }}
                    key={feature.name}
                    transition={{ delay: 0.6 + index * 0.1, duration: 0.4 }}
                  >
                    <feature.icon className={cn("h-5 w-5 md:h-6 md:w-6", feature.color)} />
                    <span className="font-medium text-sm md:text-base">{feature.name}</span>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Navigation Dots */}
        <div className="absolute bottom-16 md:bottom-8 left-1/2 z-10 flex -translate-x-1/2 gap-2">
          {slideshowContent.map((_, index) => (
            <button
              aria-label={`Go to slide ${index + 1}`}
              className={cn(
                "h-2 w-6 md:w-8 rounded-full transition-all duration-300",
                index === currentIndex ? "bg-white" : "bg-white/30 hover:bg-white/50",
              )}
              key={index}
              onClick={() => setCurrentIndex(index)}
            />
          ))}
        </div>

        {/* Progress Bar */}
        <div className="absolute bottom-0 left-0 h-1 w-full bg-white/20">
          <motion.div
            animate={{ width: "100%" }}
            className="h-full bg-white"
            initial={{ width: "0%" }}
            key={currentIndex}
            transition={{ duration: 4, ease: "linear" }}
          />
        </div>
      </div>
    </div>
  )
}
