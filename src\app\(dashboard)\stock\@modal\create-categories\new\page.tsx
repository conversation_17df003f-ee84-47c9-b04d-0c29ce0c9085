'use client'

import React from 'react';

import { Companies } from '@/app/(dashboard)/spend-management/misc/types';


import {
  CreateProductFromNewCategories,
  //   CreateProductModalx,
} from '../../../misc/components/modals';
import { useSpendManagementCompanies } from '@/app/(dashboard)/spend-management/misc/api/getSpendManagementCompanies';

const _CALLBACK_URL = '/stock';
const CreateCategories = ({
  searchParams,
}: {
  searchParams: {
    company: string;
  };
}) => {
  const companyId = searchParams.company;

  const { data: companiesResponse } = useSpendManagementCompanies()

  const { results: companies } = companiesResponse as Companies || [];

  return (
    <>
      <CreateProductFromNewCategories
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        companies={companies}
        companyId={companyId}
      />
    </>
  );
};

export default CreateCategories;
