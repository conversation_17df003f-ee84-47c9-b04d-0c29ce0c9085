export type CompanyListData = CompanyListEntity[];

export interface CompanyListEntity {
  id: string;
  company_name: string;
  account: string;
  default?: string;
  company_wallet_type: string;
  industry: string;
  cac_num: string;
  company_status: string;
  verification_status: string;
  size: number;
  registration_date: string;
  registration_submission_date: string;
  corporate_id: string;
  on_boarded_corporate_user: boolean;
  corporate_account_created: boolean;
  is_subscription: boolean;
  is_free_trial: boolean;
  is_paid_subscription: boolean;
  active_subscriptions: Array<{
    id: number;
    status: string;
    access_type: string;
    plan: string | null;
    overall_sub_days: string;
    created_at: string;
    pos_included: boolean;
    modules: Array<{
      module_subscription_id: number;
      module: {
        id: number;
        name: string;
        code: string;
        description: string;
        is_premium: boolean;
      };
      plan_name: string;
      plan_id: number;
      start_date: string;
      end_date: string;
      status: string;
      is_active: boolean;
      is_not_started: boolean;
    }>;
    has_active_modules: boolean;
    has_not_started_modules: boolean;
  }>;
  not_started_subscriptions: Array<{
    id: number;
    status: string;
    access_type: string;
    plan: string | null;
    overall_sub_days: string;
    created_at: string;
    pos_included: boolean;
    modules: Array<{
      module_subscription_id: number;
      module: {
        id: number;
        name: string;
        code: string;
        description: string;
        is_premium: boolean;
      };
      plan_name: string;
      plan_id: number;
      start_date: string;
      end_date: string;
      status: string;
      is_active: boolean;
      is_not_started: boolean;
    }>;
    has_active_modules: boolean;
    has_not_started_modules: boolean;
  }>;
}

export interface UserStatus {
  status: boolean;
}



export type Module = {
  id: number;
  name: string;
  code: string;
  description: string;
  is_premium: boolean;
  start_date: string;
  end_date: string;
  status: string;
  is_active: boolean;
};

export type SubscriptionInfo = {
  id: number;
  status: string;
  access_type: string;
  plan: string | null;
  overall_sub_days: string;
  created_at: string;
  pos_included: boolean;
  modules: Module[];
};