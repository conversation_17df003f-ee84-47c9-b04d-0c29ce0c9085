import { managementAxios } from "@/lib/axios"
import { useMutation } from "@tanstack/react-query"

// Define the request payload type

interface Subscriptionmodule {
    module_id: number;
    plan: number;

}


export interface SubscriptionModule {
    name: string;
    price: number;
    duration: number;
    total_price: number;
    plan?: number;
    module_id?: number;
}

export interface CreateInvoiceResponse {
    invoice_reference: string;
    amount_payable: number;
    subscription_modules: SubscriptionModule[];
    customer_name: string;
    start_date: string;
    expiry_date: string;
    payment_link: string;

}


interface Subscriptionmodule {
    module_id: number;
    plan: number;
    total_price?: number;
}
interface CreateInvoicePayload {
    company_id: string;
    subscription_modules: Subscriptionmodule[];
    is_token: boolean;
    token_units: number;
}

const createInvoice = async (payload: CreateInvoicePayload): Promise<CreateInvoiceResponse> => {
    console.log('Sending payload:', payload);

    const response = await managementAxios.post<CreateInvoiceResponse>(
        `/subscription_and_invoicing/create-invoice/`,
        payload
    )
    return response.data
}

// Custom hook for mutation
export const useCreateInvoice = () => {
    return useMutation({
        mutationFn: createInvoice,
    })
}
