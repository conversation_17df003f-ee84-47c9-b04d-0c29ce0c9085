import { managementAxios } from "@/lib/axios"
import { useMutation } from "@tanstack/react-query"

// Define the request payload type

interface Subscriptionmodule {
    module_id: number;
    plan: number;

}


export interface SubscriptionModule {
    name: string;
    price: number;
    duration: number;
    total_price: number;
    plan?: number;
    module_id?: number;
}

export interface CreateInvoiceResponse {
    invoice_reference: string;
    amount_payable: number;
    subscription_modules: SubscriptionModule[];
    customer_name: string;
    start_date: string;
    expiry_date: string;
    payment_link: string;

}


interface Subscriptionmodule {
    module_id: number;
    plan: number;
    total_price?: number;
}
interface prop {
    company_id: string,
    requestData: Subscriptionmodule[]
}




const createInvoice = async ({ company_id, requestData }: prop): Promise<CreateInvoiceResponse> => {
    // console.log(company_id, requestData);

    const response = await managementAxios.post<CreateInvoiceResponse>(
        `/subscription_and_invoicing/create-invoice/`, {
        company_id,
        subscription_modules: requestData
    },)
    return response.data
}

// Custom hook for mutation
export const useCreateInvoice = () => {
    return useMutation({
        mutationFn: createInvoice,
    })
}
