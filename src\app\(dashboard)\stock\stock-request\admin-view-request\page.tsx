/* eslint-disable @typescript-eslint/no-use-before-define */

'use client';

import { useSearchParams } from 'next/navigation';
import React from 'react';

import { LoaderModal, Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/core';
import { Spinner } from '@/icons/core';

import { useGetSingleStockRequestDetails } from '../../misc/api';
import {
  SingleStockRequestDashboardStats,
  SingleStockRequestTab,
  ViewBranchesDialog,
} from '../../misc/components';
import { SingleStockRequestsDetails } from '../../misc/types';
import { useCompaniesList } from '@/app/(dashboard)/instant-web/misc/api';

const Page = () => {
  const searchParams = useSearchParams();

  const branchId = searchParams.get('branch') as string;
  const companyId = searchParams.get('company') as string;
  const requestId = searchParams.get('request') as string;

  const fetchOptions = {
    branchId,
    companyId,
    requestId,
  };

  const { data: companies, isLoading: isCompanyLoading } = useCompaniesList();

  const { data: requestDetails, isLoading: isRequestLoading } =
    useGetSingleStockRequestDetails(fetchOptions);

  const allRequestDetails = requestDetails?.data?.requests || [];

  const getUniqueCategories = (requests: SingleStockRequestsDetails[]) => {
    const uniqueCategories: SingleStockRequestsDetails[] = [];

    for (const request of requests) {
      const foundCategory = uniqueCategories.find(
        cat => cat.category_id === request.category_id
      );
      if (!foundCategory) {
        uniqueCategories.push(request);
      }
    }

    //for all section
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    uniqueCategories.push({
      category_id: '',
      id: '',
      request_id: '',
      category: 'All Products',
      item: '',
      quantity_requested: 0,
      quantity_approved: 0,
      available_stock: 0,
      status: '',
      stock_item_has_ids: false,
      stock_item: '',
      created_at: '',
    });

    return uniqueCategories;
  };

  const allCategories = getUniqueCategories(allRequestDetails);

  return (
    <>
      <LoaderModal isOpen={isCompanyLoading} />
      {branchId ?
        <div className="md:px-7 md:py-3 lg:px-11">
          <SingleStockRequestDashboardStats />

          <div className="stocks__table">
            {isRequestLoading ? (
              <div className="mt-4 flex justify-center text-center">
                <Spinner className="inline-flex" />
              </div>
            ) : (
              <Tabs className="w-full" defaultValue={'0'}>
                <TabsList className="my-4 grid grid-cols-1 gap-4 border-none bg-white md:grid-cols-2 lg:grid-cols-5">
                  {allCategories?.map((item, index) => {
                    return (
                      <TabsTrigger
                        className="rounded-none border-b-2 border-b-transparent pt-7 text-sm data-[state=active]:border-b-solid-underline data-[state=active]:shadow-none"
                        key={index + item?.category_id}
                        value={index.toString()}
                      >
                        <p className="truncate">{item.category}</p>
                      </TabsTrigger>
                    );
                  })}
                </TabsList>
                <div className="mt-[10px] w-full "></div>

                {allCategories?.map((item, index) => {
                  return (
                    <TabsContent
                      className="mt-2 h-screen w-full rounded-sm bg-white"
                      key={index + item?.category_id}
                      value={index.toString()}
                    >
                      {allCategories.length - 1 !== index ? (
                        <SingleStockRequestTab categoryId={item?.category_id} />
                      ) : (
                        <SingleStockRequestTab
                          allStockRequests={allRequestDetails}
                        />
                      )}
                    </TabsContent>
                  );
                })}

                {/* <TabsContent
              className="mt-2 h-screen w-full rounded-sm bg-white"
              value={(allCategories.length - 1).toString()}
            >
              <SingleStockRequestTab allStockRequests={allRequestDetails} />
            </TabsContent> */}
              </Tabs>
            )}
          </div>
        </div>
        :
        <ViewBranchesDialog company={companyId}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          companyList={companies?.results} link={'/stock/branch-details'} />
      }
    </>
  );
};

export default Page;
